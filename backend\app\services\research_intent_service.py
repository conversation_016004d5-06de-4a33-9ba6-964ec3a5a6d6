"""
研究意图关键词整合服务
基于COMPREHENSIVE_SOLUTION_FRAMEWORK_INTEGRATION.md的设计
智能分析用户研究意图，生成精准的搜索关键词和一键搜索链接
"""
from typing import Dict, List, Any, Optional
import urllib.parse
import logging
from datetime import datetime

from app.services.ai_service import AIService

logger = logging.getLogger(__name__)


class ResearchIntentService:
    """研究意图关键词整合服务"""
    
    def __init__(self):
        self.ai_service = AIService()
        
        # 单细胞测序核心技术关键词库
        self.core_tech_keywords = {
            "platforms": [
                "10x Genomics", "Smart-seq", "Drop-seq", "inDrop", "BD Rhapsody", 
                "MARS-seq", "CEL-seq", "STRT-seq", "Fluidigm C1"
            ],
            "analysis_methods": [
                "scRNA-seq", "single cell RNA sequencing", "transcriptomics",
                "cell clustering", "trajectory analysis", "pseudotime",
                "differential expression", "cell type annotation"
            ],
            "bioinformatics_tools": [
                "Seurat", "Scanpy", "Cell Ranger", "Monocle", "SingleR",
                "CellTypist", "scType", "Azimuth", "Symphony", "SCENIC"
            ]
        }
        
        # 研究领域特异性关键词
        self.domain_specific_keywords = {
            "cancer": {
                "core": ["tumor microenvironment", "cancer", "oncology", "metastasis", "malignant"],
                "cellular": ["cancer cell", "tumor cell", "circulating tumor cell", "CAR-T"],
                "mechanisms": ["drug resistance", "immune escape", "EMT", "stemness"],
                "applications": ["immunotherapy", "precision medicine", "liquid biopsy"]
            },
            "immunology": {
                "core": ["immune", "immunology", "T cell", "B cell", "NK cell"],
                "cellular": ["PBMC", "immune cell", "lymphocyte", "macrophage", "dendritic cell"],
                "mechanisms": ["immune response", "activation", "exhaustion", "memory"],
                "applications": ["vaccine", "autoimmune", "transplantation", "infection"]
            },
            "neuroscience": {
                "core": ["brain", "neuron", "neural", "neuronal", "CNS"],
                "cellular": ["neuron", "astrocyte", "microglia", "oligodendrocyte"],
                "mechanisms": ["synaptic", "connectivity", "plasticity", "development"],
                "applications": ["Alzheimer", "Parkinson", "autism", "depression"]
            },
            "development": {
                "core": ["development", "developmental", "embryo", "organogenesis"],
                "cellular": ["stem cell", "progenitor", "differentiation", "lineage"],
                "mechanisms": ["cell fate", "trajectory", "reprogramming", "regeneration"],
                "applications": ["organoid", "tissue engineering", "regenerative medicine"]
            }
        }
        
        # 搜索平台配置
        self.search_platforms = {
            "pubmed": {
                "base_url": "https://pubmed.ncbi.nlm.nih.gov/?term=",
                "query_format": self._format_pubmed_query,
                "name": "PubMed",
                "description": "生物医学文献数据库",
                "icon": "🏥"
            },
            "google_scholar": {
                "base_url": "https://scholar.google.com/scholar?q=",
                "query_format": self._format_google_scholar_query,
                "name": "Google Scholar", 
                "description": "学术搜索引擎",
                "icon": "🎓"
            },
            "semantic_scholar": {
                "base_url": "https://www.semanticscholar.org/search?q=",
                "query_format": self._format_semantic_scholar_query,
                "name": "Semantic Scholar",
                "description": "AI驱动的学术搜索",
                "icon": "🧠"
            },
            "biorxiv": {
                "base_url": "https://www.biorxiv.org/search/",
                "query_format": self._format_biorxiv_query,
                "name": "bioRxiv",
                "description": "生物学预印本",
                "icon": "🧬"
            }
        }
    
    async def analyze_research_intent_and_generate_keywords(
        self,
        requirements: Dict[str, Any],
        user_message: str = ""
    ) -> Dict[str, Any]:
        """
        分析研究意图并生成整合的关键词
        避免多层级复杂性，直接生成可用的搜索查询
        """
        try:
            logger.info("🔍 开始研究意图分析和关键词生成")
            
            # 1. AI分析用户研究意图
            intent_analysis = await self._analyze_user_research_intent(requirements, user_message)
            
            # 2. 基于意图整合生成精准关键词
            integrated_keywords = self._generate_integrated_keywords(
                requirements, intent_analysis
            )
            
            # 3. 生成精准搜索链接
            precision_search_links = self._generate_precision_search_links(
                integrated_keywords, intent_analysis
            )
            
            # 4. 创建研究焦点展示
            research_focus_display = self._create_research_focus_display(
                integrated_keywords, intent_analysis
            )
            
            return {
                "intent_analysis": intent_analysis,
                "integrated_keywords": integrated_keywords,
                "precision_search_links": precision_search_links,
                "research_focus_display": research_focus_display,
                "generation_metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "method": "ai_intent_integration",
                    "confidence_score": intent_analysis.get("confidence", 0.85)
                }
            }
            
        except Exception as e:
            logger.error(f"研究意图分析失败: {e}")
            return self._get_fallback_keywords(requirements)
    
    async def _analyze_user_research_intent(
        self,
        requirements: Dict[str, Any],
        user_message: str
    ) -> Dict[str, Any]:
        """使用AI深度分析用户的研究意图，生成整合的搜索策略"""
        
        prompt = f"""
作为CellForge AI的单细胞测序专家，请深入分析用户的研究意图，为其生成整合的、精准的文献搜索策略。

用户详细需求：
- 物种类型：{requirements.get('speciesType', '未指定')}
- 实验类型：{requirements.get('experimentType', '未指定')}
- 研究目标：{requirements.get('researchGoal', '未指定')}
- 样本类型：{requirements.get('sampleType', '未指定')}
- 细胞数量：{requirements.get('cellCount', '未指定')}
- 样本状态：{requirements.get('sampleCondition', '未指定')}
- 预算范围：{requirements.get('budget', '未指定')}
- 项目周期：{requirements.get('timeline', '未指定')}

用户具体描述：{user_message}

请基于以上信息进行深度分析，返回JSON格式结果：

{{
  "research_domain": "主要研究领域 (cancer/immunology/neuroscience/development/general)",
  "sample_context": "样本类型的专业上下文描述，包括物种特异性和样本特点（使用英文专业术语）",
  "analysis_objectives": ["具体的分析目标1（英文）", "具体的分析目标2（英文）", "具体的分析目标3（英文）"],
  "technical_focus": "主要技术重点描述（使用英文专业术语）",
  "literature_priorities": ["文献搜索优先级1（英文）", "文献搜索优先级2（英文）"],
  "integrated_search_strategy": "基于用户需求整合的核心搜索策略描述（使用英文关键词）",
  "key_research_questions": ["核心研究问题1（英文）", "核心研究问题2（英文）"],
  "confidence": 0.85
}}

**重要要求**：
1. 所有关键词和专业术语必须使用英文，因为国际期刊和数据库都使用英文检索
2. 分析结果要专业、准确，能够真正反映用户的研究需求和技术背景
3. 确保生成的关键词适合PubMed、Google Scholar等英文学术数据库搜索
        """
        
        try:
            ai_response = await self.ai_service.generate_response(
                message=prompt,
                context={},
                conversation_type="intent_analysis"
            )
            
            # 解析AI响应
            intent_data = self._parse_intent_analysis(ai_response.content)
            
            return intent_data
            
        except Exception as e:
            logger.warning(f"AI意图分析失败，使用规则分析: {e}")
            return self._rule_based_intent_analysis(requirements, user_message)
    
    def _parse_intent_analysis(self, ai_content: str) -> Dict[str, Any]:
        """解析AI的意图分析结果"""
        try:
            # 尝试提取JSON
            import json
            import re
            
            # 查找JSON块
            json_match = re.search(r'\{.*\}', ai_content, re.DOTALL)
            if json_match:
                intent_data = json.loads(json_match.group())
                return intent_data
            else:
                # 如果没有JSON，使用文本解析
                return self._text_parse_intent(ai_content)
                
        except Exception as e:
            logger.warning(f"AI结果解析失败: {e}")
            return {
                "research_domain": "general",
                "sample_context": "通用单细胞样本",
                "analysis_objectives": ["细胞类型鉴定", "表达分析"],
                "technical_focus": "单细胞转录组分析",
                "literature_priorities": ["方法学", "技术比较"],
                "confidence": 0.7
            }
    
    def _text_parse_intent(self, ai_content: str) -> Dict[str, Any]:
        """从文本中解析研究意图"""
        content_lower = ai_content.lower()
        
        # 确定研究领域
        research_domain = "general"
        if any(word in content_lower for word in ["cancer", "tumor", "oncology"]):
            research_domain = "cancer"
        elif any(word in content_lower for word in ["immune", "pbmc", "t cell", "b cell"]):
            research_domain = "immunology"
        elif any(word in content_lower for word in ["brain", "neuron", "neural"]):
            research_domain = "neuroscience"
        elif any(word in content_lower for word in ["development", "stem cell", "embryo"]):
            research_domain = "development"
        
        return {
            "research_domain": research_domain,
            "sample_context": "基于AI分析的样本上下文",
            "analysis_objectives": ["细胞类型分析", "功能研究"],
            "technical_focus": "单细胞转录组分析",
            "literature_priorities": ["最新研究", "方法学"],
            "confidence": 0.8
        }
    
    def _rule_based_intent_analysis(
        self,
        requirements: Dict[str, Any],
        user_message: str
    ) -> Dict[str, Any]:
        """基于规则的意图分析（AI分析失败时的后备方案）"""
        
        research_goal = requirements.get('researchGoal', '').lower()
        sample_type = requirements.get('sampleType', '').lower()
        species_type = requirements.get('speciesType', '').lower()
        combined_text = (research_goal + " " + sample_type + " " + user_message).lower()
        
        # 确定研究领域
        research_domain = "general"
        if any(word in combined_text for word in ["cancer", "tumor", "肿瘤", "癌症"]):
            research_domain = "cancer"
        elif any(word in combined_text for word in ["immune", "pbmc", "免疫", "t细胞", "b细胞", "淋巴"]):
            research_domain = "immunology"
        elif any(word in combined_text for word in ["brain", "neuron", "神经", "大脑"]):
            research_domain = "neuroscience"
        elif any(word in combined_text for word in ["development", "stem", "发育", "干细胞"]):
            research_domain = "development"
        
        # 基于样本类型进一步细化研究领域
        if "pbmc" in sample_type or "blood" in sample_type or "血" in sample_type:
            research_domain = "immunology"
        elif "tumor" in sample_type or "cancer" in sample_type:
            research_domain = "cancer"
        
        # 确定技术重点和分析目标
        technical_focus = "单细胞类型鉴定"
        analysis_objectives = ["Single cell type identification"]
        
        if "trajectory" in combined_text or "轨迹" in combined_text:
            technical_focus = "发育轨迹分析"
            analysis_objectives = ["Developmental trajectory analysis", "Pseudotime analysis"]
        elif "differential" in combined_text or "差异表达" in combined_text:
            technical_focus = "差异表达分析"
            analysis_objectives = ["Differential gene expression analysis", "Biomarker identification"]
        elif "spatial" in combined_text or "空间" in combined_text:
            technical_focus = "空间转录组分析"
            analysis_objectives = ["Spatial transcriptomics analysis", "Tissue architecture"]
        elif "function" in combined_text or "功能" in combined_text:
            analysis_objectives = ["Cell function analysis", "Pathway enrichment analysis"]
        
        # 特殊处理免疫研究
        if research_domain == "immunology":
            analysis_objectives = ["Immune cell type identification", "T cell activation analysis", "Immune response profiling"]
        
        return {
            "research_domain": research_domain,
            "sample_context": f"{species_type} {sample_type}样本的{research_domain}研究",
            "analysis_objectives": analysis_objectives,
            "technical_focus": technical_focus,
            "literature_priorities": ["领域特异性最新研究", "技术方法学文献"],
            "integrated_search_strategy": f"基于{research_domain}领域的{technical_focus}整合搜索策略",
            "key_research_questions": [f"如何在{sample_type}中进行{technical_focus}", f"{research_domain}研究的最佳实践"],
            "confidence": 0.75
        }
    
    def _generate_integrated_keywords(
        self,
        requirements: Dict[str, Any],
        intent_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """基于研究意图整合生成精准的搜索关键词 - 避免分散，生成整合查询"""
        
        research_domain = intent_analysis.get("research_domain", "general")
        sample_context = intent_analysis.get("sample_context", "")
        technical_focus = intent_analysis.get("technical_focus", "")
        integrated_strategy = intent_analysis.get("integrated_search_strategy", "")
        analysis_objectives = intent_analysis.get("analysis_objectives", [])
        
        # 提取核心信息构建整合查询
        species_type = requirements.get('speciesType', '')
        sample_type = requirements.get('sampleType', '')
        research_goal = requirements.get('researchGoal', '')
        
        # 构建主要的整合查询 - 体现文档要求的"整合"概念，使用英文关键词
        species_clean = self._translate_to_english_term(species_type.split('(')[0].strip() if '(' in species_type else species_type)
        sample_clean = self._translate_to_english_term(sample_type)
        goal_clean = self._translate_to_english_term(research_goal)

        # 生成整合的主查询，包含所有关键信息（全英文）
        if research_domain == "immunology":
            primary_query = f"{goal_clean} {sample_clean} single cell RNA sequencing {species_clean} immune cell analysis"
        elif research_domain == "cancer":
            primary_query = f"{goal_clean} {sample_clean} scRNA-seq {species_clean} tumor microenvironment"
        elif research_domain == "neuroscience":
            primary_query = f"{goal_clean} {sample_clean} single cell transcriptomics {species_clean} neural"
        elif research_domain == "development":
            primary_query = f"{goal_clean} {sample_clean} scRNA-seq {species_clean} developmental trajectory"
        else:
            primary_query = f"{goal_clean} {sample_clean} single cell RNA sequencing {species_clean}"

        # 清理和优化主查询，确保英文格式
        primary_query = " ".join(primary_query.split()).lower()  # 移除多余空格并转为小写
        
        # 生成备选的整合查询（仍然是整合的，不是分散的关键词，全英文）
        alternative_queries = []

        # 基于分析目标的整合查询
        if analysis_objectives:
            obj_clean = self._translate_to_english_term(analysis_objectives[0]) if analysis_objectives[0] else ""
            obj_query = f"{obj_clean} {sample_clean} scRNA-seq".strip()
            if obj_query:
                alternative_queries.append(obj_query.lower())

        # 基于技术重点的整合查询
        if technical_focus:
            tech_clean = self._translate_to_english_term(technical_focus)
            tech_query = f"{tech_clean} {species_clean} single cell analysis".strip()
            if tech_query:
                alternative_queries.append(tech_query.lower())

        # 添加通用的高质量查询
        alternative_queries.append(f"{sample_clean} single cell sequencing protocol")
        alternative_queries.append(f"{species_clean} scRNA-seq analysis workflow")

        # 研究焦点的整合表述（英文）
        if analysis_objectives:
            eng_objectives = [self._translate_to_english_term(obj) for obj in analysis_objectives[:2] if obj]
            research_focus = f"{research_domain} + {' + '.join(eng_objectives)}" if eng_objectives else f"{research_domain} + single cell analysis"
        else:
            tech_clean = self._translate_to_english_term(technical_focus) if technical_focus else "single cell analysis"
            research_focus = f"{research_domain} + {tech_clean}"
        
        return {
            "primary_query": primary_query,
            "alternative_queries": list(filter(None, alternative_queries[:3])),  # 最多3个备选整合查询，过滤空值
            "research_focus": research_focus,
            "integrated_search_strategy": integrated_strategy,
            "analysis_objectives": analysis_objectives,
            "query_optimization_notes": f"AI-generated integrated search strategy focused on {research_domain} research with {goal_clean} objectives"
        }
    
    def _extract_suggested_terms(
        self,
        requirements: Dict[str, Any],
        intent_analysis: Dict[str, Any]
    ) -> List[str]:
        """提取建议的补充搜索词汇（英文）"""

        terms = []
        research_domain = intent_analysis.get("research_domain", "general")

        # 技术平台相关（已经是英文）
        terms.extend(self.core_tech_keywords["platforms"][:3])

        # 分析方法相关（已经是英文）
        terms.extend(self.core_tech_keywords["analysis_methods"][:3])

        # 领域特异性词汇（已经是英文）
        if research_domain in self.domain_specific_keywords:
            domain_terms = self.domain_specific_keywords[research_domain]
            terms.extend(domain_terms.get("mechanisms", [])[:2])

        # 当前年份（获取最新文献）
        current_year = datetime.now().year
        terms.append(str(current_year))
        terms.append(f"{current_year-1}-{current_year}")

        # 添加通用的英文搜索增强词
        terms.extend(["protocol", "methods", "analysis", "workflow", "recent advances"])

        return terms[:10]  # 最多10个建议词汇

    def _translate_to_english_term(self, chinese_term: str) -> str:
        """将中文术语翻译为英文专业术语"""
        if not chinese_term:
            return ""

        # 常见中英文术语对照表
        translation_dict = {
            # 物种类型
            "人": "human", "小鼠": "mouse", "大鼠": "rat", "猴": "monkey", "斑马鱼": "zebrafish",
            "果蝇": "drosophila", "线虫": "c elegans", "酵母": "yeast",

            # 样本类型
            "外周血": "PBMC", "骨髓": "bone marrow", "脾脏": "spleen", "淋巴结": "lymph node",
            "肿瘤组织": "tumor tissue", "正常组织": "normal tissue", "血液": "blood",
            "脑组织": "brain tissue", "肝脏": "liver", "肺": "lung", "心脏": "heart",
            "肾脏": "kidney", "皮肤": "skin", "肌肉": "muscle", "脂肪": "adipose tissue",

            # 研究目标
            "细胞分型": "cell type identification", "发育轨迹": "developmental trajectory",
            "差异表达": "differential expression", "功能分析": "functional analysis",
            "信号通路": "signaling pathway", "免疫分析": "immune analysis",
            "肿瘤研究": "cancer research", "神经研究": "neuroscience research",
            "干细胞研究": "stem cell research", "疾病机制": "disease mechanism",

            # 实验类型
            "单细胞转录组": "single cell RNA sequencing", "单细胞基因组": "single cell genomics",
            "单细胞蛋白组": "single cell proteomics", "多组学": "multiomics",
            "空间转录组": "spatial transcriptomics", "ATAC-seq": "ATAC-seq",

            # 技术平台
            "10x平台": "10x genomics", "智能测序": "smart-seq", "液滴测序": "droplet sequencing"
        }

        # 先尝试直接匹配
        term_lower = chinese_term.lower().strip()
        if term_lower in translation_dict:
            return translation_dict[term_lower]

        # 部分匹配
        for chinese, english in translation_dict.items():
            if chinese in chinese_term:
                return english

        # 如果已经是英文，直接返回
        if chinese_term.encode('utf-8').isascii():
            return chinese_term.lower()

        # 默认返回原文（可能需要手动处理）
        return chinese_term
    
    def _generate_precision_search_links(
        self,
        integrated_keywords: Dict[str, Any],
        intent_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成精准的一键搜索链接"""
        
        primary_query = integrated_keywords["primary_query"]
        research_focus = integrated_keywords["research_focus"]
        
        # 为不同平台生成优化的搜索链接
        precision_links = {}
        
        for platform_key, platform_config in self.search_platforms.items():
            try:
                # 使用平台特定的查询格式化
                formatted_query = platform_config["query_format"](primary_query, research_focus)
                search_url = platform_config["base_url"] + urllib.parse.quote(formatted_query)
                
                precision_links[platform_key] = {
                    "url": search_url,
                    "name": platform_config["name"],
                    "description": platform_config["description"],
                    "icon": platform_config["icon"],
                    "query_used": formatted_query
                }
                
            except Exception as e:
                logger.warning(f"生成{platform_key}链接失败: {e}")
                # 使用默认格式
                default_url = platform_config["base_url"] + urllib.parse.quote(primary_query)
                precision_links[platform_key] = {
                    "url": default_url,
                    "name": platform_config["name"],
                    "description": platform_config["description"],
                    "icon": platform_config["icon"],
                    "query_used": primary_query
                }
        
        return {
            "primary_links": precision_links,
            "research_summary": f"基于您的研究意图：{research_focus}，为您精准匹配相关文献",
            "search_strategy": "多平台综合搜索策略，覆盖最新研究和经典文献",
            "alternative_queries": integrated_keywords.get("alternative_queries", [])
        }
    
    def _create_research_focus_display(
        self,
        integrated_keywords: Dict[str, Any],
        intent_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """创建研究焦点的展示信息"""
        
        research_domain = intent_analysis.get("research_domain", "general")
        technical_focus = intent_analysis.get("technical_focus", "")
        primary_query = integrated_keywords["primary_query"]
        
        # 研究焦点的友好显示
        focus_display = {
            "main_focus": integrated_keywords["research_focus"],
            "primary_search_query": primary_query,
            "domain_description": self._get_domain_description(research_domain),
            "technical_approach": technical_focus,
            "expected_results": self._get_expected_results(research_domain, technical_focus),
            "search_confidence": intent_analysis.get("confidence", 0.8)
        }
        
        return focus_display
    
    def _get_domain_description(self, research_domain: str) -> str:
        """获取研究领域的描述"""
        descriptions = {
            "cancer": "肿瘤研究：聚焦于癌症生物学、肿瘤微环境和治疗靶点发现",
            "immunology": "免疫学研究：专注于免疫细胞功能、免疫反应机制和免疫治疗",
            "neuroscience": "神经科学研究：探索神经系统发育、功能和疾病机制",
            "development": "发育生物学研究：研究细胞分化、组织发育和再生医学",
            "general": "综合性单细胞研究：涵盖细胞异质性分析和功能研究"
        }
        return descriptions.get(research_domain, descriptions["general"])
    
    def _get_expected_results(self, research_domain: str, technical_focus: str) -> List[str]:
        """获取预期的研究结果类型"""
        base_results = [
            "高质量的单细胞转录组数据",
            "细胞类型和亚群的精确鉴定",
            "关键生物标志物的发现"
        ]
        
        domain_specific = {
            "cancer": [
                "肿瘤细胞异质性分析",
                "潜在治疗靶点识别",
                "耐药机制洞察"
            ],
            "immunology": [
                "免疫细胞功能状态评估",
                "免疫反应动态监测", 
                "免疫治疗靶点发现"
            ],
            "neuroscience": [
                "神经细胞类型图谱",
                "神经发育轨迹分析",
                "疾病相关基因网络"
            ],
            "development": [
                "细胞分化轨迹重建",
                "发育关键调控因子",
                "组织发育时空图谱"
            ]
        }
        
        specific_results = domain_specific.get(research_domain, [])
        return base_results + specific_results[:3]
    
    # 平台特异性查询格式化方法
    def _format_pubmed_query(self, primary_query: str, research_focus: str) -> str:
        """PubMed查询格式优化"""
        # PubMed支持MeSH terms和字段搜索
        formatted = f'("{primary_query}"[Title/Abstract] OR "{research_focus}"[MeSH Terms])'
        return formatted
    
    def _format_google_scholar_query(self, primary_query: str, research_focus: str) -> str:
        """Google Scholar查询格式优化"""
        # Google Scholar支持引号精确匹配和文件类型限制
        formatted = f'"{primary_query}" "{research_focus}" filetype:pdf'
        return formatted
    
    def _format_semantic_scholar_query(self, primary_query: str, research_focus: str) -> str:
        """Semantic Scholar查询格式优化"""
        # Semantic Scholar支持语义搜索
        formatted = f'{primary_query} {research_focus}'
        return formatted
    
    def _format_biorxiv_query(self, primary_query: str, research_focus: str) -> str:
        """bioRxiv查询格式优化"""
        # bioRxiv需要简化查询
        formatted = primary_query.replace(' ', '+')
        return formatted
    
    def _get_fallback_keywords(self, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """降级关键词生成方案"""
        sample_type = requirements.get('sampleType', 'cells')
        research_goal = requirements.get('researchGoal', 'analysis')
        
        fallback_query = f"single cell RNA sequencing {sample_type}"
        
        return {
            "intent_analysis": {
                "research_domain": "general",
                "confidence": 0.6
            },
            "integrated_keywords": {
                "primary_query": fallback_query,
                "alternative_queries": [
                    "scRNA-seq analysis",
                    "single cell transcriptomics",
                    f"{sample_type} single cell"
                ],
                "research_focus": f"单细胞分析 + {research_goal}"
            },
            "precision_search_links": {
                "primary_links": {
                    "pubmed": {
                        "url": f"https://pubmed.ncbi.nlm.nih.gov/?term={urllib.parse.quote(fallback_query)}",
                        "name": "PubMed",
                        "description": "生物医学文献数据库",
                        "icon": "🏥"
                    }
                },
                "research_summary": "基于基础信息生成的通用搜索方案"
            },
            "research_focus_display": {
                "main_focus": f"单细胞分析 + {research_goal}",
                "primary_search_query": fallback_query
            }
        }


# 全局服务实例
research_intent_service = ResearchIntentService()


def get_research_intent_service() -> ResearchIntentService:
    """获取研究意图服务实例"""
    return research_intent_service