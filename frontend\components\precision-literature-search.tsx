"use client"

import React, { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  BookOpen, 
  Search, 
  Target, 
  Zap,
  ExternalLink,
  ChevronDown,
  ChevronUp,
  Quote,
  Star
} from 'lucide-react'

interface FeaturedPaper {
  title: string
  authors: string[]
  journal: string
  year: number
  relevance_score: number
  direct_links: {
    pubmed?: string
    doi?: string
    pdf?: string
    google_scholar?: string
  }
  why_relevant: string
}

interface PrecisionLiteratureSearchProps {
  literature_search: {
    research_focus: string
    precision_search_links: {
      pubmed: string
      google_scholar: string
      semantic_scholar: string
      biorxiv: string
    }
    featured_papers: FeaturedPaper[]
    search_tips: string[]
  }
}

const PrecisionLiteratureSearch: React.FC<PrecisionLiteratureSearchProps> = ({ literature_search }) => {
  const [expandedPaper, setExpandedPaper] = useState<number | null>(null)
  const [showTips, setShowTips] = useState(false)

  const searchPlatforms = [
    {
      name: 'PubMed',
      icon: '🏥',
      url: literature_search.precision_search_links.pubmed,
      color: 'bg-blue-600 hover:bg-blue-700',
      description: '医学文献数据库'
    },
    {
      name: 'Google Scholar',
      icon: '🎓',
      url: literature_search.precision_search_links.google_scholar,
      color: 'bg-green-600 hover:bg-green-700',
      description: '学术搜索引擎'
    },
    {
      name: 'Semantic Scholar',
      icon: '🧠',
      url: literature_search.precision_search_links.semantic_scholar,
      color: 'bg-purple-600 hover:bg-purple-700',
      description: 'AI驱动的学术搜索'
    },
    {
      name: 'bioRxiv',
      icon: '🧬',
      url: literature_search.precision_search_links.biorxiv,
      color: 'bg-orange-600 hover:bg-orange-700',
      description: '生物学预印本'
    }
  ]

  return (
    <div className="space-y-6">
      {/* 研究焦点与精准搜索 */}
      <Card className="border-indigo-200">
        <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b border-indigo-200">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Target className="h-5 w-5 text-indigo-600" />
            🎯 精准文献搜索
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          {/* 研究焦点展示 */}
          <div className="mb-6 p-4 bg-slate-50 rounded-lg border border-slate-200">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                <Search className="h-5 w-5 text-white" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-slate-800 mb-1">研究焦点</h4>
                <p className="text-sm text-slate-600 bg-white px-3 py-2 rounded-lg border border-slate-200">
                  {literature_search.research_focus}
                </p>
              </div>
            </div>
          </div>

          {/* 一键精准搜索按钮组 */}
          <div>
            <h4 className="font-medium text-slate-700 mb-4 flex items-center gap-2">
              <Zap className="h-4 w-4 text-yellow-500" />
              一键直达相关文献
            </h4>
            <div className="grid grid-cols-2 gap-3 mb-4">
              {searchPlatforms.map((platform, index) => (
                <a
                  key={index}
                  href={platform.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`${platform.color} text-white rounded-lg p-4 transition-all hover:shadow-lg hover:scale-105 group`}
                >
                  <div className="flex items-center gap-3">
                    <span className="text-2xl">{platform.icon}</span>
                    <div className="flex-1">
                      <div className="font-semibold">{platform.name}</div>
                      <div className="text-xs opacity-90">{platform.description}</div>
                    </div>
                    <ExternalLink className="h-4 w-4 opacity-70 group-hover:opacity-100 transition-opacity" />
                  </div>
                </a>
              ))}
            </div>

            {/* 搜索提示 */}
            <div className="border-t border-slate-200 pt-4">
              <button
                onClick={() => setShowTips(!showTips)}
                className="flex items-center gap-2 text-sm text-slate-600 hover:text-slate-800 transition-colors"
              >
                <ChevronDown className={`h-4 w-4 transition-transform ${showTips ? 'rotate-180' : ''}`} />
                搜索提示 ({literature_search.search_tips.length}条)
              </button>

              {showTips && (
                <div className="mt-3 space-y-2">
                  {literature_search.search_tips.map((tip, index) => (
                    <div key={index} className="flex items-start gap-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
                      <div className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-xs font-medium text-blue-600">{index + 1}</span>
                      </div>
                      <span className="text-sm text-blue-800">{tip}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 推荐文献精选 */}
      <Card className="border-green-200">
        <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 border-b border-green-200">
          <CardTitle className="flex items-center gap-2 text-lg">
            <BookOpen className="h-5 w-5 text-green-600" />
            📚 推荐文献精选
          </CardTitle>
          <p className="text-sm text-slate-600 mt-1">基于您的研究需求智能筛选的高质量文献</p>
        </CardHeader>
        <CardContent className="p-0">
          <div className="divide-y divide-slate-100">
            {literature_search.featured_papers.map((paper, index) => (
              <div key={index} className="p-6 hover:bg-slate-50 transition-colors">
                <div className="flex items-start justify-between gap-4">
                  <div className="flex-1">
                    <h4 className="font-semibold text-slate-800 leading-tight mb-2">{paper.title}</h4>
                    <div className="flex items-center gap-4 text-sm text-slate-600 mb-3">
                      <span>{paper.authors.slice(0, 3).join(', ')}{paper.authors.length > 3 && ' et al.'}</span>
                      <span>•</span>
                      <span className="font-medium">{paper.journal}</span>
                      <span>•</span>
                      <span>{paper.year}</span>
                    </div>

                    {/* 相关性说明 */}
                    <div className="mb-3 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                      <p className="text-sm text-amber-800">
                        <strong>推荐理由：</strong>{paper.why_relevant}
                      </p>
                    </div>

                    {/* 直达链接 */}
                    <div className="flex items-center gap-2 flex-wrap">
                      {paper.direct_links.pubmed && (
                        <a href={paper.direct_links.pubmed} target="_blank" rel="noopener noreferrer"
                           className="inline-flex items-center gap-1 px-3 py-1 bg-blue-600 text-white text-xs rounded-full hover:bg-blue-700 transition-colors">
                          <ExternalLink className="h-3 w-3" />
                          PubMed
                        </a>
                      )}
                      {paper.direct_links.doi && (
                        <a href={paper.direct_links.doi} target="_blank" rel="noopener noreferrer"
                           className="inline-flex items-center gap-1 px-3 py-1 bg-emerald-600 text-white text-xs rounded-full hover:bg-emerald-700 transition-colors">
                          <ExternalLink className="h-3 w-3" />
                          DOI
                        </a>
                      )}
                      {paper.direct_links.pdf && (
                        <a href={paper.direct_links.pdf} target="_blank" rel="noopener noreferrer"
                           className="inline-flex items-center gap-1 px-3 py-1 bg-red-600 text-white text-xs rounded-full hover:bg-red-700 transition-colors">
                          <ExternalLink className="h-3 w-3" />
                          PDF
                        </a>
                      )}
                      {paper.direct_links.google_scholar && (
                        <a href={paper.direct_links.google_scholar} target="_blank" rel="noopener noreferrer"
                           className="inline-flex items-center gap-1 px-3 py-1 bg-green-600 text-white text-xs rounded-full hover:bg-green-700 transition-colors">
                          <ExternalLink className="h-3 w-3" />
                          Scholar
                        </a>
                      )}
                    </div>
                  </div>

                  {/* 相关度评分 */}
                  <div className="flex flex-col items-end gap-2">
                    <div className="flex items-center gap-1">
                      <span className="text-xs text-slate-500">相关度</span>
                      <div className="w-16 h-2 bg-slate-200 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-gradient-to-r from-green-400 to-green-600 rounded-full transition-all"
                          style={{ width: `${paper.relevance_score * 100}%` }}
                        />
                      </div>
                      <span className="text-xs font-medium text-slate-700">{Math.round(paper.relevance_score * 100)}%</span>
                    </div>
                    
                    {/* 质量评分 */}
                    <div className="flex items-center gap-1">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className={`h-3 w-3 ${i < Math.floor(paper.relevance_score * 5) ? 'text-yellow-400 fill-current' : 'text-slate-300'}`} />
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default PrecisionLiteratureSearch