"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/enhanced-solution-overview-card.tsx":
/*!********************************************************!*\
  !*** ./components/enhanced-solution-overview-card.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle2_ChevronDown_ChevronUp_DollarSign_Microscope_PlayCircle_Settings_Target_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle2,ChevronDown,ChevronUp,DollarSign,Microscope,PlayCircle,Settings,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/microscope.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle2_ChevronDown_ChevronUp_DollarSign_Microscope_PlayCircle_Settings_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle2,ChevronDown,ChevronUp,DollarSign,Microscope,PlayCircle,Settings,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle2_ChevronDown_ChevronUp_DollarSign_Microscope_PlayCircle_Settings_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle2,ChevronDown,ChevronUp,DollarSign,Microscope,PlayCircle,Settings,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle2_ChevronDown_ChevronUp_DollarSign_Microscope_PlayCircle_Settings_Target_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle2,ChevronDown,ChevronUp,DollarSign,Microscope,PlayCircle,Settings,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-play.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle2_ChevronDown_ChevronUp_DollarSign_Microscope_PlayCircle_Settings_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle2,ChevronDown,ChevronUp,DollarSign,Microscope,PlayCircle,Settings,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle2_ChevronDown_ChevronUp_DollarSign_Microscope_PlayCircle_Settings_Target_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle2,ChevronDown,ChevronUp,DollarSign,Microscope,PlayCircle,Settings,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle2_ChevronDown_ChevronUp_DollarSign_Microscope_PlayCircle_Settings_Target_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle2,ChevronDown,ChevronUp,DollarSign,Microscope,PlayCircle,Settings,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle2_ChevronDown_ChevronUp_DollarSign_Microscope_PlayCircle_Settings_Target_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle2,ChevronDown,ChevronUp,DollarSign,Microscope,PlayCircle,Settings,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle2_ChevronDown_ChevronUp_DollarSign_Microscope_PlayCircle_Settings_Target_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle2,ChevronDown,ChevronUp,DollarSign,Microscope,PlayCircle,Settings,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst EnhancedSolutionOverviewCard = (param)=>{\n    let { solution_overview } = param;\n    _s();\n    const [showTechnicalDetails, setShowTechnicalDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProcessSteps, setShowProcessSteps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showConsiderations, setShowConsiderations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"border-blue-200 shadow-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                className: \"bg-gradient-to-br from-blue-50 to-indigo-100 border-b border-blue-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_ChevronDown_ChevronUp_DollarSign_Microscope_PlayCircle_Settings_Target_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-6 w-6 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-xl font-bold text-slate-800\",\n                                    children: \"\\uD83E\\uDDEC 单细胞测序解决方案\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-slate-600 mt-1\",\n                                    children: \"基于AI分析的个性化技术方案\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 bg-white rounded-lg border border-slate-200 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_ChevronDown_ChevronUp_DollarSign_Microscope_PlayCircle_Settings_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4 text-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-slate-500\",\n                                                children: \"研究类型\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold text-slate-800\",\n                                        children: solution_overview.research_type\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-slate-600 mt-1\",\n                                        children: [\n                                            \"平台：\",\n                                            solution_overview.recommended_platform\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 bg-white rounded-lg border border-slate-200 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_ChevronDown_ChevronUp_DollarSign_Microscope_PlayCircle_Settings_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4 text-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-slate-500\",\n                                                children: \"预估成本\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold text-slate-800\",\n                                        children: solution_overview.estimated_cost\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-slate-600 mt-1\",\n                                        children: [\n                                            \"周期：\",\n                                            solution_overview.project_timeline\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-slate-800 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_ChevronDown_ChevronUp_DollarSign_Microscope_PlayCircle_Settings_Target_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 text-indigo-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"全流程关键环节\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowProcessSteps(!showProcessSteps),\n                                        className: \"text-slate-600 hover:text-slate-800\",\n                                        children: showProcessSteps ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_ChevronDown_ChevronUp_DollarSign_Microscope_PlayCircle_Settings_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 35\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_ChevronDown_ChevronUp_DollarSign_Microscope_PlayCircle_Settings_Target_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 71\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, undefined),\n                            showProcessSteps && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: solution_overview.key_process_steps.map((step, index)=>{\n                                    const [timeAndTitle, description] = step.split(' - ');\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-3 p-3 bg-slate-50 rounded-lg border border-slate-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-bold text-white\",\n                                                    children: index + 1\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-slate-800\",\n                                                        children: timeAndTitle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-slate-600 mt-1\",\n                                                        children: description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-slate-800 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_ChevronDown_ChevronUp_DollarSign_Microscope_PlayCircle_Settings_Target_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 text-amber-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"关键注意事项\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowConsiderations(!showConsiderations),\n                                        className: \"text-slate-600 hover:text-slate-800\",\n                                        children: showConsiderations ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_ChevronDown_ChevronUp_DollarSign_Microscope_PlayCircle_Settings_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 37\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_ChevronDown_ChevronUp_DollarSign_Microscope_PlayCircle_Settings_Target_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 73\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, undefined),\n                            showConsiderations && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: solution_overview.critical_considerations.map((consideration, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-3 p-3 bg-amber-50 rounded-lg border border-amber-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_ChevronDown_ChevronUp_DollarSign_Microscope_PlayCircle_Settings_Target_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 text-amber-600 flex-shrink-0 mt-0.5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-amber-800\",\n                                                children: consideration\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-slate-800 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_ChevronDown_ChevronUp_DollarSign_Microscope_PlayCircle_Settings_Target_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 text-slate-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"技术方案详情\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowTechnicalDetails(!showTechnicalDetails),\n                                        className: \"text-slate-600 hover:text-slate-800\",\n                                        children: showTechnicalDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_ChevronDown_ChevronUp_DollarSign_Microscope_PlayCircle_Settings_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 39\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_ChevronDown_ChevronUp_DollarSign_Microscope_PlayCircle_Settings_Target_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 75\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, undefined),\n                            showTechnicalDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-white rounded-lg border border-slate-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-slate-500 mb-1\",\n                                                children: \"平台规格\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-slate-700\",\n                                                children: solution_overview.technical_details.platform_specifications\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-white rounded-lg border border-slate-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-slate-500 mb-1\",\n                                                children: \"样本制备\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-slate-700\",\n                                                children: solution_overview.technical_details.sample_prep_protocol\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-white rounded-lg border border-slate-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-slate-500 mb-1\",\n                                                children: \"分析流程\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-slate-700\",\n                                                children: solution_overview.technical_details.analysis_pipeline\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-white rounded-lg border border-slate-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-slate-500 mb-1\",\n                                                children: \"预期结果\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-slate-700\",\n                                                children: solution_overview.technical_details.expected_outcomes\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 pt-4 border-t border-slate-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle2_ChevronDown_ChevronUp_DollarSign_Microscope_PlayCircle_Settings_Target_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 text-green-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-slate-600\",\n                                            children: \"方案状态：\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                            variant: \"secondary\",\n                                            className: \"bg-green-100 text-green-800 border-green-200\",\n                                            children: \"已优化\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-slate-500\",\n                                    children: [\n                                        \"生成时间：\",\n                                        new Date().toLocaleString('zh-CN')\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\enhanced-solution-overview-card.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EnhancedSolutionOverviewCard, \"+vPhyVmD6bbgu1fLfzuzB82sAgQ=\");\n_c = EnhancedSolutionOverviewCard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EnhancedSolutionOverviewCard);\nvar _c;\n$RefreshReg$(_c, \"EnhancedSolutionOverviewCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/enhanced-solution-overview-card.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/formatted-message.tsx":
/*!******************************************!*\
  !*** ./components/formatted-message.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FormattedMessage: () => (/* binding */ FormattedMessage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Copy,ExternalLink,Info,Lightbulb!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Copy,ExternalLink,Info,Lightbulb!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Copy,ExternalLink,Info,Lightbulb!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Copy,ExternalLink,Info,Lightbulb!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Copy,ExternalLink,Info,Lightbulb!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Copy,ExternalLink,Info,Lightbulb!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _interactive_literature_citation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./interactive-literature-citation */ \"(app-pages-browser)/./components/interactive-literature-citation.tsx\");\n/* harmony import */ var _comprehensive_solution_display__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./comprehensive-solution-display */ \"(app-pages-browser)/./components/comprehensive-solution-display.tsx\");\n/* harmony import */ var _comprehensive_solution_framework__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./comprehensive-solution-framework */ \"(app-pages-browser)/./components/comprehensive-solution-framework.tsx\");\n/* harmony import */ var _streamlined_solution_display__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./streamlined-solution-display */ \"(app-pages-browser)/./components/streamlined-solution-display.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction FormattedMessage(param) {\n    let { content, className = '' } = param;\n    _s();\n    // 渲染Mermaid图表的简化版本\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FormattedMessage.useEffect\": ()=>{\n            const renderMermaidDiagrams = {\n                \"FormattedMessage.useEffect.renderMermaidDiagrams\": ()=>{\n                    const diagrams = document.querySelectorAll('.mermaid-diagram[data-mermaid]:not(.mermaid-processed)');\n                    diagrams.forEach({\n                        \"FormattedMessage.useEffect.renderMermaidDiagrams\": (diagram)=>{\n                            const element = diagram;\n                            const mermaidCode = element.getAttribute('data-mermaid');\n                            if (!mermaidCode) return;\n                            // 标记为已处理\n                            element.classList.add('mermaid-processed');\n                            // 创建一个更好的展示界面\n                            element.innerHTML = '\\n          <div class=\"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200\">\\n            <div class=\"text-center mb-4\">\\n              <div class=\"inline-flex items-center gap-2 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium\">\\n                <svg class=\"h-4 w-4\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\\n                  <path d=\"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\"/>\\n                </svg>\\n                Mermaid 图表\\n              </div>\\n            </div>\\n            <div class=\"bg-white rounded border border-slate-200 p-4\">\\n              <div class=\"text-center text-slate-600 mb-3\">\\n                <div class=\"text-lg mb-2\">\\uD83D\\uDCCA</div>\\n                <div class=\"text-sm font-medium\">图表内容预览</div>\\n              </div>\\n              <pre class=\"text-xs text-slate-700 bg-slate-50 p-3 rounded border overflow-x-auto whitespace-pre-wrap\">'.concat(mermaidCode, '</pre>\\n              <div class=\"mt-3 text-center\">\\n                <div class=\"text-xs text-slate-500\">\\n                  \\uD83D\\uDCA1 提示：此图表包含 ').concat(mermaidCode.split('\\\\n').length, \" 行定义\\n                </div>\\n              </div>\\n            </div>\\n          </div>\\n        \");\n                        }\n                    }[\"FormattedMessage.useEffect.renderMermaidDiagrams\"]);\n                }\n            }[\"FormattedMessage.useEffect.renderMermaidDiagrams\"];\n            // 延迟处理，确保DOM已更新\n            const timer = setTimeout(renderMermaidDiagrams, 100);\n            return ({\n                \"FormattedMessage.useEffect\": ()=>clearTimeout(timer)\n            })[\"FormattedMessage.useEffect\"];\n        }\n    }[\"FormattedMessage.useEffect\"], [\n        content\n    ]);\n    // 处理内联格式（粗体、斜体、代码、链接等）\n    const formatInlineText = (text)=>{\n        // 如果没有特殊格式，直接返回\n        if (!text.includes('**') && !text.includes('*') && !text.includes('`') && !text.includes('~~') && !text.includes('[')) {\n            return [\n                text\n            ];\n        }\n        const parts = [];\n        let key = 0;\n        // 改进的正则表达式，更精确地处理markdown格式\n        // 优先匹配较长的格式（如 *** 和 **），避免误匹配，并支持链接\n        const formatRegex = RegExp('(\\\\[([^\\\\]]+?)\\\\]\\\\(([^)]+?)(?:\\\\s+\"([^\"]*)\")?\\\\)|\\\\*\\\\*\\\\*([^*\\\\n]+?)\\\\*\\\\*\\\\*|\\\\*\\\\*([^*\\\\n]+?)\\\\*\\\\*|~~([^~\\\\n]+?)~~|(?<!\\\\*)\\\\*([^*\\\\n]+?)\\\\*(?!\\\\*)|`([^`\\\\n]+?)`)', \"g\");\n        let lastIndex = 0;\n        let match;\n        // 重置正则表达式的lastIndex\n        formatRegex.lastIndex = 0;\n        while((match = formatRegex.exec(text)) !== null){\n            // 添加格式前的普通文本\n            if (match.index > lastIndex) {\n                const plainText = text.slice(lastIndex, match.index);\n                if (plainText) {\n                    parts.push(plainText);\n                }\n            }\n            // 根据匹配的格式添加相应的元素\n            if (match[2] && match[3]) {\n                // 链接 [text](url) 或 [text](url \"title\")\n                const linkText = match[2];\n                const linkUrl = match[3];\n                const linkTitle = match[4] || '';\n                parts.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: linkUrl,\n                    title: linkTitle,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    className: \"text-blue-600 hover:text-blue-800 underline hover:no-underline transition-colors duration-200 font-medium inline-flex items-center gap-1\",\n                    children: [\n                        linkText,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-3 w-3 opacity-60\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, key++, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 11\n                }, this));\n            } else if (match[5]) {\n                // 粗体斜体 ***text***\n                parts.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                    className: \"font-bold italic text-slate-900\",\n                    children: match[5]\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 11\n                }, this));\n            } else if (match[6]) {\n                // 粗体 **text**\n                parts.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                    className: \"font-semibold text-slate-900\",\n                    children: match[6]\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 11\n                }, this));\n            } else if (match[7]) {\n                // 删除线 ~~text~~\n                parts.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"line-through text-slate-600\",\n                    children: match[7]\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 11\n                }, this));\n            } else if (match[8]) {\n                // 斜体 *text* (使用负向前瞻和后瞻避免与**冲突)\n                parts.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                    className: \"italic text-slate-800\",\n                    children: match[8]\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 11\n                }, this));\n            } else if (match[9]) {\n                // 代码 `text`\n                parts.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                    className: \"bg-slate-100 text-slate-800 px-1 py-0.5 rounded text-sm font-mono\",\n                    children: match[9]\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, this));\n            }\n            lastIndex = match.index + match[0].length;\n        }\n        // 添加剩余的普通文本\n        if (lastIndex < text.length) {\n            const remainingPlainText = text.slice(lastIndex);\n            if (remainingPlainText) {\n                parts.push(remainingPlainText);\n            }\n        }\n        return parts.length > 0 ? parts : [\n            text\n        ];\n    };\n    // 复制文本到剪贴板\n    const copyToClipboard = (text)=>{\n        navigator.clipboard.writeText(text).then(()=>{\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('已复制到剪贴板');\n        }).catch(()=>{\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('复制失败');\n        });\n    };\n    const formatContent = (text)=>{\n        const lines = text.split('\\n');\n        const elements = [];\n        let key = 0;\n        let inCodeBlock = false;\n        let codeBlockContent = [];\n        let codeBlockLanguage = '';\n        for(let i = 0; i < lines.length; i++){\n            const line = lines[i];\n            const trimmedLine = line.trim();\n            // 处理代码块\n            if (trimmedLine.startsWith('```')) {\n                if (!inCodeBlock) {\n                    // 开始代码块\n                    inCodeBlock = true;\n                    codeBlockLanguage = trimmedLine.slice(3).trim();\n                    codeBlockContent = [];\n                } else {\n                    // 结束代码块\n                    inCodeBlock = false;\n                    const codeContent = codeBlockContent.join('\\n');\n                    // 检查是否是综合解决方案或综合方案框架的JSON数据\n                    if (codeBlockLanguage === 'json') {\n                        try {\n                            const jsonData = JSON.parse(codeContent);\n                            if (jsonData.type === 'comprehensive_framework' && jsonData.data) {\n                                // 渲染综合方案框架组件（按设计文档要求）\n                                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_comprehensive_solution_framework__WEBPACK_IMPORTED_MODULE_6__.ComprehensiveSolutionFramework, {\n                                        frameworkData: jsonData.data,\n                                        onSearchClick: (platform, url)=>{\n                                            window.open(url, '_blank');\n                                        },\n                                        onOptimize: (feedback)=>{\n                                            console.log('优化反馈:', feedback);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 21\n                                    }, this)\n                                }, key++, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 19\n                                }, this));\n                            } else if (jsonData.type === 'comprehensive_solution' && jsonData.data) {\n                                // 渲染综合解决方案组件（降级方案）\n                                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_comprehensive_solution_display__WEBPACK_IMPORTED_MODULE_5__.ComprehensiveSolutionDisplay, {\n                                        solutionData: jsonData.data\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 21\n                                    }, this)\n                                }, key++, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 19\n                                }, this));\n                            } else if (jsonData.type === 'streamlined_solution' && jsonData.data) {\n                                // 渲染简化方案组件（新的3模块方案）\n                                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_streamlined_solution_display__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        solutionData: jsonData.data\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 21\n                                    }, this)\n                                }, key++, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 19\n                                }, this));\n                            } else {\n                                // 普通JSON代码块\n                                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-4 relative group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-slate-900 rounded-lg overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between px-4 py-2 bg-slate-800 border-b border-slate-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-slate-300 font-medium\",\n                                                        children: \"JSON\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>copyToClipboard(codeContent),\n                                                        className: \"h-6 px-2 text-slate-400 hover:text-white hover:bg-slate-700\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"p-4 text-sm text-slate-100 overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    children: codeContent\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 21\n                                    }, this)\n                                }, key++, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 19\n                                }, this));\n                            }\n                        } catch (jsonError) {\n                            // JSON解析失败，作为普通代码块处理\n                            elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"my-4 relative group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-900 rounded-lg overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between px-4 py-2 bg-slate-800 border-b border-slate-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-slate-300 font-medium\",\n                                                    children: \"JSON\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>copyToClipboard(codeContent),\n                                                    className: \"h-6 px-2 text-slate-400 hover:text-white hover:bg-slate-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                            className: \"p-4 text-sm text-slate-100 overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                children: codeContent\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 19\n                                }, this)\n                            }, key++, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 17\n                            }, this));\n                        }\n                    } else if (codeBlockLanguage === 'mermaid' || codeContent.trim().match(/^(graph|pie|gantt|sequenceDiagram|classDiagram|stateDiagram|journey|gitgraph|flowchart)/)) {\n                        elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white border border-slate-200 rounded-lg overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between px-4 py-2 bg-slate-50 border-b border-slate-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-slate-600 font-medium flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"h-4 w-4\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Mermaid 图表\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>copyToClipboard(codeContent),\n                                                className: \"h-6 px-2 text-slate-400 hover:text-slate-600\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mermaid-diagram bg-white rounded border border-slate-100 p-4 text-center min-h-[200px] flex flex-col items-center justify-center\",\n                                            \"data-mermaid\": codeContent,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-slate-500 text-sm mb-2\",\n                                                    children: \"\\uD83C\\uDFA8 正在渲染图表...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-slate-400 bg-slate-50 p-2 rounded text-left overflow-x-auto max-w-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                        className: \"whitespace-pre-wrap\",\n                                                        children: codeContent\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 17\n                            }, this)\n                        }, key++, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 15\n                        }, this));\n                    } else {\n                        // 普通代码块\n                        elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-4 relative group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-slate-900 rounded-lg overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between px-4 py-2 bg-slate-800 border-b border-slate-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-slate-300 font-medium\",\n                                                children: codeBlockLanguage || 'Code'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>copyToClipboard(codeContent),\n                                                className: \"h-6 px-2 text-slate-400 hover:text-white hover:bg-slate-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"p-4 text-sm text-slate-100 overflow-x-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            children: codeContent\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 17\n                            }, this)\n                        }, key++, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 15\n                        }, this));\n                    }\n                    codeBlockContent = [];\n                    codeBlockLanguage = '';\n                }\n                continue;\n            }\n            // 如果在代码块内，收集内容\n            if (inCodeBlock) {\n                codeBlockContent.push(line);\n                continue;\n            }\n            // Skip empty lines but add spacing\n            if (!trimmedLine) {\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-2\"\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 23\n                }, this));\n                continue;\n            }\n            // Markdown标题支持 (### ## #)\n            if (trimmedLine.match(/^#{1,6}\\s+(.+)/)) {\n                const match = trimmedLine.match(/^(#{1,6})\\s+(.+)/);\n                if (match) {\n                    const [, hashes, title] = match;\n                    const level = hashes.length;\n                    let className = \"\";\n                    let Component = \"h1\";\n                    switch(level){\n                        case 1:\n                            Component = \"h1\";\n                            className = \"text-2xl font-bold text-slate-900 mt-6 mb-4 pb-2 border-b border-slate-200\";\n                            break;\n                        case 2:\n                            Component = \"h2\";\n                            className = \"text-xl font-semibold text-slate-800 mt-5 mb-3 pb-1 border-b border-slate-100\";\n                            break;\n                        case 3:\n                            Component = \"h3\";\n                            className = \"text-lg font-semibold text-slate-800 mt-4 mb-2\";\n                            break;\n                        case 4:\n                            Component = \"h4\";\n                            className = \"text-base font-medium text-slate-700 mt-3 mb-2\";\n                            break;\n                        case 5:\n                            Component = \"h5\";\n                            className = \"text-sm font-medium text-slate-700 mt-2 mb-1\";\n                            break;\n                        case 6:\n                            Component = \"h6\";\n                            className = \"text-sm font-medium text-slate-600 mt-2 mb-1\";\n                            break;\n                    }\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                            className: className,\n                            children: formatInlineText(title)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                            lineNumber: 393,\n                            columnNumber: 15\n                        }, this)\n                    }, key++, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 13\n                    }, this));\n                }\n                continue;\n            }\n            // Main title (🎯 at start)\n            if (trimmedLine.match(/^🎯\\s+(.+)/)) {\n                const title = trimmedLine.replace(/^🎯\\s+/, '');\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-xl font-bold text-blue-900 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"\\uD83C\\uDFAF\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: formatInlineText(title)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 407,\n                        columnNumber: 13\n                    }, this)\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 406,\n                    columnNumber: 11\n                }, this));\n                continue;\n            }\n            // Section headers with emojis (📊, 🔬, 💰, ⏰, ⚠️, 🧬, 🏆, 🤝, etc.)\n            if (trimmedLine.match(/^[📊🔬💰⏰⚠️🎯📋📅⚡🛡️📞🔍💡🚀🧬🏆🤝📚📈]\\s+(.+)/)) {\n                const match = trimmedLine.match(/^([📊🔬💰⏰⚠️🎯📋📅⚡🛡️📞🔍💡🚀🧬🏆🤝📚📈])\\s+(.+)/);\n                if (match) {\n                    const [, emoji, title] = match;\n                    const isIntelligentFeature = [\n                        '🧬',\n                        '🏆',\n                        '🤝',\n                        '📚',\n                        '📈'\n                    ].includes(emoji);\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 mb-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold flex items-center gap-2 border-b pb-2 \".concat(isIntelligentFeature ? 'text-blue-800 border-blue-200 bg-blue-50 px-3 py-2 rounded-t-lg' : 'text-slate-800 border-slate-200'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl\",\n                                    children: emoji\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: formatInlineText(title)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 17\n                                }, this),\n                                isIntelligentFeature && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs bg-blue-600 text-white px-2 py-1 rounded-full ml-auto\",\n                                    children: \"AI智能推荐\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                            lineNumber: 424,\n                            columnNumber: 15\n                        }, this)\n                    }, key++, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 13\n                    }, this));\n                }\n                continue;\n            }\n            // Subsection headers (🌟, 💡, 🎯, etc.)\n            if (trimmedLine.match(/^[🌟💡🎯📅⚡]\\s+(.+):/)) {\n                const match = trimmedLine.match(/^([🌟💡🎯📅⚡])\\s+(.+):?/);\n                if (match) {\n                    const [, emoji, title] = match;\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-base font-medium text-slate-700 flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: emoji\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: formatInlineText(title)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                            lineNumber: 450,\n                            columnNumber: 15\n                        }, this)\n                    }, key++, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 449,\n                        columnNumber: 13\n                    }, this));\n                }\n                continue;\n            }\n            // Markdown分隔线支持 (--- *** ___)\n            if (trimmedLine.match(/^(-{3,}|\\*{3,}|_{3,})$/)) {\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                        className: \"border-slate-300 border-t-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 464,\n                        columnNumber: 13\n                    }, this)\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 463,\n                    columnNumber: 11\n                }, this));\n                continue;\n            }\n            // Separator lines (━━━)\n            if (trimmedLine.match(/^━+$/)) {\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                        className: \"border-slate-300 border-t-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 474,\n                        columnNumber: 13\n                    }, this)\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 473,\n                    columnNumber: 11\n                }, this));\n                continue;\n            }\n            // Markdown无序列表支持 (- * +)\n            if (trimmedLine.match(/^[\\s]*[-\\*\\+]\\s+(.+)/)) {\n                const match = trimmedLine.match(/^(\\s*)([-\\*\\+])\\s+(.+)/);\n                if (match) {\n                    const [, indent, bullet, text] = match;\n                    const indentLevel = Math.floor(indent.length / 2) // 每2个空格为一级缩进\n                    ;\n                    const marginLeft = indentLevel * 20 + 16 // 基础16px + 每级20px\n                    ;\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-1 flex items-start gap-2\",\n                        style: {\n                            marginLeft: \"\".concat(marginLeft, \"px\")\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mt-0.5 text-slate-600 font-bold\",\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-slate-700 leading-relaxed\",\n                                children: formatInlineText(text)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 493,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, key++, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 489,\n                        columnNumber: 13\n                    }, this));\n                }\n                continue;\n            }\n            // Bullet points (• or ✅)\n            if (trimmedLine.match(/^[•✅]\\s+(.+)/)) {\n                const match = trimmedLine.match(/^([•✅])\\s+(.+)/);\n                if (match) {\n                    const [, bullet, text] = match;\n                    const isCheckmark = bullet === '✅';\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-4 mb-1 flex items-start gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mt-0.5 \".concat(isCheckmark ? 'text-green-600' : 'text-slate-600'),\n                                children: bullet\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-slate-700 leading-relaxed\",\n                                children: formatInlineText(text)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, key++, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 13\n                    }, this));\n                }\n                continue;\n            }\n            // Numbered lists (1., 2., etc.)\n            if (trimmedLine.match(/^\\d+\\.\\s+(.+)/)) {\n                const match = trimmedLine.match(/^(\\d+)\\.\\s+(.+)/);\n                if (match) {\n                    const [, number, text] = match;\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-4 mb-2 flex items-start gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-blue-100 text-blue-800 text-sm font-medium px-2 py-0.5 rounded-full min-w-[24px] text-center\",\n                                children: number\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-slate-700 leading-relaxed\",\n                                children: formatInlineText(text)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 528,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, key++, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 524,\n                        columnNumber: 13\n                    }, this));\n                }\n                continue;\n            }\n            // Key-value pairs with colon (研究目标：xxx)\n            if (trimmedLine.includes('：') && !trimmedLine.match(/^[📊🔬💰⏰⚠️🎯📋📅⚡🛡️📞🔍💡🚀]/)) {\n                const parts = trimmedLine.split('：');\n                if (parts.length === 2) {\n                    const keyText = parts[0].trim();\n                    const value = parts[1].trim();\n                    // Special styling for different types of information\n                    const isImportant = keyText.includes('预算') || keyText.includes('费用') || keyText.includes('成本');\n                    const isTime = keyText.includes('时间') || keyText.includes('周期') || keyText.includes('阶段');\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2 flex items-start\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium min-w-[120px] \".concat(isImportant ? 'text-green-700' : isTime ? 'text-blue-700' : 'text-slate-800'),\n                                children: [\n                                    keyText,\n                                    \"：\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 548,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-slate-700 \".concat(isImportant ? 'font-medium text-green-800' : ''),\n                                children: formatInlineText(value)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 555,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, key++, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 547,\n                        columnNumber: 13\n                    }, this));\n                    continue;\n                }\n            }\n            // Cost/fee information (¥ symbol)\n            if (trimmedLine.includes('¥')) {\n                // Extract cost ranges and highlight them\n                const costPattern = /¥([\\d,.-]+)/g;\n                const parts = trimmedLine.split(costPattern);\n                const formattedParts = parts.map((part, index)=>{\n                    if (part.match(/^[\\d,.-]+$/)) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-semibold text-green-700 bg-green-50 px-1 rounded\",\n                            children: [\n                                \"\\xa5\",\n                                part\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                            lineNumber: 574,\n                            columnNumber: 15\n                        }, this);\n                    }\n                    return part;\n                });\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-1 text-slate-700\",\n                    children: formattedParts\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 583,\n                    columnNumber: 11\n                }, this));\n                continue;\n            }\n            // 表格支持 (| column1 | column2 |)\n            if (trimmedLine.includes('|') && trimmedLine.split('|').length >= 3) {\n                // 收集表格行\n                const tableRows = [\n                    trimmedLine\n                ];\n                let j = i + 1;\n                // 收集连续的表格行\n                while(j < lines.length){\n                    const nextLine = lines[j].trim();\n                    if (nextLine.includes('|') && nextLine.split('|').length >= 3) {\n                        tableRows.push(nextLine);\n                        j++;\n                    } else if (nextLine === '') {\n                        j++;\n                        break;\n                    } else {\n                        break;\n                    }\n                }\n                if (tableRows.length > 0) {\n                    const isHeaderSeparator = (row)=>/^[\\|\\s\\-:]+$/.test(row);\n                    let headerRow = null;\n                    let separatorIndex = -1;\n                    let dataRows = [];\n                    // 查找表头和分隔符\n                    for(let k = 0; k < tableRows.length; k++){\n                        if (isHeaderSeparator(tableRows[k])) {\n                            if (k > 0) {\n                                headerRow = tableRows[k - 1];\n                                separatorIndex = k;\n                            }\n                            break;\n                        }\n                    }\n                    // 如果找到分隔符，分离数据行\n                    if (separatorIndex >= 0) {\n                        dataRows = tableRows.slice(separatorIndex + 1);\n                    } else {\n                        // 没有分隔符，第一行作为表头，其余作为数据\n                        headerRow = tableRows[0];\n                        dataRows = tableRows.slice(1);\n                    }\n                    const parseTableRow = (row)=>{\n                        return row.split('|').map((cell)=>cell.trim()).filter((cell)=>cell !== '');\n                    };\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full border border-slate-200 rounded-lg overflow-hidden\",\n                            children: [\n                                headerRow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-slate-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: parseTableRow(headerRow).map((header, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-4 py-2 text-left text-sm font-medium text-slate-700 border-b border-slate-200\",\n                                                children: formatInlineText(header)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 25\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                        lineNumber: 647,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                    lineNumber: 646,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: dataRows.map((row, rowIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: rowIndex % 2 === 0 ? 'bg-white' : 'bg-slate-25',\n                                            children: parseTableRow(row).map((cell, cellIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-4 py-2 text-sm text-slate-700 border-b border-slate-100\",\n                                                    children: formatInlineText(cell)\n                                                }, cellIndex, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                    lineNumber: 660,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, rowIndex, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                            lineNumber: 658,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                    lineNumber: 656,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                            lineNumber: 644,\n                            columnNumber: 15\n                        }, this)\n                    }, key++, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 643,\n                        columnNumber: 13\n                    }, this));\n                    i = j - 1 // 跳过已处理的行\n                    ;\n                    continue;\n                }\n            }\n            // 引用块支持 (> text)\n            if (trimmedLine.startsWith('> ')) {\n                const quoteText = trimmedLine.slice(2);\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-3 pl-4 border-l-4 border-blue-300 bg-blue-50 py-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-slate-700 italic\",\n                        children: formatInlineText(quoteText)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 681,\n                        columnNumber: 13\n                    }, this)\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 680,\n                    columnNumber: 11\n                }, this));\n                continue;\n            }\n            // 警告和提示框增强\n            if (trimmedLine.match(/^[⚠️💡ℹ️✅]\\s+(.+)/)) {\n                const match = trimmedLine.match(/^([⚠️💡ℹ️✅])\\s+(.+)/);\n                if (match) {\n                    const [, emoji, text] = match;\n                    let bgColor, borderColor, textColor, icon;\n                    switch(emoji){\n                        case '⚠️':\n                            bgColor = 'bg-amber-50';\n                            borderColor = 'border-amber-200';\n                            textColor = 'text-amber-800';\n                            icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 699,\n                                columnNumber: 22\n                            }, this);\n                            break;\n                        case '💡':\n                            bgColor = 'bg-blue-50';\n                            borderColor = 'border-blue-200';\n                            textColor = 'text-blue-800';\n                            icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 705,\n                                columnNumber: 22\n                            }, this);\n                            break;\n                        case 'ℹ️':\n                            bgColor = 'bg-slate-50';\n                            borderColor = 'border-slate-200';\n                            textColor = 'text-slate-800';\n                            icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 711,\n                                columnNumber: 22\n                            }, this);\n                            break;\n                        case '✅':\n                            bgColor = 'bg-green-50';\n                            borderColor = 'border-green-200';\n                            textColor = 'text-green-800';\n                            icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 717,\n                                columnNumber: 22\n                            }, this);\n                            break;\n                        default:\n                            bgColor = 'bg-slate-50';\n                            borderColor = 'border-slate-200';\n                            textColor = 'text-slate-800';\n                            icon = null;\n                    }\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 rounded-lg mb-3 \".concat(bgColor, \" \").concat(borderColor, \" border\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium flex items-center gap-2 \".concat(textColor),\n                            children: [\n                                icon,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: formatInlineText(text)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                    lineNumber: 730,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                            lineNumber: 728,\n                            columnNumber: 15\n                        }, this)\n                    }, key++, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 727,\n                        columnNumber: 13\n                    }, this));\n                }\n                continue;\n            }\n            // Signature line (CellForge AI - ...)\n            if (trimmedLine.includes('CellForge AI') && trimmedLine.includes('🧬')) {\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 pt-4 border-t border-slate-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center text-sm text-slate-500 italic\",\n                        children: trimmedLine\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 742,\n                        columnNumber: 13\n                    }, this)\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 741,\n                    columnNumber: 11\n                }, this));\n                continue;\n            }\n            // 检测文献引用格式 **[数字] 标题**\n            const literatureMatch = trimmedLine.match(/^\\*\\*\\[(\\d+)\\]\\s+(.+?)\\*\\*$/);\n            if (literatureMatch) {\n                const [, number, title] = literatureMatch;\n                // 收集后续的文献信息行\n                const literatureInfo = [\n                    trimmedLine\n                ];\n                let j = i + 1;\n                // 收集文献的详细信息（作者、期刊、DOI等）\n                while(j < lines.length){\n                    const nextLine = lines[j].trim();\n                    if (nextLine === '' || nextLine.startsWith('**[')) {\n                        break;\n                    }\n                    if (nextLine.startsWith('*') || nextLine.startsWith('📖') || nextLine.startsWith('🏆') || nextLine.startsWith('💡') || nextLine.startsWith('🔑') || nextLine.startsWith('🔗')) {\n                        literatureInfo.push(nextLine);\n                        j++;\n                    } else {\n                        break;\n                    }\n                }\n                // 解析文献信息\n                const literature = parseLiteratureInfo(literatureInfo);\n                if (literature) {\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_interactive_literature_citation__WEBPACK_IMPORTED_MODULE_4__.InteractiveLiteratureCitation, {\n                            literature: literature,\n                            relevanceExplanation: literature.relevanceExplanation,\n                            supportPoints: literature.supportPoints,\n                            showQualityIndicators: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                            lineNumber: 783,\n                            columnNumber: 15\n                        }, this)\n                    }, key++, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 782,\n                        columnNumber: 13\n                    }, this));\n                } else {\n                    // 如果解析失败，回退到普通文本显示\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-2 text-slate-700 leading-relaxed\",\n                        children: formatInlineText(trimmedLine)\n                    }, key++, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 794,\n                        columnNumber: 13\n                    }, this));\n                }\n                i = j - 1 // 跳过已处理的行\n                ;\n                continue;\n            }\n            // Regular paragraph text\n            if (trimmedLine.length > 0) {\n                // Check if it's a continuation of a list or section\n                const isIndented = line.startsWith('  ') || line.startsWith('\\t');\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-2 text-slate-700 leading-relaxed \".concat(isIndented ? 'ml-4 text-slate-600' : ''),\n                    children: formatInlineText(trimmedLine)\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 810,\n                    columnNumber: 11\n                }, this));\n            }\n        }\n        return elements;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"formatted-message \".concat(className),\n        children: formatContent(content)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n        lineNumber: 823,\n        columnNumber: 5\n    }, this);\n}\n_s(FormattedMessage, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = FormattedMessage;\n// 文献信息解析函数\nfunction parseLiteratureInfo(literatureInfo) {\n    try {\n        const titleLine = literatureInfo[0];\n        const titleMatch = titleLine.match(/^\\*\\*\\[(\\d+)\\]\\s+(.+?)\\*\\*$/);\n        if (!titleMatch) return null;\n        const [, number, title] = titleMatch;\n        // 初始化文献对象\n        const literature = {\n            id: parseInt(number),\n            title: title,\n            authors: [],\n            journal: '',\n            publication_year: new Date().getFullYear(),\n            category: 'unknown',\n            technology_tags: [],\n            application_tags: [],\n            citation_count: 0,\n            relevance_score: 0,\n            key_findings: '',\n            methodology_summary: '',\n            business_value: '',\n            abstract: '',\n            relevanceExplanation: '',\n            supportPoints: []\n        };\n        // 解析其他信息行\n        for(let i = 1; i < literatureInfo.length; i++){\n            const line = literatureInfo[i].trim();\n            // 作者和期刊信息 *作者等*\n            if (line.startsWith('*') && line.endsWith('*') && !line.includes('**')) {\n                const authorJournalMatch = line.match(/^\\*(.+?)\\*$/);\n                if (authorJournalMatch) {\n                    const authorJournalText = authorJournalMatch[1];\n                    const parts = authorJournalText.split('等');\n                    if (parts.length > 0) {\n                        literature.authors = parts[0].split(',').map((author)=>author.trim());\n                    }\n                }\n            } else if (line.startsWith('📖')) {\n                const journalMatch = line.match(/📖\\s+(.+?)\\s+\\((\\d{4})\\)/);\n                if (journalMatch) {\n                    literature.journal = journalMatch[1];\n                    literature.publication_year = parseInt(journalMatch[2]);\n                }\n            } else if (line.startsWith('🏆')) {\n                const ifMatch = line.match(/🏆\\s+影响因子:\\s*([\\d.]+)/);\n                if (ifMatch) {\n                    literature.impact_factor = parseFloat(ifMatch[1]);\n                }\n            } else if (line.startsWith('💡')) {\n                const relevanceMatch = line.match(/💡\\s+\\*\\*相关性\\*\\*:\\s*(.+)/);\n                if (relevanceMatch) {\n                    literature.relevanceExplanation = relevanceMatch[1];\n                }\n            } else if (line.startsWith('🔑')) {\n                const supportMatch = line.match(/🔑\\s+\\*\\*支持要点\\*\\*:\\s*(.+)/);\n                if (supportMatch) {\n                    literature.supportPoints.push(supportMatch[1]);\n                }\n            } else if (line.startsWith('🔗')) {\n                const doiMatch = line.match(/🔗\\s+DOI:\\s*(.+)/);\n                if (doiMatch) {\n                    literature.doi = doiMatch[1];\n                }\n            }\n        }\n        // 设置默认值\n        if (!literature.key_findings) {\n            literature.key_findings = \"\".concat(title, \"的重要研究发现\");\n        }\n        if (!literature.methodology_summary) {\n            literature.methodology_summary = '详细的方法学信息请查看原文';\n        }\n        if (!literature.business_value) {\n            literature.business_value = '为单细胞测序研究提供重要的理论和技术支持';\n        }\n        // 设置技术标签（基于标题推断）\n        const titleLower = title.toLowerCase();\n        if (titleLower.includes('single-cell') || titleLower.includes('scrna')) {\n            literature.technology_tags.push('scRNA-seq');\n        }\n        if (titleLower.includes('atac')) {\n            literature.technology_tags.push('scATAC-seq');\n        }\n        if (titleLower.includes('seurat')) {\n            literature.technology_tags.push('seurat');\n        }\n        if (titleLower.includes('10x')) {\n            literature.technology_tags.push('10x_genomics');\n        }\n        // 设置应用标签\n        if (titleLower.includes('immune') || titleLower.includes('dendritic')) {\n            literature.application_tags.push('immunology');\n        }\n        if (titleLower.includes('development')) {\n            literature.application_tags.push('development');\n        }\n        // 设置分类\n        if (titleLower.includes('integration') || titleLower.includes('method')) {\n            literature.category = 'methodology';\n        } else if (titleLower.includes('reveals') || titleLower.includes('analysis')) {\n            literature.category = 'application';\n        } else {\n            literature.category = 'technology';\n        }\n        // 估算引用数和相关性评分\n        if (literature.impact_factor) {\n            literature.citation_count = Math.round(literature.impact_factor * 100 + Math.random() * 1000);\n            literature.relevance_score = Math.min(0.95, literature.impact_factor / 50 + 0.5);\n        } else {\n            literature.citation_count = Math.round(Math.random() * 500 + 100);\n            literature.relevance_score = 0.7 + Math.random() * 0.2;\n        }\n        return literature;\n    } catch (error) {\n        console.error('解析文献信息失败:', error);\n        return null;\n    }\n}\nvar _c;\n$RefreshReg$(_c, \"FormattedMessage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/formatted-message.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/precision-literature-search.tsx":
/*!****************************************************!*\
  !*** ./components/precision-literature-search.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ExternalLink_Search_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ExternalLink,Search,Star,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ExternalLink_Search_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ExternalLink,Search,Star,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ExternalLink_Search_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ExternalLink,Search,Star,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ExternalLink_Search_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ExternalLink,Search,Star,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ExternalLink_Search_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ExternalLink,Search,Star,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ExternalLink_Search_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ExternalLink,Search,Star,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ExternalLink_Search_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ExternalLink,Search,Star,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst PrecisionLiteratureSearch = (param)=>{\n    let { literature_search } = param;\n    _s();\n    const [expandedPaper, setExpandedPaper] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showTips, setShowTips] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const searchPlatforms = [\n        {\n            name: 'PubMed',\n            icon: '🏥',\n            url: literature_search.precision_search_links.pubmed,\n            color: 'bg-blue-600 hover:bg-blue-700',\n            description: '医学文献数据库'\n        },\n        {\n            name: 'Google Scholar',\n            icon: '🎓',\n            url: literature_search.precision_search_links.google_scholar,\n            color: 'bg-green-600 hover:bg-green-700',\n            description: '学术搜索引擎'\n        },\n        {\n            name: 'Semantic Scholar',\n            icon: '🧠',\n            url: literature_search.precision_search_links.semantic_scholar,\n            color: 'bg-purple-600 hover:bg-purple-700',\n            description: 'AI驱动的学术搜索'\n        },\n        {\n            name: 'bioRxiv',\n            icon: '🧬',\n            url: literature_search.precision_search_links.biorxiv,\n            color: 'bg-orange-600 hover:bg-orange-700',\n            description: '生物学预印本'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-indigo-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"bg-gradient-to-r from-indigo-50 to-blue-50 border-b border-indigo-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2 text-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ExternalLink_Search_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-5 w-5 text-indigo-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"\\uD83C\\uDFAF 精准文献搜索\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 p-4 bg-slate-50 rounded-lg border border-slate-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ExternalLink_Search_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-5 w-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-slate-800 mb-1\",\n                                                    children: \"研究焦点\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-slate-600 bg-white px-3 py-2 rounded-lg border border-slate-200\",\n                                                    children: literature_search.research_focus\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-slate-700 mb-4 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ExternalLink_Search_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-4 w-4 text-yellow-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"一键直达相关文献\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-3 mb-4\",\n                                        children: searchPlatforms.map((platform, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: platform.url,\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"\".concat(platform.color, \" text-white rounded-lg p-4 transition-all hover:shadow-lg hover:scale-105 group\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl\",\n                                                            children: platform.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: platform.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                                    lineNumber: 128,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs opacity-90\",\n                                                                    children: platform.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                                    lineNumber: 129,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ExternalLink_Search_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-4 w-4 opacity-70 group-hover:opacity-100 transition-opacity\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t border-slate-200 pt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowTips(!showTips),\n                                                className: \"flex items-center gap-2 text-sm text-slate-600 hover:text-slate-800 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ExternalLink_Search_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4 transition-transform \".concat(showTips ? 'rotate-180' : '')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"搜索提示 (\",\n                                                    literature_search.search_tips.length,\n                                                    \"条)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            showTips && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-3 space-y-2\",\n                                                children: literature_search.search_tips.map((tip, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start gap-2 p-3 bg-blue-50 rounded-lg border border-blue-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-blue-600\",\n                                                                    children: index + 1\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                                    lineNumber: 152,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-blue-800\",\n                                                                children: tip\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                                lineNumber: 154,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-green-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"bg-gradient-to-r from-green-50 to-emerald-50 border-b border-green-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2 text-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ExternalLink_Search_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-5 w-5 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"\\uD83D\\uDCDA 推荐文献精选\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-slate-600 mt-1\",\n                                children: \"基于您的研究需求智能筛选的高质量文献\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"divide-y divide-slate-100\",\n                            children: literature_search.featured_papers.map((paper, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 hover:bg-slate-50 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-slate-800 leading-tight mb-2\",\n                                                        children: paper.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-4 text-sm text-slate-600 mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    paper.authors.slice(0, 3).join(', '),\n                                                                    paper.authors.length > 3 && ' et al.'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                                lineNumber: 182,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: paper.journal\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: paper.year\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                                lineNumber: 185,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3 p-3 bg-amber-50 border border-amber-200 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-amber-800\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"推荐理由：\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                                    lineNumber: 191,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                paper.why_relevant\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 flex-wrap\",\n                                                        children: [\n                                                            paper.direct_links.pubmed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: paper.direct_links.pubmed,\n                                                                target: \"_blank\",\n                                                                rel: \"noopener noreferrer\",\n                                                                className: \"inline-flex items-center gap-1 px-3 py-1 bg-blue-600 text-white text-xs rounded-full hover:bg-blue-700 transition-colors\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ExternalLink_Search_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                                        lineNumber: 200,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    \"PubMed\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                                lineNumber: 198,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            paper.direct_links.doi && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: paper.direct_links.doi,\n                                                                target: \"_blank\",\n                                                                rel: \"noopener noreferrer\",\n                                                                className: \"inline-flex items-center gap-1 px-3 py-1 bg-emerald-600 text-white text-xs rounded-full hover:bg-emerald-700 transition-colors\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ExternalLink_Search_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                                        lineNumber: 207,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    \"DOI\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            paper.direct_links.pdf && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: paper.direct_links.pdf,\n                                                                target: \"_blank\",\n                                                                rel: \"noopener noreferrer\",\n                                                                className: \"inline-flex items-center gap-1 px-3 py-1 bg-red-600 text-white text-xs rounded-full hover:bg-red-700 transition-colors\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ExternalLink_Search_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                                        lineNumber: 214,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    \"PDF\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                                lineNumber: 212,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            paper.direct_links.google_scholar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: paper.direct_links.google_scholar,\n                                                                target: \"_blank\",\n                                                                rel: \"noopener noreferrer\",\n                                                                className: \"inline-flex items-center gap-1 px-3 py-1 bg-green-600 text-white text-xs rounded-full hover:bg-green-700 transition-colors\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ExternalLink_Search_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                                        lineNumber: 221,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    \"Scholar\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-end gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-slate-500\",\n                                                                children: \"相关度\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                                lineNumber: 231,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-2 bg-slate-200 rounded-full overflow-hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-full bg-gradient-to-r from-green-400 to-green-600 rounded-full transition-all\",\n                                                                    style: {\n                                                                        width: \"\".concat(paper.relevance_score * 100, \"%\")\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-slate-700\",\n                                                                children: [\n                                                                    Math.round(paper.relevance_score * 100),\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                                lineNumber: 238,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: [\n                                                            ...Array(5)\n                                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ExternalLink_Search_Star_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-3 w-3 \".concat(i < Math.floor(paper.relevance_score * 5) ? 'text-yellow-400 fill-current' : 'text-slate-300')\n                                                            }, i, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 25\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\precision-literature-search.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PrecisionLiteratureSearch, \"RyD3HRIvNB1u10I0ZUCkR7LeF2Q=\");\n_c = PrecisionLiteratureSearch;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PrecisionLiteratureSearch);\nvar _c;\n$RefreshReg$(_c, \"PrecisionLiteratureSearch\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/precision-literature-search.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/research-intent-guess-panel.tsx":
/*!****************************************************!*\
  !*** ./components/research-intent-guess-panel.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Lightbulb_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Lightbulb,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Lightbulb_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Lightbulb,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Lightbulb_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Lightbulb,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Lightbulb_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Lightbulb,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Lightbulb_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Lightbulb,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst ResearchIntentGuessPanel = (param)=>{\n    let { research_intent_guess } = param;\n    const getConfidenceColor = (confidence)=>{\n        const level = parseInt(confidence.replace('%', ''));\n        if (level >= 90) return 'text-green-600 bg-green-50 border-green-200';\n        if (level >= 70) return 'text-blue-600 bg-blue-50 border-blue-200';\n        return 'text-amber-600 bg-amber-50 border-amber-200';\n    };\n    const getConfidenceIcon = (confidence)=>{\n        const level = parseInt(confidence.replace('%', ''));\n        if (level >= 90) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Lightbulb_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"h-4 w-4 text-green-600\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n            lineNumber: 35,\n            columnNumber: 29\n        }, undefined);\n        if (level >= 70) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Lightbulb_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"h-4 w-4 text-blue-600\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n            lineNumber: 36,\n            columnNumber: 29\n        }, undefined);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Lightbulb_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4 text-amber-600\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n            lineNumber: 37,\n            columnNumber: 12\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"border-purple-200\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                className: \"bg-gradient-to-r from-purple-50 to-indigo-50 border-b border-purple-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center gap-2 text-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Lightbulb_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-5 w-5 text-purple-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 11\n                            }, undefined),\n                            \"\\uD83C\\uDFAF 研究意图智能分析\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-slate-600 mt-1\",\n                        children: \"基于您的输入，AI分析您的研究目标和关注重点\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Lightbulb_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-slate-800\",\n                                        children: \"研究意图分析\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 bg-slate-50 rounded-lg border border-slate-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-700 leading-relaxed\",\n                                    children: research_intent_guess.primary_intent\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 bg-white rounded-lg border border-slate-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-slate-500 mb-1\",\n                                        children: \"研究方向\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold text-slate-800\",\n                                        children: research_intent_guess.research_direction\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 bg-white rounded-lg border border-slate-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-slate-500 mb-1\",\n                                        children: \"分析类型\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold text-slate-800\",\n                                        children: research_intent_guess.analysis_type\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-slate-700\",\n                                        children: \"分析置信度\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center gap-1 px-3 py-1 rounded-full border \".concat(getConfidenceColor(research_intent_guess.confidence_level)),\n                                        children: [\n                                            getConfidenceIcon(research_intent_guess.confidence_level),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: research_intent_guess.confidence_level\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full bg-slate-200 rounded-full h-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-purple-400 to-indigo-600 h-2 rounded-full transition-all\",\n                                    style: {\n                                        width: research_intent_guess.confidence_level\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, undefined),\n                    (research_intent_guess.key_interests || research_intent_guess.inferred_goals) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium text-slate-700 mb-3 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Lightbulb_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4 text-indigo-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"推测的研究重点\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: (research_intent_guess.key_interests || research_intent_guess.inferred_goals || []).map((interest, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-3 p-3 bg-indigo-50 rounded-lg border border-indigo-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-6 h-6 bg-indigo-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-medium text-indigo-600\",\n                                                    children: index + 1\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-indigo-800\",\n                                                    children: interest\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Lightbulb_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-3 w-3 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-blue-800\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"分析说明：\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 18\n                                            }, undefined),\n                                            \"基于您的输入信息，AI系统分析了您的研究背景、样本类型和目标，推测了最可能的研究意图。这有助于为您提供更精准的技术方案和文献推荐。\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\research-intent-guess-panel.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ResearchIntentGuessPanel;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResearchIntentGuessPanel);\nvar _c;\n$RefreshReg$(_c, \"ResearchIntentGuessPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/research-intent-guess-panel.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/streamlined-solution-display.tsx":
/*!*****************************************************!*\
  !*** ./components/streamlined-solution-display.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Info!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Info!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _enhanced_solution_overview_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./enhanced-solution-overview-card */ \"(app-pages-browser)/./components/enhanced-solution-overview-card.tsx\");\n/* harmony import */ var _research_intent_guess_panel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./research-intent-guess-panel */ \"(app-pages-browser)/./components/research-intent-guess-panel.tsx\");\n/* harmony import */ var _precision_literature_search__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./precision-literature-search */ \"(app-pages-browser)/./components/precision-literature-search.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst StreamlinedSolutionDisplay = (param)=>{\n    let { solutionData } = param;\n    if (!solutionData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.Alert, {\n            className: \"m-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.AlertDescription, {\n                    children: \"暂无方案数据，请先完成需求收集。\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-7xl mx-auto p-6 space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-gray-900 mb-2\",\n                        children: \"\\uD83E\\uDDEC 智能化单细胞测序解决方案\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"基于AI分析的个性化方案，包含研究意图分析、技术方案和精准文献推荐\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                variant: \"secondary\",\n                                className: \"bg-green-100 text-green-800 border-green-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"方案已生成\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                variant: \"outline\",\n                                children: [\n                                    \"方案ID: \",\n                                    solutionData.solution_id.slice(-8)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                variant: \"outline\",\n                                children: [\n                                    \"生成方式: \",\n                                    solutionData.generation_method === 'streamlined' ? '简化版' : '标准版'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined),\n            solutionData.agent_enhancement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.Alert, {\n                className: \"bg-blue-50 border-blue-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.AlertDescription, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"AI增强分析：\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, undefined),\n                            \"本方案采用了智能Agent技术进行深度文献分析和专业洞察， 置信度评分：\",\n                            Math.round(solutionData.agent_enhancement.confidence_score * 100),\n                            \"%\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                lineNumber: 112,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold text-gray-800 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold\",\n                                                children: \"1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"方案概览\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 ml-8\",\n                                        children: \"技术方案、流程安排和关键注意事项\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_enhanced_solution_overview_card__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                solution_overview: solutionData.solution_overview\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold text-gray-800 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-6 h-6 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold\",\n                                                children: \"2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"研究意图分析\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 ml-8\",\n                                        children: \"AI智能分析您的研究目标和关注重点\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_research_intent_guess_panel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                research_intent_guess: solutionData.research_intent_guess\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold text-gray-800 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold\",\n                                                children: \"3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"精准文献搜索\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 ml-8\",\n                                        children: \"一键直达相关文献，推荐高质量参考资料\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_precision_literature_search__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                literature_search: solutionData.literature_search\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-12 pt-8 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"方案生成时间：\",\n                                new Date(solutionData.generated_at).toLocaleString('zh-CN')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1\",\n                            children: \"如需调整方案或有疑问，请联系我们的技术专家团队\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\streamlined-solution-display.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, undefined);\n};\n_c = StreamlinedSolutionDisplay;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StreamlinedSolutionDisplay);\nvar _c;\n$RefreshReg$(_c, \"StreamlinedSolutionDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/streamlined-solution-display.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/circle-check.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CircleCheck)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst CircleCheck = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"CircleCheck\", [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m9 12 2 2 4-4\",\n            key: \"dzmm74\"\n        }\n    ]\n]);\n //# sourceMappingURL=circle-check.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-play.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/circle-play.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CirclePlay)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst CirclePlay = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"CirclePlay\", [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"polygon\",\n        {\n            points: \"10 8 16 12 10 16 10 8\",\n            key: \"1cimsy\"\n        }\n    ]\n]);\n //# sourceMappingURL=circle-play.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-play.js\n"));

/***/ })

});