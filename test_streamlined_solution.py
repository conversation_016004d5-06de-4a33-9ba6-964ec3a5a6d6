#!/usr/bin/env python3
"""
测试简化的3模块方案框架
验证新的streamlined_solution是否正常工作
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.solution_generator import get_solution_generator
import json

async def test_streamlined_solution():
    """测试简化方案生成"""
    
    # 测试用需求数据
    test_requirements = {
        "researchGoal": "细胞类型鉴定",
        "sampleType": "PBMC",
        "speciesType": "人类",
        "experimentType": "scRNA-seq",
        "cellCount": "5,000-10,000",
        "budget": "5-10万",
        "timeline": "1个月",
        "urgencyLevel": "常规",
        "cellViability": "> 90%",
        "sampleCondition": "新鲜"
    }
    
    user_message = "我想对人类PBMC进行单细胞RNA测序，主要目标是进行细胞类型鉴定，预算在5-10万元，希望1个月内完成。"
    
    print("🚀 开始测试简化的3模块方案框架...")
    print(f"测试需求: {test_requirements}")
    print(f"用户消息: {user_message}")
    print("-" * 50)
    
    try:
        # 获取方案生成器
        solution_generator = get_solution_generator()
        
        # 生成简化方案
        result = await solution_generator.generate_comprehensive_solution(
            requirements=test_requirements,
            user_message=user_message,
            enable_intelligent_agent=False  # 为了测试，暂时不使用Agent
        )
        
        print("✅ 方案生成成功!")
        print(f"方案ID: {result.get('solution_id')}")
        print(f"生成方法: {result.get('generation_method')}")
        print("-" * 50)
        
        # 检查3个核心模块
        print("📋 1. 方案概览模块:")
        solution_overview = result.get('solution_overview', {})
        print(f"  研究类型: {solution_overview.get('research_type')}")
        print(f"  预估成本: {solution_overview.get('estimated_cost')}")
        print(f"  推荐平台: {solution_overview.get('recommended_platform')}")
        print(f"  项目周期: {solution_overview.get('project_timeline')}")
        print(f"  流程步骤数: {len(solution_overview.get('key_process_steps', []))}")
        print(f"  注意事项数: {len(solution_overview.get('critical_considerations', []))}")
        print()
        
        print("🎯 2. 研究意图猜测模块:")
        intent_guess = result.get('research_intent_guess', {})
        print(f"  主要意图: {intent_guess.get('primary_intent', '')[:100]}...")
        print(f"  研究方向: {intent_guess.get('research_direction')}")
        print(f"  分析类型: {intent_guess.get('analysis_type')}")
        print(f"  置信度: {intent_guess.get('confidence_level')}")
        print()
        
        print("📚 3. 精准文献搜索模块:")
        literature_search = result.get('literature_search', {})
        print(f"  研究焦点: {literature_search.get('research_focus')}")
        precision_links = literature_search.get('precision_search_links', {})
        print(f"  PubMed链接: {precision_links.get('pubmed', '')[:80]}...")
        print(f"  Google Scholar链接: {precision_links.get('google_scholar', '')[:80]}...")
        print(f"  推荐文献数: {len(literature_search.get('featured_papers', []))}")
        print(f"  搜索提示数: {len(literature_search.get('search_tips', []))}")
        print()
        
        # 输出完整的JSON格式（用于前端测试）
        print("🔧 完整方案JSON (用于前端测试):")
        print("```json")
        print(json.dumps({
            "type": "streamlined_solution",
            "data": result
        }, ensure_ascii=False, indent=2))
        print("```")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_streamlined_solution())
    if success:
        print("\n🎉 测试完成！新的简化方案框架工作正常。")
    else:
        print("\n💥 测试失败，请检查错误信息。")