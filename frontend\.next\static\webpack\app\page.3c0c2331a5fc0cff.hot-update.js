"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/conversation-interface.tsx":
/*!***********************************************!*\
  !*** ./components/conversation-interface.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConversationInterface: () => (/* binding */ ConversationInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/paperclip.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-list.js\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./contexts/auth-context.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/enhanced-error-handler */ \"(app-pages-browser)/./lib/enhanced-error-handler.ts\");\n/* harmony import */ var _enhanced_loading_indicator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./enhanced-loading-indicator */ \"(app-pages-browser)/./components/enhanced-loading-indicator.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _progressive_requirement_collector__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./progressive-requirement-collector */ \"(app-pages-browser)/./components/progressive-requirement-collector.tsx\");\n/* harmony import */ var _formatted_message__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./formatted-message */ \"(app-pages-browser)/./components/formatted-message.tsx\");\n/* harmony import */ var _conversation_history__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./conversation-history */ \"(app-pages-browser)/./components/conversation-history.tsx\");\n/* harmony import */ var _literature_search_toggle__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./literature-search-toggle */ \"(app-pages-browser)/./components/literature-search-toggle.tsx\");\n/* __next_internal_client_entry_do_not_use__ ConversationInterface auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ConversationInterface() {\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [conversationId, setConversationId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [requirements, setRequirements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rightPanelCollapsed, setRightPanelCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHistory, setShowHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentSessionId, setCurrentSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [resetTrigger, setResetTrigger] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0) // 用于触发需求收集助手重置\n    ;\n    const [enableLiteratureSearch, setEnableLiteratureSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false) // 文献搜索开关\n    ;\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { user } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    // 使用增强的加载状态管理\n    const { isLoading, currentStage, progress, startLoading, updateStage, finishLoading, cancelLoading } = (0,_enhanced_loading_indicator__WEBPACK_IMPORTED_MODULE_9__.useStageLoading)();\n    // 响应式检测\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConversationInterface.useEffect\": ()=>{\n            const checkMobile = {\n                \"ConversationInterface.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 1024);\n                    if (window.innerWidth < 1024) {\n                        setRightPanelCollapsed(true);\n                    }\n                }\n            }[\"ConversationInterface.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"ConversationInterface.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"ConversationInterface.useEffect\"];\n        }\n    }[\"ConversationInterface.useEffect\"], []);\n    // 滚动到底部\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConversationInterface.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ConversationInterface.useEffect\"], [\n        messages\n    ]);\n    // 初始化欢迎消息\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConversationInterface.useEffect\": ()=>{\n            const welcomeMessage = {\n                id: \"welcome\",\n                type: \"ai\",\n                content: \"您好\".concat((user === null || user === void 0 ? void 0 : user.name) ? \"，\".concat(user.name) : '', \"！\\uD83D\\uDC4B\\n\\n我是 **CellForge AI** 智能顾问，专注于单细胞测序技术咨询。\\n\\n\\uD83D\\uDD2C **我的专长**：\\n• 单细胞RNA测序 (scRNA-seq)\\n• 单细胞ATAC测序 (scATAC-seq)\\n• 多组学测序 (Multiome)\\n• 空间转录组学\\n• 实验设计与数据分析\\n\\n\\uD83D\\uDCA1 **开始方式**：\\n您可以直接提问，我会智能收集您的需求信息，或者使用右侧的需求收集助手快速填写项目信息。\\n\\n请告诉我您的研究目标，我来为您制定最佳的技术方案！\"),\n                timestamp: new Date(),\n                status: \"read\",\n                confidence: 1.0,\n                suggestions: [\n                    \"我需要进行单细胞RNA测序\",\n                    \"请推荐适合的技术平台\",\n                    \"帮我分析项目成本\",\n                    \"查看成功案例\"\n                ]\n            };\n            setMessages([\n                welcomeMessage\n            ]);\n        }\n    }[\"ConversationInterface.useEffect\"], [\n        user\n    ]);\n    // 自动保存对话到历史\n    const autoSaveConversation = (messages)=>{\n        if (messages.length >= 2) {\n            try {\n                var _existingSessions_find, _messages_;\n                const storageKey = \"cellforge_conversations_\".concat((user === null || user === void 0 ? void 0 : user.id) || 'anonymous');\n                const existingSessions = JSON.parse(localStorage.getItem(storageKey) || '[]');\n                // 生成对话标题\n                const generateTitle = (msgs)=>{\n                    const firstUserMessage = msgs.find((m)=>m.type === \"user\");\n                    if (firstUserMessage) {\n                        const content = firstUserMessage.content.trim();\n                        return content.length > 30 ? content.substring(0, 30) + \"...\" : content;\n                    }\n                    return \"新对话\";\n                };\n                const sessionId = currentSessionId || \"session_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n                const now = new Date();\n                const session = {\n                    id: sessionId,\n                    title: generateTitle(messages),\n                    messages,\n                    createdAt: currentSessionId ? ((_existingSessions_find = existingSessions.find((s)=>s.id === sessionId)) === null || _existingSessions_find === void 0 ? void 0 : _existingSessions_find.createdAt) || now : now,\n                    updatedAt: now,\n                    isStarred: false,\n                    isArchived: false,\n                    tags: [],\n                    messageCount: messages.length,\n                    lastMessage: ((_messages_ = messages[messages.length - 1]) === null || _messages_ === void 0 ? void 0 : _messages_.content.substring(0, 100)) || \"\"\n                };\n                // 更新或添加会话\n                const updatedSessions = currentSessionId ? existingSessions.map((s)=>s.id === sessionId ? session : s) : [\n                    session,\n                    ...existingSessions\n                ].slice(0, 50) // 最多保存50个对话\n                ;\n                localStorage.setItem(storageKey, JSON.stringify(updatedSessions));\n                setCurrentSessionId(sessionId);\n            } catch (error) {\n                console.error('自动保存对话失败:', error);\n            }\n        }\n    };\n    // 处理表单提交 - 使用增强的错误处理和分阶段加载\n    const handleSendMessage = async ()=>{\n        if (!inputValue.trim()) return;\n        const userMessage = inputValue.trim();\n        setInputValue(\"\");\n        setError(\"\");\n        // 创建用户消息\n        const newUserMessage = {\n            id: Date.now().toString(),\n            type: \"user\",\n            content: userMessage,\n            timestamp: new Date(),\n            status: \"sending\"\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newUserMessage\n            ]);\n        // 构建对话历史和上下文（在分阶段操作外部准备）\n        const conversationHistory = messages.map((msg)=>({\n                role: msg.type === \"user\" ? \"user\" : \"assistant\",\n                content: msg.content\n            }));\n        const enhancedContext = {\n            user_profile: {\n                id: user === null || user === void 0 ? void 0 : user.id,\n                organization: (user === null || user === void 0 ? void 0 : user.organization) || \"\",\n                role: (user === null || user === void 0 ? void 0 : user.role) || \"\",\n                expertise: (user === null || user === void 0 ? void 0 : user.expertise_areas) || \"\",\n                research_interests: (user === null || user === void 0 ? void 0 : user.research_interests) || \"\"\n            },\n            requirements: requirements,\n            conversation_history: conversationHistory,\n            enable_literature_search: enableLiteratureSearch\n        };\n        try {\n            // 使用分阶段加载处理\n            let aiApiResponse;\n            await _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.errorHandler.handleStagedOperation([\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.ANALYZING,\n                    operation: async ()=>{\n                        // 模拟分析阶段\n                        await new Promise((resolve)=>setTimeout(resolve, 800));\n                        return {\n                            step: \"analyzed\"\n                        };\n                    }\n                },\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.SEARCHING_LITERATURE,\n                    operation: async ()=>{\n                        // 模拟文献搜索\n                        await new Promise((resolve)=>setTimeout(resolve, 1200));\n                        return {\n                            step: \"literature_searched\"\n                        };\n                    }\n                },\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.GENERATING_RESPONSE,\n                    operation: async ()=>{\n                        // 实际调用AI API\n                        aiApiResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.conversationApi.sendMessage({\n                            message: userMessage,\n                            conversation_id: conversationId || undefined,\n                            conversation_type: \"enhanced\",\n                            history: conversationHistory,\n                            context: enhancedContext\n                        });\n                        return aiApiResponse;\n                    }\n                },\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.FORMATTING,\n                    operation: async ()=>{\n                        // 模拟格式化处理\n                        await new Promise((resolve)=>setTimeout(resolve, 500));\n                        return {\n                            step: \"formatted\"\n                        };\n                    }\n                }\n            ], (stage, progress)=>{\n                if (!isLoading) {\n                    startLoading(stage);\n                } else {\n                    updateStage(stage, progress);\n                }\n            }, {\n                maxRetries: 1,\n                retryDelay: 3000,\n                userFriendlyMessage: \"AI服务暂时不可用，请稍后重试\",\n                showToast: false,\n                onRetry: (attempt, error)=>{\n                    var _error_message, _error_message1;\n                    // 只在网络错误时才显示重试信息\n                    if (((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('fetch')) || ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('网络'))) {\n                        sonner__WEBPACK_IMPORTED_MODULE_10__.toast.info(\"检测到网络问题，正在重试...\");\n                    }\n                }\n            });\n            // aiApiResponse已经在GENERATING_RESPONSE阶段中设置\n            // 更新用户消息状态为已发送\n            setMessages((prev)=>prev.map((msg)=>msg.id === newUserMessage.id ? {\n                        ...msg,\n                        status: \"sent\"\n                    } : msg));\n            // 创建AI回复消息\n            const aiResponse = {\n                id: (Date.now() + 1).toString(),\n                type: \"ai\",\n                content: aiApiResponse.message,\n                timestamp: new Date(),\n                status: \"read\",\n                confidence: aiApiResponse.confidence,\n                sources: aiApiResponse.sources,\n                suggestions: aiApiResponse.suggestions\n            };\n            setMessages((prev)=>{\n                const newMessages = [\n                    ...prev,\n                    aiResponse\n                ];\n                // 自动保存对话\n                setTimeout(()=>autoSaveConversation(newMessages), 1000);\n                return newMessages;\n            });\n            finishLoading();\n        } catch (err) {\n            console.error(\"发送消息失败:\", err);\n            // 更新用户消息状态为错误\n            setMessages((prev)=>prev.map((msg)=>msg.id === newUserMessage.id ? {\n                        ...msg,\n                        status: \"error\"\n                    } : msg));\n            // 错误已经由errorHandler处理，这里只需要更新UI状态\n            setError(\"发送消息失败，请稍后重试\");\n            cancelLoading();\n        }\n    };\n    // 处理需求变更\n    const handleRequirementsChange = (newRequirements)=>{\n        setRequirements(newRequirements);\n    };\n    // 处理需求提交 - 使用新的方案生成API\n    const handleRequirementsSubmit = async (requirements)=>{\n        // 生成需求总结消息\n        const requirementSummary = generateRequirementSummary(requirements);\n        // 添加用户消息到对话中\n        const userMessage = {\n            id: Date.now().toString(),\n            type: \"user\",\n            content: requirementSummary,\n            timestamp: new Date(),\n            status: \"sending\"\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        try {\n            var _solutionResponse_ai_analysis_features, _solutionResponse_solution_id, _solutionResponse_ai_analysis_features1;\n            // 使用分阶段加载处理需求提交，但现在专注于方案生成\n            let solutionResponse;\n            let literatureResults = null;\n            await _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.errorHandler.handleStagedOperation([\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.ANALYZING,\n                    operation: async ()=>{\n                        // 验证需求完整性\n                        const validation = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.solutionApi.validateRequirements(requirements);\n                        return validation;\n                    }\n                },\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.SEARCHING_LITERATURE,\n                    operation: async ()=>{\n                        // 如果启用了文献搜索，先执行文献搜索获得可见的结果\n                        if (enableLiteratureSearch) {\n                            try {\n                                // 调用智能文献搜索API获得用户可见的搜索过程\n                                const literatureResult = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.literatureApi.enhancedLiteratureSearch({\n                                    query: \"单细胞测序 \".concat(requirements.researchGoal, \" \").concat(requirements.sampleType),\n                                    requirements: requirements,\n                                    enable_literature_search: true\n                                });\n                                // 保存文献搜索结果以便后续使用\n                                literatureResults = literatureResult;\n                                return {\n                                    step: \"literature_evidence_collected\",\n                                    literature_results: literatureResult,\n                                    search_visible: true\n                                };\n                            } catch (error) {\n                                console.warn(\"文献搜索失败，继续方案生成:\", error);\n                                // 搜索失败也继续流程，在方案生成时会有内部文献支撑\n                                return {\n                                    step: \"literature_fallback_mode\"\n                                };\n                            }\n                        } else {\n                            // 文献搜索在后端内部进行（用户不可见）\n                            await new Promise((resolve)=>setTimeout(resolve, 800));\n                            return {\n                                step: \"literature_evidence_collected\",\n                                search_visible: false\n                            };\n                        }\n                    }\n                },\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.GENERATING_RESPONSE,\n                    operation: async ()=>{\n                        // 优先调用综合方案框架API（按设计文档要求）\n                        try {\n                            console.log('🚀 开始调用综合方案框架API...');\n                            console.log('请求数据:', {\n                                requirements: requirements,\n                                user_message: requirementSummary,\n                                framework_template: 'standard',\n                                enable_literature_search: enableLiteratureSearch\n                            });\n                            const frameworkResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.solutionApi.generateComprehensiveFramework({\n                                requirements: requirements,\n                                user_message: requirementSummary,\n                                framework_template: 'standard',\n                                enable_literature_search: enableLiteratureSearch\n                            });\n                            console.log('✅ 综合方案框架API响应:', frameworkResponse);\n                            if (frameworkResponse.success) {\n                                var _frameworkResponse_data_solution_overview, _frameworkResponse_data_implementation_plan;\n                                // 将综合方案框架结果转换为前端需要的格式\n                                solutionResponse = {\n                                    solution_id: frameworkResponse.framework_id,\n                                    generated_at: frameworkResponse.generation_time,\n                                    client_requirements: requirements,\n                                    comprehensive_framework: frameworkResponse.data,\n                                    recommended_solution: {\n                                        platform: \"单细胞测序解决方案\",\n                                        reasoning: \"基于设计文档的完整方案框架，包含方案概览、研究意图分析、关键要素、文献推荐、平台对比和实施规划\",\n                                        specifications: frameworkResponse.data.solution_overview\n                                    },\n                                    cost_analysis: ((_frameworkResponse_data_solution_overview = frameworkResponse.data.solution_overview) === null || _frameworkResponse_data_solution_overview === void 0 ? void 0 : _frameworkResponse_data_solution_overview.cost_analysis) || {},\n                                    risk_assessment: frameworkResponse.data.risk_assessment || {},\n                                    timeline: ((_frameworkResponse_data_implementation_plan = frameworkResponse.data.implementation_plan) === null || _frameworkResponse_data_implementation_plan === void 0 ? void 0 : _frameworkResponse_data_implementation_plan.timeline) || {},\n                                    deliverables: [\n                                        \"方案概览卡片\",\n                                        \"研究意图精准搜索链接\",\n                                        \"关键要素分析\",\n                                        \"智能文献推荐\",\n                                        \"平台对比分析\",\n                                        \"项目实施规划\"\n                                    ],\n                                    next_steps: [\n                                        \"查看完整的方案框架分析\",\n                                        \"使用精准搜索链接获取文献\",\n                                        \"根据平台对比选择技术方案\"\n                                    ],\n                                    contact_info: {\n                                        email: \"<EMAIL>\",\n                                        phone: \"************\"\n                                    },\n                                    framework_features: {\n                                        comprehensive_framework: true,\n                                        research_intent_analysis: true,\n                                        precision_search_links: true,\n                                        literature_recommendations: true,\n                                        platform_comparison: true\n                                    }\n                                };\n                            } else {\n                                throw new Error('综合方案框架API调用失败');\n                            }\n                        } catch (frameworkError) {\n                            console.error('❌ 综合方案框架API失败:', frameworkError);\n                            console.error('错误详情:', {\n                                message: frameworkError.message,\n                                stack: frameworkError.stack,\n                                name: frameworkError.name\n                            });\n                            // 使用简化的降级方案\n                            solutionResponse = {\n                                solution_id: \"fallback_\".concat(Date.now()),\n                                generated_at: new Date().toISOString(),\n                                client_requirements: requirements,\n                                recommended_solution: {\n                                    platform: \"CellForge AI 基础方案\",\n                                    reasoning: \"由于技术原因，当前提供基础方案。我们的专家将为您进行人工分析，确保方案的准确性和个性化。\",\n                                    specifications: {}\n                                },\n                                cost_analysis: {},\n                                risk_assessment: {},\n                                timeline: {},\n                                deliverables: [\n                                    \"人工专家分析报告\",\n                                    \"个性化技术方案设计\",\n                                    \"详细成本效益分析\"\n                                ],\n                                next_steps: [\n                                    \"联系专家进行人工分析\",\n                                    \"获取个性化方案设计\",\n                                    \"确定最优技术路线\"\n                                ],\n                                contact_info: {\n                                    email: \"<EMAIL>\",\n                                    phone: \"************\"\n                                },\n                                fallback_notice: {\n                                    message: \"为确保方案质量，建议联系专家进行人工分析\",\n                                    expert_contact: true,\n                                    reason: \"技术服务暂时不可用\"\n                                }\n                            };\n                        }\n                        return solutionResponse;\n                    }\n                },\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.FORMATTING,\n                    operation: async ()=>{\n                        // 格式化方案内容，如果有文献结果则包含进去\n                        await new Promise((resolve)=>setTimeout(resolve, 400));\n                        return {\n                            step: \"solution_formatted\"\n                        };\n                    }\n                }\n            ], (stage, progress)=>{\n                if (!isLoading) {\n                    startLoading(stage);\n                } else {\n                    updateStage(stage, progress);\n                }\n            }, {\n                maxRetries: 1,\n                retryDelay: 3000,\n                userFriendlyMessage: \"方案生成失败，请稍后重试\",\n                showToast: false,\n                onRetry: (attempt, error)=>{\n                    var _error_message, _error_message1;\n                    if (((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('fetch')) || ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('网络'))) {\n                        sonner__WEBPACK_IMPORTED_MODULE_10__.toast.info(\"网络问题，正在重试生成方案...\");\n                    }\n                }\n            });\n            // 更新用户消息状态\n            setMessages((prev)=>prev.map((msg)=>msg.id === userMessage.id ? {\n                        ...msg,\n                        status: \"sent\"\n                    } : msg));\n            // 生成方案展示的AI回复，包含文献搜索结果（如果有）\n            const solutionContent = formatSolutionResponse(solutionResponse, literatureResults);\n            // 更新sources以反映实际的数据来源\n            let sources = [\n                \"方案生成系统\",\n                \"技术知识库\",\n                \"成本分析引擎\"\n            ];\n            // 根据不同的方案类型使用不同的数据来源\n            if ((_solutionResponse_ai_analysis_features = solutionResponse.ai_analysis_features) === null || _solutionResponse_ai_analysis_features === void 0 ? void 0 : _solutionResponse_ai_analysis_features.intent_analyzed) {\n                sources = [\n                    \"AI意图分析引擎\",\n                    \"用户行为分析\",\n                    \"个性化算法\",\n                    \"专业知识库\"\n                ];\n                if (solutionResponse.ai_analysis_features.analysis_sources) {\n                    sources = sources.concat(solutionResponse.ai_analysis_features.analysis_sources);\n                }\n            } else if (((_solutionResponse_solution_id = solutionResponse.solution_id) === null || _solutionResponse_solution_id === void 0 ? void 0 : _solutionResponse_solution_id.includes('intelligent')) || solutionResponse.intelligent_features) {\n                sources = [\n                    \"AI智能推荐引擎\",\n                    \"专业知识库\",\n                    \"热点文献发现\",\n                    \"技术规格数据库\",\n                    \"风险评估算法\"\n                ];\n            } else if (solutionResponse.fallback_notice) {\n                sources = [\n                    \"基础知识库\",\n                    \"专家经验库\",\n                    \"标准方案模板\"\n                ];\n            }\n            if (literatureResults && enableLiteratureSearch) {\n                sources.push(\"文献数据库\", \"智能搜索引擎\");\n            }\n            const aiResponse = {\n                id: (Date.now() + 1).toString(),\n                type: \"ai\",\n                content: solutionContent,\n                timestamp: new Date(),\n                status: \"read\",\n                confidence: ((_solutionResponse_ai_analysis_features1 = solutionResponse.ai_analysis_features) === null || _solutionResponse_ai_analysis_features1 === void 0 ? void 0 : _solutionResponse_ai_analysis_features1.confidence_score) || (literatureResults ? 0.98 : solutionResponse.fallback_notice ? 0.70 : 0.95),\n                sources: sources,\n                suggestions: [\n                    \"查看详细的成本分析\",\n                    \"了解项目时间规划\",\n                    \"获取技术平台对比\",\n                    \"联系专家进行咨询\"\n                ]\n            };\n            setMessages((prev)=>{\n                const newMessages = [\n                    ...prev,\n                    aiResponse\n                ];\n                // 自动保存对话\n                setTimeout(()=>autoSaveConversation(newMessages), 1000);\n                return newMessages;\n            });\n            finishLoading();\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"已基于您的需求生成专业方案！\");\n        } catch (err) {\n            console.error(\"生成建议失败:\", err);\n            // 更新用户消息状态为错误\n            setMessages((prev)=>prev.map((msg)=>msg.id === userMessage.id ? {\n                        ...msg,\n                        status: \"error\"\n                    } : msg));\n            cancelLoading();\n        // 错误已经由errorHandler处理\n        }\n    };\n    // 生成需求总结 - 更新以匹配新的字段结构\n    const generateRequirementSummary = (reqs)=>{\n        const sections = [];\n        sections.push(\"📋 **项目需求总结**\\n\");\n        // 基础分类信息\n        if (reqs.speciesType) sections.push(\"\\uD83E\\uDDEC **物种类型**: \".concat(reqs.speciesType));\n        if (reqs.experimentType) sections.push(\"\\uD83D\\uDD2C **实验类型**: \".concat(reqs.experimentType));\n        if (reqs.researchGoal) sections.push(\"\\uD83C\\uDFAF **研究目标**: \".concat(reqs.researchGoal));\n        // 样本信息\n        if (reqs.sampleType || reqs.sampleCount || reqs.sampleCondition || reqs.cellCount) {\n            sections.push(\"\\n🧪 **样本信息**\");\n            if (reqs.sampleType) sections.push(\"• 样本类型: \".concat(reqs.sampleType));\n            if (reqs.sampleCount) sections.push(\"• 样本数目: \".concat(reqs.sampleCount));\n            if (reqs.sampleCondition) sections.push(\"• 样本状态: \".concat(reqs.sampleCondition));\n            if (reqs.sampleProcessing) sections.push(\"• 处理方式: \".concat(reqs.sampleProcessing));\n            if (reqs.cellCount) sections.push(\"• 细胞数量: \".concat(reqs.cellCount));\n            if (reqs.cellViability) sections.push(\"• 细胞活力: \".concat(reqs.cellViability));\n        }\n        // 项目规划\n        if (reqs.budget || reqs.timeline || reqs.urgencyLevel) {\n            sections.push(\"\\n📅 **项目规划**\");\n            if (reqs.budget) sections.push(\"• 预算范围: \".concat(reqs.budget));\n            if (reqs.timeline) sections.push(\"• 项目周期: \".concat(reqs.timeline));\n            if (reqs.urgencyLevel) sections.push(\"• 紧急程度: \".concat(reqs.urgencyLevel));\n        }\n        // 技术细节\n        if (reqs.sequencingDepth || reqs.analysisType || reqs.dataAnalysisNeeds || reqs.needsCellSorting || reqs.specialRequirements) {\n            sections.push(\"\\n⚙️ **技术细节**\");\n            if (reqs.sequencingDepth) sections.push(\"• 测序深度: \".concat(reqs.sequencingDepth));\n            if (reqs.analysisType) sections.push(\"• 分析类型: \".concat(reqs.analysisType));\n            if (reqs.dataAnalysisNeeds) sections.push(\"• 数据分析需求: \".concat(reqs.dataAnalysisNeeds));\n            if (reqs.needsCellSorting) sections.push(\"• 细胞分选: \".concat(reqs.needsCellSorting));\n            if (reqs.specialRequirements) sections.push(\"• 特殊要求: \".concat(reqs.specialRequirements));\n        }\n        sections.push(\"\\n请为我生成详细的技术方案建议。\");\n        return sections.join(\"\\n\");\n    };\n    // 格式化方案响应，包含文献搜索结果和个性化信息\n    const formatSolutionResponse = (solutionResponse, literatureResults)=>{\n        var _literatureResults_literature_results;\n        if (!solutionResponse) return \"抱歉，方案生成失败。\";\n        const { solution_id, client_requirements, recommended_solution, cost_analysis, risk_assessment, timeline, deliverables, next_steps, contact_info, intelligent_features, intent_analysis, research_domain, personalization_level, ai_analysis_features, fallback_notice } = solutionResponse;\n        const sections = [];\n        // 检测是否为个性化推荐或AI分析结果\n        const isPersonalized = personalization_level === \"high\" || intent_analysis;\n        const isIntelligentRecommendation = (solution_id === null || solution_id === void 0 ? void 0 : solution_id.includes('intelligent')) || intelligent_features;\n        const isAiAnalyzed = (ai_analysis_features === null || ai_analysis_features === void 0 ? void 0 : ai_analysis_features.intent_analyzed) || (solution_id === null || solution_id === void 0 ? void 0 : solution_id.includes('ai_analyzed'));\n        const isFallback = fallback_notice || (solution_id === null || solution_id === void 0 ? void 0 : solution_id.includes('fallback'));\n        // 根据个性化程度和研究领域定制标题\n        if (isFallback) {\n            sections.push(\"⚠️ **CellForge AI 基础方案**\");\n            sections.push(\"⏰ 系统正在升级，专家人工分析中...\");\n            sections.push(\"方案编号: \".concat(solution_id));\n            sections.push(\"\");\n            // 添加回退通知\n            if (fallback_notice) {\n                sections.push(\"📢 **重要提示**\");\n                sections.push(\"• \".concat(fallback_notice.message));\n                if (fallback_notice.expert_contact) {\n                    sections.push(\"• 专家团队将在2小时内与您联系\");\n                }\n                sections.push(\"• 原因: \".concat(fallback_notice.reason));\n                sections.push(\"\");\n            }\n        } else if (isAiAnalyzed && ai_analysis_features) {\n            var _ai_analysis_features_analysis_sources;\n            sections.push(\"🧠 **CellForge AI 智能意图分析方案**\");\n            sections.push(\"✨ 基于深度用户意图分析的个性化推荐\");\n            sections.push(\"方案编号: \".concat(solution_id));\n            sections.push(\"\");\n            // 添加AI分析摘要\n            sections.push(\"🔍 **AI意图分析结果**\");\n            sections.push(\"• 分析置信度：\".concat(Math.round((ai_analysis_features.confidence_score || 0.85) * 100), \"%\"));\n            if (((_ai_analysis_features_analysis_sources = ai_analysis_features.analysis_sources) === null || _ai_analysis_features_analysis_sources === void 0 ? void 0 : _ai_analysis_features_analysis_sources.length) > 0) {\n                sections.push(\"• 分析来源：\".concat(ai_analysis_features.analysis_sources.join(\", \")));\n            }\n            sections.push(\"• 个性化程度：高度定制\");\n            sections.push(\"\");\n        } else if (isPersonalized && research_domain) {\n            sections.push(\"\\uD83C\\uDFAF **CellForge AI \".concat(research_domain, \"专业方案**\"));\n            sections.push(\"✨ 基于意图分析的个性化推荐\");\n            sections.push(\"方案编号: \".concat(solution_id));\n            sections.push(\"\");\n            // 添加意图分析摘要\n            if (intent_analysis) {\n                sections.push(\"🧠 **用户意图分析**\");\n                sections.push(\"• 研究领域：\".concat(intent_analysis.research_domain || '通用研究'));\n                sections.push(\"• 预算类别：\".concat(intent_analysis.budget_category || '标准型'));\n                sections.push(\"• 紧急程度：\".concat(intent_analysis.urgency_level || '常规'));\n                sections.push(\"• 技术偏好：\".concat(intent_analysis.technical_preference || '10x Genomics'));\n                sections.push(\"• 置信度：\".concat(Math.round((intent_analysis.confidence_score || 0.85) * 100), \"%\"));\n                sections.push(\"\");\n            }\n        } else if (isIntelligentRecommendation) {\n            sections.push(\"🎯 **CellForge AI 智能推荐方案**\");\n            sections.push(\"方案编号: \".concat(solution_id));\n            sections.push(\"\");\n        } else {\n            sections.push(\"🎯 **CellForge AI 专业方案**\");\n            sections.push(\"方案编号: \".concat(solution_id));\n            sections.push(\"\");\n        }\n        // 风险评估 - 支持新格式和旧格式\n        if (risk_assessment) {\n            var _risk_assessment_technical_risks, _risk_assessment_identified_risks;\n            sections.push(\"⚠️ **风险评估**\");\n            if (risk_assessment.success_probability) {\n                sections.push(\"**成功概率**: \".concat(risk_assessment.success_probability));\n            }\n            if (risk_assessment.overall_risk_level) {\n                sections.push(\"**整体风险等级**: \".concat(risk_assessment.overall_risk_level));\n            }\n            // 新格式技术风险\n            if (((_risk_assessment_technical_risks = risk_assessment.technical_risks) === null || _risk_assessment_technical_risks === void 0 ? void 0 : _risk_assessment_technical_risks.length) > 0) {\n                sections.push(\"**主要风险**:\");\n                risk_assessment.technical_risks.forEach((risk)=>{\n                    if (typeof risk === 'object') {\n                        sections.push(\"• \".concat(risk.risk, \" (\").concat(risk.probability, \")\"));\n                        sections.push(\"  影响: \".concat(risk.impact));\n                        sections.push(\"  缓解: \".concat(risk.mitigation));\n                    } else {\n                        sections.push(\"• \".concat(risk));\n                    }\n                });\n            } else if (((_risk_assessment_identified_risks = risk_assessment.identified_risks) === null || _risk_assessment_identified_risks === void 0 ? void 0 : _risk_assessment_identified_risks.length) > 0) {\n                sections.push(\"**主要风险**:\");\n                risk_assessment.identified_risks.forEach((risk)=>{\n                    sections.push(\"• \".concat(risk));\n                });\n            }\n            sections.push(\"\");\n        }\n        // 添加智能推荐特性（如果是智能推荐）\n        if (intelligent_features) {\n            var _intelligent_features_hot_papers, _intelligent_features_literature_recommendations_combined_results, _intelligent_features_literature_recommendations, _intelligent_features_smart_insights, _intelligent_features_collaboration_opportunities;\n            // 智能文献推荐 - 修复数据结构访问\n            if (((_intelligent_features_hot_papers = intelligent_features.hot_papers) === null || _intelligent_features_hot_papers === void 0 ? void 0 : _intelligent_features_hot_papers.length) > 0) {\n                sections.push(\"🧬 **智能文献推荐报告**\");\n                sections.push(\"\");\n                sections.push(\"🔥 **热点文献发现**:\");\n                intelligent_features.hot_papers.forEach((paper, index)=>{\n                    const pubmedLink = paper.doi ? \"https://pubmed.ncbi.nlm.nih.gov/?term=\".concat(encodeURIComponent(paper.doi), \"[DOI]\") : \"https://pubmed.ncbi.nlm.nih.gov/?term=\".concat(encodeURIComponent(paper.title));\n                    const scholarLink = \"https://scholar.google.com/scholar?q=\".concat(encodeURIComponent(paper.title));\n                    sections.push(\"**[\".concat(index + 1, \"] \").concat(paper.title, \"**\"));\n                    sections.push(\"\\uD83D\\uDCD6 \".concat(paper.journal, \" \").concat(paper.impact_factor ? \"(IF: \".concat(paper.impact_factor, \")\") : ''));\n                    sections.push(\"\\uD83D\\uDCA1 \".concat(paper.reason));\n                    sections.push(\"\\uD83D\\uDD17 [PubMed](\".concat(pubmedLink, \") | [Google Scholar](\").concat(scholarLink, \")\"));\n                    sections.push(\"\");\n                });\n            }\n            // 智能关键词扩展 - 修复数据结构访问并添加搜索链接\n            if (intelligent_features.expanded_keywords) {\n                var _keywords_semantic_expansion, _keywords_trending_terms;\n                const keywords = intelligent_features.expanded_keywords;\n                if (((_keywords_semantic_expansion = keywords.semantic_expansion) === null || _keywords_semantic_expansion === void 0 ? void 0 : _keywords_semantic_expansion.length) > 0 || ((_keywords_trending_terms = keywords.trending_terms) === null || _keywords_trending_terms === void 0 ? void 0 : _keywords_trending_terms.length) > 0) {\n                    var _keywords_semantic_expansion1, _keywords_trending_terms1, _keywords_molecular_targets;\n                    sections.push(\"🔍 **智能关键词扩展与搜索链接**:\");\n                    sections.push(\"\");\n                    if (((_keywords_semantic_expansion1 = keywords.semantic_expansion) === null || _keywords_semantic_expansion1 === void 0 ? void 0 : _keywords_semantic_expansion1.length) > 0) {\n                        sections.push(\"**语义扩展关键词**:\");\n                        keywords.semantic_expansion.slice(0, 5).forEach((keyword)=>{\n                            const pubmedLink = \"https://pubmed.ncbi.nlm.nih.gov/?term=\".concat(encodeURIComponent(keyword));\n                            const scholarLink = \"https://scholar.google.com/scholar?q=\".concat(encodeURIComponent(keyword));\n                            sections.push(\"• \".concat(keyword, \" - [PubMed](\").concat(pubmedLink, \") | [Scholar](\").concat(scholarLink, \")\"));\n                        });\n                        sections.push(\"\");\n                    }\n                    if (((_keywords_trending_terms1 = keywords.trending_terms) === null || _keywords_trending_terms1 === void 0 ? void 0 : _keywords_trending_terms1.length) > 0) {\n                        sections.push(\"**热点趋势关键词**:\");\n                        keywords.trending_terms.slice(0, 5).forEach((keyword)=>{\n                            const pubmedLink = \"https://pubmed.ncbi.nlm.nih.gov/?term=\".concat(encodeURIComponent(keyword));\n                            const scholarLink = \"https://scholar.google.com/scholar?q=\".concat(encodeURIComponent(keyword));\n                            sections.push(\"• \".concat(keyword, \" - [PubMed](\").concat(pubmedLink, \") | [Scholar](\").concat(scholarLink, \")\"));\n                        });\n                        sections.push(\"\");\n                    }\n                    if (((_keywords_molecular_targets = keywords.molecular_targets) === null || _keywords_molecular_targets === void 0 ? void 0 : _keywords_molecular_targets.length) > 0) {\n                        sections.push(\"**分子靶点关键词**:\");\n                        keywords.molecular_targets.slice(0, 3).forEach((keyword)=>{\n                            const pubmedLink = \"https://pubmed.ncbi.nlm.nih.gov/?term=\".concat(encodeURIComponent(keyword));\n                            sections.push(\"• \".concat(keyword, \" - [PubMed](\").concat(pubmedLink, \")\"));\n                        });\n                        sections.push(\"\");\n                    }\n                }\n            }\n            // 外部文献搜索结果\n            if (((_intelligent_features_literature_recommendations = intelligent_features.literature_recommendations) === null || _intelligent_features_literature_recommendations === void 0 ? void 0 : (_intelligent_features_literature_recommendations_combined_results = _intelligent_features_literature_recommendations.combined_results) === null || _intelligent_features_literature_recommendations_combined_results === void 0 ? void 0 : _intelligent_features_literature_recommendations_combined_results.length) > 0) {\n                sections.push(\"📚 **相关文献搜索结果**:\");\n                sections.push(\"\");\n                intelligent_features.literature_recommendations.combined_results.slice(0, 3).forEach((paper, index)=>{\n                    var _paper_authors;\n                    const pubmedLink = paper.pubmed_id ? \"https://pubmed.ncbi.nlm.nih.gov/\".concat(paper.pubmed_id, \"/\") : paper.doi ? \"https://pubmed.ncbi.nlm.nih.gov/?term=\".concat(encodeURIComponent(paper.doi), \"[DOI]\") : \"https://pubmed.ncbi.nlm.nih.gov/?term=\".concat(encodeURIComponent(paper.title));\n                    sections.push(\"**[\".concat(index + 1, \"] \").concat(paper.title, \"**\"));\n                    sections.push(\"\\uD83D\\uDCD6 \".concat(paper.journal, \" \").concat(paper.publication_year ? \"(\".concat(paper.publication_year, \")\") : ''));\n                    if (((_paper_authors = paper.authors) === null || _paper_authors === void 0 ? void 0 : _paper_authors.length) > 0) {\n                        sections.push(\"\\uD83D\\uDC65 \".concat(paper.authors.slice(0, 3).join(', ')).concat(paper.authors.length > 3 ? ' et al.' : ''));\n                    }\n                    if (paper.abstract) {\n                        sections.push(\"\\uD83D\\uDCDD \".concat(paper.abstract.substring(0, 200), \"...\"));\n                    }\n                    sections.push(\"\\uD83D\\uDD17 [查看原文](\".concat(pubmedLink, \") | 来源: \").concat(paper.source || 'Unknown'));\n                    sections.push(\"\");\n                });\n            }\n            // AI智能洞察\n            if (((_intelligent_features_smart_insights = intelligent_features.smart_insights) === null || _intelligent_features_smart_insights === void 0 ? void 0 : _intelligent_features_smart_insights.length) > 0) {\n                sections.push(\"💡 **AI智能洞察**\");\n                intelligent_features.smart_insights.forEach((insight)=>{\n                    sections.push(\"• \".concat(insight));\n                });\n                sections.push(\"\");\n            }\n            // 专家合作机会推荐\n            if (((_intelligent_features_collaboration_opportunities = intelligent_features.collaboration_opportunities) === null || _intelligent_features_collaboration_opportunities === void 0 ? void 0 : _intelligent_features_collaboration_opportunities.length) > 0) {\n                sections.push(\"🤝 **专家合作机会推荐**\");\n                intelligent_features.collaboration_opportunities.forEach((collab)=>{\n                    sections.push(\"• **\".concat(collab.institution, \"** - \").concat(collab.expert));\n                    sections.push(\"  \".concat(collab.collaboration_type));\n                });\n                sections.push(\"\");\n            }\n            // 智能文献检索建议\n            if (intelligent_features.search_suggestions) {\n                sections.push(\"🔍 **智能文献检索建议**\");\n                sections.push(\"\");\n                sections.push(\"📌 **推荐检索关键词**:\");\n                intelligent_features.search_suggestions.primaryKeywords.forEach((keyword, index)=>{\n                    sections.push(\"\".concat(index + 1, \". **\").concat(keyword, \"**\"));\n                    // 为每个主要关键词添加搜索链接\n                    const links = intelligent_features.search_suggestions.searchLinks[keyword];\n                    if (links && links.length > 0) {\n                        const linkTexts = links.map((link)=>\"[\".concat(link.emoji, \" \").concat(link.name, \"](\").concat(link.url, \")\")).join(' • ');\n                        sections.push(\"   \".concat(linkTexts));\n                    }\n                    sections.push(\"\");\n                });\n                if (intelligent_features.search_suggestions.secondaryKeywords.length > 0) {\n                    sections.push(\"🔖 **扩展检索关键词**:\");\n                    intelligent_features.search_suggestions.secondaryKeywords.slice(0, 4).forEach((keyword)=>{\n                        sections.push(\"• \".concat(keyword));\n                    });\n                    sections.push(\"\");\n                }\n                sections.push(\"💡 **检索建议**:\");\n                sections.push(\"• 建议先用主要关键词在PubMed中检索最新文献\");\n                sections.push(\"• 使用Google Scholar查找引用次数高的经典文献\");\n                sections.push(\"• Semantic Scholar可发现相关的综述文章\");\n                sections.push(\"• 组合关键词可获得更精准的搜索结果\");\n                sections.push(\"\");\n            }\n        }\n        // 添加文献搜索结果（如果启用了文献搜索）\n        if (literatureResults && ((_literatureResults_literature_results = literatureResults.literature_results) === null || _literatureResults_literature_results === void 0 ? void 0 : _literatureResults_literature_results.length) > 0) {\n            sections.push(\"\");\n            sections.push(\"📚 **相关文献支撑**\");\n            sections.push(\"\");\n            // 显示搜索到的文献（最多显示3篇）\n            const papersToShow = literatureResults.literature_results.slice(0, 3);\n            papersToShow.forEach((paper, index)=>{\n                sections.push(\"**[\".concat(index + 1, \"] \").concat(paper.title || '文献标题', \"**\"));\n                if (paper.authors && paper.authors.length > 0) {\n                    const authorList = paper.authors.slice(0, 3).join(\", \");\n                    const moreAuthors = paper.authors.length > 3 ? \" 等\" : \"\";\n                    sections.push(\"*\".concat(authorList).concat(moreAuthors, \"*\"));\n                }\n                if (paper.journal) {\n                    sections.push(\"\\uD83D\\uDCD6 \".concat(paper.journal).concat(paper.publication_year ? \" (\".concat(paper.publication_year, \")\") : ''));\n                }\n                if (paper.key_findings) {\n                    sections.push(\"\\uD83D\\uDCA1 **核心发现**: \".concat(paper.key_findings));\n                }\n                if (paper.methodology_summary) {\n                    sections.push(\"\\uD83D\\uDD2C **方法**: \".concat(paper.methodology_summary));\n                }\n                sections.push(\"\");\n            });\n            // 显示搜索统计信息\n            if (literatureResults.total_papers > 3) {\n                sections.push(\"\\uD83D\\uDCCA **搜索统计**: 共找到 \".concat(literatureResults.total_papers, \" 篇相关文献，已显示最相关的 \").concat(papersToShow.length, \" 篇\"));\n                sections.push(\"\");\n            }\n            // 显示数据来源\n            if (literatureResults.sources && literatureResults.sources.length > 0) {\n                sections.push(\"\\uD83D\\uDD0D **数据来源**: \".concat(literatureResults.sources.join(\", \")));\n                sections.push(\"\");\n            }\n        }\n        // 处理综合方案框架（按用户要求的新结构）\n        const { comprehensive_framework, framework_features } = solutionResponse;\n        if (comprehensive_framework && (framework_features === null || framework_features === void 0 ? void 0 : framework_features.comprehensive_framework)) {\n            sections.push(\"\");\n            sections.push(\"🎯 **单细胞测序解决方案**\");\n            sections.push(\"\");\n            // 添加简化方案的JSON数据供前端组件解析\n            sections.push(\"```json\");\n            sections.push(JSON.stringify({\n                type: \"streamlined_solution\",\n                data: comprehensive_framework\n            }, null, 2));\n            sections.push(\"```\");\n            sections.push(\"\");\n        }\n        // 处理综合解决方案（5个核心功能）- 作为降级方案\n        const { comprehensive_solution, enhanced_features } = solutionResponse;\n        if (comprehensive_solution && (enhanced_features === null || enhanced_features === void 0 ? void 0 : enhanced_features.five_core_functions)) {\n            sections.push(\"\");\n            sections.push(\"🎯 **CellForge AI 5大核心功能综合分析**\");\n            sections.push(\"\");\n            // 添加综合解决方案的JSON数据供前端组件解析\n            sections.push(\"```json\");\n            sections.push(JSON.stringify({\n                type: \"comprehensive_solution\",\n                data: comprehensive_solution\n            }, null, 2));\n            sections.push(\"```\");\n            sections.push(\"\");\n            sections.push(\"💡 **功能说明**:\");\n            sections.push(\"• **个性化方案**: 基于您的需求定制的专业技术方案\");\n            sections.push(\"• **文献推荐**: 领域相关的高质量学术文献\");\n            sections.push(\"• **搜索关键词**: 优化的学术检索关键词组合\");\n            sections.push(\"• **痛点分析**: 该领域常见技术挑战及解决方案\");\n            sections.push(\"• **风险评估**: 项目实施风险评估及缓解策略\");\n            sections.push(\"\");\n        }\n        sections.push(\"---\");\n        if (isFallback) {\n            sections.push(\"🧬 **CellForge AI** - 您的单细胞测序专业伙伴\");\n            sections.push(\"\");\n            sections.push(\"**服务状态**: 临时方案 • **专家支持**: 人工分析中 • **预计响应**: 2小时内\");\n        } else if (isAiAnalyzed) {\n            sections.push(\"🧬 **CellForge AI** - 您的智能单细胞测序专业伙伴\");\n            sections.push(\"\");\n            const confidence = (ai_analysis_features === null || ai_analysis_features === void 0 ? void 0 : ai_analysis_features.confidence_score) ? Math.round(ai_analysis_features.confidence_score * 100) : 95;\n            sections.push(\"**AI分析置信度**: \".concat(confidence, \"% • **来源**: AI意图分析引擎, 专业知识库, 个性化算法\"));\n        } else if (isIntelligentRecommendation) {\n            sections.push(\"🧬 **CellForge AI** - 您的智能单细胞测序专业伙伴\");\n            sections.push(\"\");\n            sections.push(\"**置信度**: 95% • **来源**: AI智能推荐引擎, 专业知识库, 热点文献发现\");\n        } else {\n            sections.push(\"🧬 **CellForge AI** - 您的单细胞测序专业伙伴\");\n        }\n        return sections.join(\"\\n\");\n    };\n    // 基于需求生成AI建议 - 使用增强的加载状态\n    const generateRequirementBasedSuggestion = async (reqs)=>{\n        const suggestionMessage = \"基于您填写的需求信息：\\n- 研究目标：\".concat(reqs.researchGoal, \"\\n- 样本类型：\").concat(reqs.sampleType, \"\\n- 细胞数量：\").concat(reqs.cellCount, \"\\n- 预算范围：\").concat(reqs.budget, \"\\n- 项目周期：\").concat(reqs.timeline, \"\\n\\n我来为您生成专业的技术方案建议。\");\n        try {\n            // 使用分阶段加载\n            let apiResponse;\n            await _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.errorHandler.handleStagedOperation([\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.ANALYZING,\n                    operation: async ()=>{\n                        await new Promise((resolve)=>setTimeout(resolve, 500));\n                        return {\n                            step: \"analyzed\"\n                        };\n                    }\n                },\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.GENERATING_RESPONSE,\n                    operation: async ()=>{\n                        apiResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.conversationApi.sendMessage({\n                            message: suggestionMessage,\n                            conversation_type: \"requirement_based\",\n                            context: {\n                                user_profile: {\n                                    id: user === null || user === void 0 ? void 0 : user.id,\n                                    organization: (user === null || user === void 0 ? void 0 : user.organization) || \"\",\n                                    role: (user === null || user === void 0 ? void 0 : user.role) || \"\"\n                                },\n                                requirements: reqs\n                            }\n                        });\n                        return apiResponse;\n                    }\n                }\n            ], (stage, progress)=>{\n                if (!isLoading) {\n                    startLoading(stage);\n                } else {\n                    updateStage(stage, progress);\n                }\n            }, {\n                maxRetries: 1,\n                retryDelay: 3000,\n                userFriendlyMessage: \"生成建议失败，请稍后重试\",\n                showToast: false\n            });\n            // apiResponse已经在GENERATING_RESPONSE阶段中设置\n            const aiSuggestion = {\n                id: Date.now().toString(),\n                type: \"ai\",\n                content: apiResponse.message,\n                timestamp: new Date(),\n                status: \"read\",\n                confidence: apiResponse.confidence,\n                sources: apiResponse.sources,\n                suggestions: apiResponse.suggestions\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiSuggestion\n                ]);\n            finishLoading();\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"已基于您的需求生成专业建议！\");\n        } catch (err) {\n            console.error(\"生成建议失败:\", err);\n            cancelLoading();\n        // 错误已经由errorHandler处理\n        }\n    };\n    // 获取缺失的关键需求字段 - 更新以匹配新的必需字段\n    const getMissingRequirements = ()=>{\n        const requiredFields = [\n            {\n                key: \"speciesType\",\n                label: \"物种类型\",\n                question: \"您研究的是什么物种？\"\n            },\n            {\n                key: \"experimentType\",\n                label: \"实验类型\",\n                question: \"您需要什么类型的单细胞实验？\"\n            },\n            {\n                key: \"researchGoal\",\n                label: \"研究目标\",\n                question: \"您的研究目标是什么？\"\n            },\n            {\n                key: \"sampleType\",\n                label: \"样本类型\",\n                question: \"您使用什么类型的样本？\"\n            },\n            {\n                key: \"budget\",\n                label: \"预算范围\",\n                question: \"您的项目预算大概是多少？\"\n            }\n        ];\n        if (!requirements) return requiredFields;\n        return requiredFields.filter((field)=>!requirements[field.key]);\n    };\n    // 获取智能建议（合并AI建议和快速回复）\n    const getSmartSuggestions = ()=>{\n        // 优先显示AI回复中的建议\n        const lastMessage = messages[messages.length - 1];\n        if ((lastMessage === null || lastMessage === void 0 ? void 0 : lastMessage.type) === \"ai\" && lastMessage.suggestions && lastMessage.suggestions.length > 0) {\n            return {\n                type: \"ai_suggestions\",\n                title: \"AI智能建议\",\n                icon: \"✨\",\n                items: lastMessage.suggestions.slice(0, 4).map((suggestion)=>({\n                        text: suggestion,\n                        icon: \"💡\"\n                    }))\n            };\n        }\n        // 检查是否有缺失的关键需求信息\n        const missingReqs = getMissingRequirements();\n        if (missingReqs.length > 0 && (!requirements || requirements.completeness < 60)) {\n            return {\n                type: \"requirement_framework\",\n                title: \"完善需求信息\",\n                icon: \"📋\",\n                subtitle: \"还需要填写 \".concat(missingReqs.length, \" 项关键信息，建议先完善后再提交\"),\n                items: missingReqs.slice(0, 3).map((req)=>({\n                        text: req.question,\n                        icon: \"❓\",\n                        action: \"fill_requirement\",\n                        field: req.key\n                    }))\n            };\n        }\n        // 否则显示基于需求完成度的快速开始建议\n        if (!requirements || requirements.completeness < 20) {\n            return {\n                type: \"quick_start\",\n                title: \"快速开始\",\n                icon: \"💬\",\n                items: [\n                    {\n                        text: \"我需要单细胞RNA测序方案\",\n                        icon: \"🧬\"\n                    },\n                    {\n                        text: \"请推荐技术平台\",\n                        icon: \"⚡\"\n                    },\n                    {\n                        text: \"分析项目成本\",\n                        icon: \"💰\"\n                    },\n                    {\n                        text: \"查看成功案例\",\n                        icon: \"📊\"\n                    }\n                ]\n            };\n        } else if (requirements.completeness < 80) {\n            return {\n                type: \"continue_conversation\",\n                title: \"继续对话\",\n                icon: \"🔄\",\n                items: [\n                    {\n                        text: \"继续完善需求信息\",\n                        icon: \"📝\"\n                    },\n                    {\n                        text: \"基于当前信息给建议\",\n                        icon: \"💡\"\n                    },\n                    {\n                        text: \"了解技术细节\",\n                        icon: \"🔬\"\n                    },\n                    {\n                        text: \"预估项目周期\",\n                        icon: \"⏱️\"\n                    }\n                ]\n            };\n        } else {\n            return {\n                type: \"advanced_actions\",\n                title: \"高级功能\",\n                icon: \"🚀\",\n                items: [\n                    {\n                        text: \"生成完整技术方案\",\n                        icon: \"📋\"\n                    },\n                    {\n                        text: \"优化成本配置\",\n                        icon: \"💰\"\n                    },\n                    {\n                        text: \"风险评估分析\",\n                        icon: \"⚠️\"\n                    },\n                    {\n                        text: \"联系技术专家\",\n                        icon: \"👨‍🔬\"\n                    }\n                ]\n            };\n        }\n    };\n    // 处理建议点击\n    const handleSuggestionClick = (item)=>{\n        if (item.action === \"fill_requirement\") {\n            // 如果是需求填写建议，显示右侧面板并聚焦到对应字段\n            setRightPanelCollapsed(false);\n            // 可以添加滚动到对应字段的逻辑\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.info(\"请在右侧面板填写：\".concat(item.text));\n        } else {\n            // 普通建议直接设置到输入框\n            setInputValue(item.text);\n        }\n    };\n    // 加载历史对话\n    const handleLoadConversation = (session)=>{\n        setMessages(session.messages);\n        setCurrentSessionId(session.id);\n        setShowHistory(false);\n        sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"已加载对话: \".concat(session.title));\n    };\n    // 保存当前对话\n    const handleSaveCurrentConversation = ()=>{\n        // 这个函数会在ConversationHistory组件中处理\n        setShowHistory(false);\n    };\n    // 开始新对话\n    const handleNewConversation = ()=>{\n        setMessages([]);\n        setCurrentSessionId(null);\n        setRequirements(null);\n        setError(\"\");\n        setShowHistory(false);\n        setRightPanelCollapsed(false) // 显示需求收集助手\n        ;\n        setResetTrigger((prev)=>prev + 1) // 触发需求收集助手重置\n        ;\n        sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"已开始新对话，请在右侧填写项目需求\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full bg-slate-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col h-full transition-all duration-300 \".concat(rightPanelCollapsed ? 'flex-1' : 'flex-1'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border-b border-slate-200 px-4 py-3 flex items-center justify-between flex-shrink-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1269,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1268,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"font-semibold text-slate-900\",\n                                                        children: \"CellForge AI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                        lineNumber: 1272,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-slate-500\",\n                                                        children: \"单细胞测序智能顾问\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                        lineNumber: 1273,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1271,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1267,\n                                        columnNumber: 13\n                                    }, this),\n                                    requirements && requirements.completeness > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"bg-blue-100 text-blue-800\",\n                                        children: [\n                                            \"需求完成度 \",\n                                            requirements.completeness,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1277,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                lineNumber: 1266,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowHistory(!showHistory),\n                                        className: \"text-slate-600 hover:text-slate-900\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1291,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"历史\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1285,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: handleNewConversation,\n                                        className: \"text-slate-600 hover:text-slate-900\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1302,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"新对话\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1296,\n                                        columnNumber: 13\n                                    }, this),\n                                    !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setRightPanelCollapsed(!rightPanelCollapsed),\n                                        className: \"text-slate-600 hover:text-slate-900\",\n                                        children: rightPanelCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1315,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"显示助手\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"隐藏助手\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-4 w-4 ml-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1321,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1307,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                lineNumber: 1283,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                        lineNumber: 1265,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                            variant: \"destructive\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1333,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1334,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                            lineNumber: 1332,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                        lineNumber: 1331,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-h-0 relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 overflow-y-auto p-4 space-y-6 bg-white\",\n                            children: [\n                                messages.map((message)=>{\n                                    var _user_name;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex \".concat(message.type === \"user\" ? \"justify-end\" : \"justify-start\"),\n                                        children: [\n                                            message.type === \"ai\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mr-3 flex-shrink-0 mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1347,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1346,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\".concat(message.type === \"user\" ? \"max-w-lg ml-12\" : \"max-w-4xl mr-8\" // Give AI messages more space\n                                                ),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"\".concat(message.type === \"user\" ? \"px-4 py-3 rounded-2xl shadow-sm\" : \"px-6 py-4 rounded-2xl shadow-sm\" // More padding for AI messages\n                                                        , \" \").concat(message.type === \"user\" ? message.status === \"error\" ? \"bg-red-500 text-white\" : \"bg-gradient-to-r from-blue-600 to-indigo-600 text-white\" : \"bg-white border border-slate-200 text-slate-900\" // White background for AI\n                                                        ),\n                                                        children: [\n                                                            message.type === \"ai\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_formatted_message__WEBPACK_IMPORTED_MODULE_12__.FormattedMessage, {\n                                                                content: message.content,\n                                                                className: \"text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                lineNumber: 1370,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm leading-relaxed whitespace-pre-wrap\",\n                                                                children: message.content\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                lineNumber: 1372,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            message.type === \"ai\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-3 pt-2 border-t border-slate-200 space-y-1\",\n                                                                children: [\n                                                                    message.confidence !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                className: \"h-3 w-3 text-blue-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                                lineNumber: 1380,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-slate-600\",\n                                                                                children: [\n                                                                                    \"置信度: \",\n                                                                                    Math.round(message.confidence * 100),\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                                lineNumber: 1381,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                        lineNumber: 1379,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    message.sources && message.sources.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"h-3 w-3 text-green-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                                lineNumber: 1388,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-slate-600\",\n                                                                                children: [\n                                                                                    \"来源: \",\n                                                                                    message.sources.slice(0, 2).join(\", \"),\n                                                                                    message.sources.length > 2 && \"...\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                                lineNumber: 1389,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                        lineNumber: 1387,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                lineNumber: 1377,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                        lineNumber: 1356,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs mt-2 flex items-center \".concat(message.type === \"user\" ? \"justify-end text-slate-500\" : \"justify-start text-slate-500\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: message.timestamp.toLocaleTimeString()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                lineNumber: 1402,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            message.type === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2\",\n                                                                children: [\n                                                                    message.status === \"sending\" && \"⏳\",\n                                                                    message.status === \"sent\" && \"✓\",\n                                                                    message.status === \"read\" && \"✓✓\",\n                                                                    message.status === \"error\" && \"❌\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                lineNumber: 1404,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                        lineNumber: 1399,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1351,\n                                                columnNumber: 17\n                                            }, this),\n                                            message.type === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-slate-400 to-slate-500 rounded-full flex items-center justify-center ml-3 flex-shrink-0 mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-sm font-medium\",\n                                                    children: (user === null || user === void 0 ? void 0 : (_user_name = user.name) === null || _user_name === void 0 ? void 0 : _user_name.charAt(0)) || \"U\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1416,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1415,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, message.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1344,\n                                        columnNumber: 15\n                                    }, this);\n                                }),\n                                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mr-3 flex-shrink-0 mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1427,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                            lineNumber: 1426,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 max-w-md\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_enhanced_loading_indicator__WEBPACK_IMPORTED_MODULE_9__.EnhancedLoadingIndicator, {\n                                                isLoading: isLoading,\n                                                stage: currentStage,\n                                                progress: progress,\n                                                estimatedTime: 30,\n                                                onCancel: cancelLoading,\n                                                onRetry: ()=>{\n                                                    cancelLoading();\n                                                    handleSendMessage();\n                                                },\n                                                showRetryAfter: 60,\n                                                allowCancel: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1430,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                            lineNumber: 1429,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1425,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: messagesEndRef\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1446,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                            lineNumber: 1342,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                        lineNumber: 1340,\n                        columnNumber: 9\n                    }, this),\n                    (()=>{\n                        const suggestions = getSmartSuggestions();\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-slate-200 p-4 flex-shrink-0 \".concat(suggestions.type === \"ai_suggestions\" ? \"bg-gradient-to-r from-blue-50 to-indigo-50\" : suggestions.type === \"requirement_framework\" ? \"bg-gradient-to-r from-amber-50 to-orange-50\" : \"bg-white\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-3\",\n                                        children: [\n                                            suggestions.type === \"ai_suggestions\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1464,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: suggestions.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1466,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium \".concat(suggestions.type === \"ai_suggestions\" ? \"text-blue-800\" : suggestions.type === \"requirement_framework\" ? \"text-amber-800\" : \"text-slate-600\"),\n                                                children: suggestions.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1468,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1462,\n                                        columnNumber: 17\n                                    }, this),\n                                    suggestions.subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-amber-700 mb-3 p-2 bg-amber-100 rounded-lg border border-amber-200\",\n                                        children: suggestions.subtitle\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1481,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: suggestions.items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>handleSuggestionClick(item),\n                                                className: \"text-xs \".concat(suggestions.type === \"ai_suggestions\" ? \"bg-white hover:bg-blue-50 border-blue-200 text-blue-700 hover:text-blue-800\" : suggestions.type === \"requirement_framework\" ? \"bg-white hover:bg-amber-50 border-amber-200 text-amber-700 hover:text-amber-800\" : \"bg-slate-50 hover:bg-slate-100 border-slate-200\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-1\",\n                                                        children: item.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                        lineNumber: 1501,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    item.text\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1488,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1486,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                lineNumber: 1461,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                            lineNumber: 1454,\n                            columnNumber: 13\n                        }, this);\n                    })(),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-slate-200 p-4 bg-white flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 bg-slate-50 rounded-xl p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    className: \"text-slate-500 hover:text-slate-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1515,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1514,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    className: \"text-slate-500 hover:text-slate-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1518,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1517,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                    value: inputValue,\n                                    onChange: (e)=>setInputValue(e.target.value),\n                                    placeholder: \"输入您的问题，我来为您提供专业建议...\",\n                                    onKeyDown: (e)=>e.key === \"Enter\" && !e.shiftKey && handleSendMessage(),\n                                    className: \"flex-1 border-0 bg-white shadow-sm focus:ring-2 focus:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1520,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleSendMessage,\n                                    disabled: !inputValue.trim() || isLoading,\n                                    className: \"bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1532,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1527,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                            lineNumber: 1513,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                        lineNumber: 1512,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                lineNumber: 1261,\n                columnNumber: 7\n            }, this),\n            (!rightPanelCollapsed || showHistory) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-l border-slate-200 bg-white transition-all duration-300 flex flex-col h-full \".concat(isMobile ? 'absolute right-0 top-0 bottom-0 w-80 shadow-xl z-10' : 'w-80 flex-shrink-0'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-slate-200 p-4 flex-shrink-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: showHistory ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 text-indigo-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1549,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-slate-900\",\n                                                    children: \"对话历史\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1550,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    className: \"h-5 w-5 text-indigo-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1554,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-slate-900\",\n                                                    children: \"需求收集助手\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1555,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1546,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            !showHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>setShowHistory(true),\n                                                className: \"text-slate-500 hover:text-slate-700 h-6 w-6 p-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1569,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1563,\n                                                columnNumber: 19\n                                            }, this),\n                                            showHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>setShowHistory(false),\n                                                className: \"text-slate-500 hover:text-slate-700 h-6 w-6 p-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1579,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1573,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>{\n                                                    setRightPanelCollapsed(true);\n                                                    setShowHistory(false);\n                                                },\n                                                className: \"text-slate-500 h-6 w-6 p-0\",\n                                                title: \"隐藏面板\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1593,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1583,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1560,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                lineNumber: 1545,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-slate-600 mt-1\",\n                                children: showHistory ? \"查看和管理您的对话历史\" : \"快速填写项目需求，获得更精准的AI建议\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                lineNumber: 1597,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                        lineNumber: 1544,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-h-0 relative\",\n                        children: showHistory ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_conversation_history__WEBPACK_IMPORTED_MODULE_13__.ConversationHistory, {\n                                onLoadConversation: handleLoadConversation,\n                                currentMessages: messages,\n                                onSaveCurrentConversation: handleSaveCurrentConversation\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                lineNumber: 1609,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                            lineNumber: 1608,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 overflow-y-auto p-4 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_literature_search_toggle__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    enabled: enableLiteratureSearch,\n                                    onToggle: setEnableLiteratureSearch,\n                                    className: \"mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1618,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_progressive_requirement_collector__WEBPACK_IMPORTED_MODULE_11__.ProgressiveRequirementCollector, {\n                                    onRequirementsChange: handleRequirementsChange,\n                                    onSubmitRequirements: handleRequirementsSubmit,\n                                    isCompact: true,\n                                    resetTrigger: resetTrigger\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1625,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                            lineNumber: 1616,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                        lineNumber: 1606,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                lineNumber: 1540,\n                columnNumber: 9\n            }, this),\n            isMobile && !rightPanelCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-5\",\n                onClick: ()=>setRightPanelCollapsed(true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                lineNumber: 1639,\n                columnNumber: 9\n            }, this),\n            isMobile && rightPanelCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                className: \"fixed bottom-4 right-4 rounded-full w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-lg z-10\",\n                onClick: ()=>setRightPanelCollapsed(false),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                    lineNumber: 1651,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                lineNumber: 1647,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n        lineNumber: 1259,\n        columnNumber: 5\n    }, this);\n}\n_s(ConversationInterface, \"YqxGvOJHMwSGRzXwQIpKNTlg44s=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        _enhanced_loading_indicator__WEBPACK_IMPORTED_MODULE_9__.useStageLoading\n    ];\n});\n_c = ConversationInterface;\nvar _c;\n$RefreshReg$(_c, \"ConversationInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvY29udmVyc2F0aW9uLWludGVyZmFjZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFbUQ7QUFDSjtBQUNGO0FBQ0E7QUFDc0g7QUFDcEc7QUFDZDtBQUNvRDtBQUc1QjtBQUNlO0FBQzFEO0FBQ3VEO0FBQy9CO0FBQ007QUFDRztBQWlEeEQsU0FBU2tDOztJQUNkLE1BQU0sQ0FBQ0MsVUFBVUMsWUFBWSxHQUFHcEMsK0NBQVFBLENBQVksRUFBRTtJQUN0RCxNQUFNLENBQUNxQyxZQUFZQyxjQUFjLEdBQUd0QywrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUN1QyxPQUFPQyxTQUFTLEdBQUd4QywrQ0FBUUEsQ0FBUztJQUMzQyxNQUFNLENBQUN5QyxnQkFBZ0JDLGtCQUFrQixHQUFHMUMsK0NBQVFBLENBQWdCO0lBQ3BFLE1BQU0sQ0FBQzJDLGNBQWNDLGdCQUFnQixHQUFHNUMsK0NBQVFBLENBQXlCO0lBQ3pFLE1BQU0sQ0FBQzZDLHFCQUFxQkMsdUJBQXVCLEdBQUc5QywrQ0FBUUEsQ0FBQztJQUMvRCxNQUFNLENBQUMrQyxVQUFVQyxZQUFZLEdBQUdoRCwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUNpRCxhQUFhQyxlQUFlLEdBQUdsRCwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNtRCxrQkFBa0JDLG9CQUFvQixHQUFHcEQsK0NBQVFBLENBQWdCO0lBQ3hFLE1BQU0sQ0FBQ3FELGNBQWNDLGdCQUFnQixHQUFHdEQsK0NBQVFBLENBQUMsR0FBRyxlQUFlOztJQUNuRSxNQUFNLENBQUN1RCx3QkFBd0JDLDBCQUEwQixHQUFHeEQsK0NBQVFBLENBQUMsT0FBTyxTQUFTOztJQUNyRixNQUFNeUQsaUJBQWlCeEQsNkNBQU1BLENBQWlCO0lBQzlDLE1BQU0sRUFBRXlELElBQUksRUFBRSxHQUFHckMsK0RBQU9BO0lBRXhCLGNBQWM7SUFDZCxNQUFNLEVBQ0pzQyxTQUFTLEVBQ1RDLFlBQVksRUFDWkMsUUFBUSxFQUNSQyxZQUFZLEVBQ1pDLFdBQVcsRUFDWEMsYUFBYSxFQUNiQyxhQUFhLEVBQ2QsR0FBR3JDLDRFQUFlQTtJQUVuQixRQUFRO0lBQ1IxQixnREFBU0E7MkNBQUM7WUFDUixNQUFNZ0U7K0RBQWM7b0JBQ2xCbEIsWUFBWW1CLE9BQU9DLFVBQVUsR0FBRztvQkFDaEMsSUFBSUQsT0FBT0MsVUFBVSxHQUFHLE1BQU07d0JBQzVCdEIsdUJBQXVCO29CQUN6QjtnQkFDRjs7WUFFQW9CO1lBQ0FDLE9BQU9FLGdCQUFnQixDQUFDLFVBQVVIO1lBQ2xDO21EQUFPLElBQU1DLE9BQU9HLG1CQUFtQixDQUFDLFVBQVVKOztRQUNwRDswQ0FBRyxFQUFFO0lBRUwsUUFBUTtJQUNSLE1BQU1LLGlCQUFpQjtZQUNyQmQ7U0FBQUEsMEJBQUFBLGVBQWVlLE9BQU8sY0FBdEJmLDhDQUFBQSx3QkFBd0JnQixjQUFjLENBQUM7WUFBRUMsVUFBVTtRQUFTO0lBQzlEO0lBRUF4RSxnREFBU0E7MkNBQUM7WUFDUnFFO1FBQ0Y7MENBQUc7UUFBQ3BDO0tBQVM7SUFFYixVQUFVO0lBQ1ZqQyxnREFBU0E7MkNBQUM7WUFDUixNQUFNeUUsaUJBQTBCO2dCQUM5QkMsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkMsU0FBUyxLQUF1QyxPQUFsQ3BCLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTXFCLElBQUksSUFBRyxJQUFjLE9BQVZyQixLQUFLcUIsSUFBSSxJQUFLLElBQUc7Z0JBZWhEQyxXQUFXLElBQUlDO2dCQUNmQyxRQUFRO2dCQUNSQyxZQUFZO2dCQUNaQyxhQUFhO29CQUNYO29CQUNBO29CQUNBO29CQUNBO2lCQUNEO1lBQ0g7WUFDQWhELFlBQVk7Z0JBQUN1QzthQUFlO1FBQzlCOzBDQUFHO1FBQUNqQjtLQUFLO0lBRVQsWUFBWTtJQUNaLE1BQU0yQix1QkFBdUIsQ0FBQ2xEO1FBQzVCLElBQUlBLFNBQVNtRCxNQUFNLElBQUksR0FBRztZQUN4QixJQUFJO29CQXFCOEJDLHdCQU1qQnBEO2dCQTFCZixNQUFNcUQsYUFBYSwyQkFBbUQsT0FBeEI5QixDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1rQixFQUFFLEtBQUk7Z0JBQzFELE1BQU1XLG1CQUFtQkUsS0FBS0MsS0FBSyxDQUFDQyxhQUFhQyxPQUFPLENBQUNKLGVBQWU7Z0JBRXhFLFNBQVM7Z0JBQ1QsTUFBTUssZ0JBQWdCLENBQUNDO29CQUNyQixNQUFNQyxtQkFBbUJELEtBQUtFLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRXBCLElBQUksS0FBSztvQkFDbkQsSUFBSWtCLGtCQUFrQjt3QkFDcEIsTUFBTWpCLFVBQVVpQixpQkFBaUJqQixPQUFPLENBQUNvQixJQUFJO3dCQUM3QyxPQUFPcEIsUUFBUVEsTUFBTSxHQUFHLEtBQUtSLFFBQVFxQixTQUFTLENBQUMsR0FBRyxNQUFNLFFBQVFyQjtvQkFDbEU7b0JBQ0EsT0FBTztnQkFDVDtnQkFFQSxNQUFNc0IsWUFBWWpELG9CQUFvQixXQUF5QmtELE9BQWRwQixLQUFLcUIsR0FBRyxJQUFHLEtBQTJDLE9BQXhDRCxLQUFLRSxNQUFNLEdBQUdDLFFBQVEsQ0FBQyxJQUFJQyxNQUFNLENBQUMsR0FBRztnQkFDcEcsTUFBTUgsTUFBTSxJQUFJckI7Z0JBRWhCLE1BQU15QixVQUFVO29CQUNkOUIsSUFBSXdCO29CQUNKTyxPQUFPZCxjQUFjMUQ7b0JBQ3JCQTtvQkFDQXlFLFdBQVd6RCxtQkFBbUJvQyxFQUFBQSx5QkFBQUEsaUJBQWlCUyxJQUFJLENBQUMsQ0FBQ2EsSUFBV0EsRUFBRWpDLEVBQUUsS0FBS3dCLHdCQUEzQ2IsNkNBQUFBLHVCQUF1RHFCLFNBQVMsS0FBSU4sTUFBTUE7b0JBQ3hHUSxXQUFXUjtvQkFDWFMsV0FBVztvQkFDWEMsWUFBWTtvQkFDWkMsTUFBTSxFQUFFO29CQUNSQyxjQUFjL0UsU0FBU21ELE1BQU07b0JBQzdCNkIsYUFBYWhGLEVBQUFBLGFBQUFBLFFBQVEsQ0FBQ0EsU0FBU21ELE1BQU0sR0FBRyxFQUFFLGNBQTdCbkQsaUNBQUFBLFdBQStCMkMsT0FBTyxDQUFDcUIsU0FBUyxDQUFDLEdBQUcsU0FBUTtnQkFDM0U7Z0JBRUEsVUFBVTtnQkFDVixNQUFNaUIsa0JBQWtCakUsbUJBQ3BCb0MsaUJBQWlCOEIsR0FBRyxDQUFDLENBQUNSLElBQVdBLEVBQUVqQyxFQUFFLEtBQUt3QixZQUFZTSxVQUFVRyxLQUNoRTtvQkFBQ0g7dUJBQVluQjtpQkFBaUIsQ0FBQytCLEtBQUssQ0FBQyxHQUFHLElBQUksWUFBWTs7Z0JBRTVEM0IsYUFBYTRCLE9BQU8sQ0FBQy9CLFlBQVlDLEtBQUsrQixTQUFTLENBQUNKO2dCQUNoRGhFLG9CQUFvQmdEO1lBQ3RCLEVBQUUsT0FBTzdELE9BQU87Z0JBQ2RrRixRQUFRbEYsS0FBSyxDQUFDLGFBQWFBO1lBQzdCO1FBQ0Y7SUFDRjtJQUVBLDJCQUEyQjtJQUMzQixNQUFNbUYsb0JBQW9CO1FBQ3hCLElBQUksQ0FBQ3JGLFdBQVc2RCxJQUFJLElBQUk7UUFFeEIsTUFBTXlCLGNBQWN0RixXQUFXNkQsSUFBSTtRQUNuQzVELGNBQWM7UUFDZEUsU0FBUztRQUVULFNBQVM7UUFDVCxNQUFNb0YsaUJBQTBCO1lBQzlCaEQsSUFBSUssS0FBS3FCLEdBQUcsR0FBR0UsUUFBUTtZQUN2QjNCLE1BQU07WUFDTkMsU0FBUzZDO1lBQ1QzQyxXQUFXLElBQUlDO1lBQ2ZDLFFBQVE7UUFDVjtRQUVBOUMsWUFBWSxDQUFDeUYsT0FBUzttQkFBSUE7Z0JBQU1EO2FBQWU7UUFFL0MseUJBQXlCO1FBQ3pCLE1BQU1FLHNCQUFzQjNGLFNBQVNrRixHQUFHLENBQUNVLENBQUFBLE1BQVE7Z0JBQy9DQyxNQUFNRCxJQUFJbEQsSUFBSSxLQUFLLFNBQVMsU0FBUztnQkFDckNDLFNBQVNpRCxJQUFJakQsT0FBTztZQUN0QjtRQUVBLE1BQU1tRCxrQkFBa0I7WUFDdEJDLGNBQWM7Z0JBQ1p0RCxFQUFFLEVBQUVsQixpQkFBQUEsMkJBQUFBLEtBQU1rQixFQUFFO2dCQUNadUQsY0FBY3pFLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTXlFLFlBQVksS0FBSTtnQkFDcENILE1BQU10RSxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1zRSxJQUFJLEtBQUk7Z0JBQ3BCSSxXQUFXMUUsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNMkUsZUFBZSxLQUFJO2dCQUNwQ0Msb0JBQW9CNUUsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNNEUsa0JBQWtCLEtBQUk7WUFDbEQ7WUFDQTNGLGNBQWNBO1lBQ2Q0RixzQkFBc0JUO1lBQ3RCVSwwQkFBMEJqRjtRQUM1QjtRQUVBLElBQUk7WUFDRixZQUFZO1lBQ1osSUFBSWtGO1lBRUosTUFBTWhILHFFQUFZQSxDQUFDaUgscUJBQXFCLENBQUM7Z0JBQ3ZDO29CQUNFQyxPQUFPakgscUVBQVlBLENBQUNrSCxTQUFTO29CQUM3QkMsV0FBVzt3QkFDVCxTQUFTO3dCQUNULE1BQU0sSUFBSUMsUUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBUzt3QkFDakQsT0FBTzs0QkFBRUUsTUFBTTt3QkFBVztvQkFDNUI7Z0JBQ0Y7Z0JBQ0E7b0JBQ0VOLE9BQU9qSCxxRUFBWUEsQ0FBQ3dILG9CQUFvQjtvQkFDeENMLFdBQVc7d0JBQ1QsU0FBUzt3QkFDVCxNQUFNLElBQUlDLFFBQVFDLENBQUFBLFVBQVdDLFdBQVdELFNBQVM7d0JBQ2pELE9BQU87NEJBQUVFLE1BQU07d0JBQXNCO29CQUN2QztnQkFDRjtnQkFDQTtvQkFDRU4sT0FBT2pILHFFQUFZQSxDQUFDeUgsbUJBQW1CO29CQUN2Q04sV0FBVzt3QkFDVCxhQUFhO3dCQUNiSixnQkFBZ0IsTUFBTW5ILHFEQUFlQSxDQUFDOEgsV0FBVyxDQUFDOzRCQUNoREMsU0FBUzFCOzRCQUNUMkIsaUJBQWlCN0csa0JBQWtCOEc7NEJBQ25DQyxtQkFBbUI7NEJBQ25CQyxTQUFTM0I7NEJBQ1Q0QixTQUFTekI7d0JBQ1g7d0JBQ0EsT0FBT1E7b0JBQ1Q7Z0JBQ0Y7Z0JBQ0E7b0JBQ0VFLE9BQU9qSCxxRUFBWUEsQ0FBQ2lJLFVBQVU7b0JBQzlCZCxXQUFXO3dCQUNULFVBQVU7d0JBQ1YsTUFBTSxJQUFJQyxRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTO3dCQUNqRCxPQUFPOzRCQUFFRSxNQUFNO3dCQUFZO29CQUM3QjtnQkFDRjthQUNELEVBQ0QsQ0FBQ04sT0FBTzlFO2dCQUNOLElBQUksQ0FBQ0YsV0FBVztvQkFDZEcsYUFBYTZFO2dCQUNmLE9BQU87b0JBQ0w1RSxZQUFZNEUsT0FBTzlFO2dCQUNyQjtZQUNGLEdBQ0E7Z0JBQ0UrRixZQUFZO2dCQUNaQyxZQUFZO2dCQUNaQyxxQkFBcUI7Z0JBQ3JCQyxXQUFXO2dCQUNYQyxTQUFTLENBQUNDLFNBQVMxSDt3QkFFYkEsZ0JBQW9DQTtvQkFEeEMsaUJBQWlCO29CQUNqQixJQUFJQSxFQUFBQSxpQkFBQUEsTUFBTThHLE9BQU8sY0FBYjlHLHFDQUFBQSxlQUFlMkgsUUFBUSxDQUFDLGVBQVkzSCxrQkFBQUEsTUFBTThHLE9BQU8sY0FBYjlHLHNDQUFBQSxnQkFBZTJILFFBQVEsQ0FBQyxRQUFPO3dCQUNyRXJJLDBDQUFLQSxDQUFDc0ksSUFBSSxDQUFFO29CQUNkO2dCQUNGO1lBQ0Y7WUFFQSwyQ0FBMkM7WUFFM0MsZUFBZTtZQUNmL0gsWUFBWSxDQUFDeUYsT0FDWEEsS0FBS1IsR0FBRyxDQUFDVSxDQUFBQSxNQUNQQSxJQUFJbkQsRUFBRSxLQUFLZ0QsZUFBZWhELEVBQUUsR0FDeEI7d0JBQUUsR0FBR21ELEdBQUc7d0JBQUU3QyxRQUFRO29CQUFnQixJQUNsQzZDO1lBSVIsV0FBVztZQUNYLE1BQU1xQyxhQUFzQjtnQkFDMUJ4RixJQUFJLENBQUNLLEtBQUtxQixHQUFHLEtBQUssR0FBR0UsUUFBUTtnQkFDN0IzQixNQUFNO2dCQUNOQyxTQUFTMkQsY0FBY1ksT0FBTztnQkFDOUJyRSxXQUFXLElBQUlDO2dCQUNmQyxRQUFRO2dCQUNSQyxZQUFZc0QsY0FBY3RELFVBQVU7Z0JBQ3BDa0YsU0FBUzVCLGNBQWM0QixPQUFPO2dCQUM5QmpGLGFBQWFxRCxjQUFjckQsV0FBVztZQUN4QztZQUVBaEQsWUFBWSxDQUFDeUY7Z0JBQ1gsTUFBTXlDLGNBQWM7dUJBQUl6QztvQkFBTXVDO2lCQUFXO2dCQUN6QyxTQUFTO2dCQUNUcEIsV0FBVyxJQUFNM0QscUJBQXFCaUYsY0FBYztnQkFDcEQsT0FBT0E7WUFDVDtZQUVBdEc7UUFFRixFQUFFLE9BQU91RyxLQUFVO1lBQ2pCOUMsUUFBUWxGLEtBQUssQ0FBQyxXQUFXZ0k7WUFFekIsY0FBYztZQUNkbkksWUFBWSxDQUFDeUYsT0FDWEEsS0FBS1IsR0FBRyxDQUFDVSxDQUFBQSxNQUNQQSxJQUFJbkQsRUFBRSxLQUFLZ0QsZUFBZWhELEVBQUUsR0FDeEI7d0JBQUUsR0FBR21ELEdBQUc7d0JBQUU3QyxRQUFRO29CQUFpQixJQUNuQzZDO1lBSVIsa0NBQWtDO1lBQ2xDdkYsU0FBUztZQUNUeUI7UUFDRjtJQUNGO0lBRUEsU0FBUztJQUNULE1BQU11RywyQkFBMkIsQ0FBQ0M7UUFDaEM3SCxnQkFBZ0I2SDtJQUNsQjtJQUVBLHVCQUF1QjtJQUN2QixNQUFNQywyQkFBMkIsT0FBTy9IO1FBQ3RDLFdBQVc7UUFDWCxNQUFNZ0kscUJBQXFCQywyQkFBMkJqSTtRQUV0RCxhQUFhO1FBQ2IsTUFBTWdGLGNBQXVCO1lBQzNCL0MsSUFBSUssS0FBS3FCLEdBQUcsR0FBR0UsUUFBUTtZQUN2QjNCLE1BQU07WUFDTkMsU0FBUzZGO1lBQ1QzRixXQUFXLElBQUlDO1lBQ2ZDLFFBQVE7UUFDVjtRQUVBOUMsWUFBWSxDQUFDeUYsT0FBUzttQkFBSUE7Z0JBQU1GO2FBQVk7UUFFNUMsSUFBSTtnQkF5TUVrRCx3Q0FLT0EsK0JBZ0JHQTtZQTdOZCwyQkFBMkI7WUFDM0IsSUFBSUE7WUFDSixJQUFJQyxvQkFBeUI7WUFFN0IsTUFBTXJKLHFFQUFZQSxDQUFDaUgscUJBQXFCLENBQUM7Z0JBQ3ZDO29CQUNFQyxPQUFPakgscUVBQVlBLENBQUNrSCxTQUFTO29CQUM3QkMsV0FBVzt3QkFDVCxVQUFVO3dCQUNWLE1BQU1rQyxhQUFhLE1BQU12SixpREFBV0EsQ0FBQ3dKLG9CQUFvQixDQUFDckk7d0JBQzFELE9BQU9vSTtvQkFDVDtnQkFDRjtnQkFDQTtvQkFDRXBDLE9BQU9qSCxxRUFBWUEsQ0FBQ3dILG9CQUFvQjtvQkFDeENMLFdBQVc7d0JBQ1QsMkJBQTJCO3dCQUMzQixJQUFJdEYsd0JBQXdCOzRCQUMxQixJQUFJO2dDQUNGLHlCQUF5QjtnQ0FDekIsTUFBTTBILG1CQUFtQixNQUFNMUosbURBQWFBLENBQUMySix3QkFBd0IsQ0FBQztvQ0FDcEVDLE9BQU8sU0FBc0N4SSxPQUE3QkEsYUFBYXlJLFlBQVksRUFBQyxLQUEyQixPQUF4QnpJLGFBQWEwSSxVQUFVO29DQUNwRTFJLGNBQWNBO29DQUNkNkYsMEJBQTBCO2dDQUM1QjtnQ0FFQSxpQkFBaUI7Z0NBQ2pCc0Msb0JBQW9CRztnQ0FFcEIsT0FBTztvQ0FDTGhDLE1BQU07b0NBQ05xQyxvQkFBb0JMO29DQUNwQk0sZ0JBQWdCO2dDQUNsQjs0QkFDRixFQUFFLE9BQU9oSixPQUFPO2dDQUNka0YsUUFBUStELElBQUksQ0FBQyxrQkFBa0JqSjtnQ0FDL0IsMkJBQTJCO2dDQUMzQixPQUFPO29DQUFFMEcsTUFBTTtnQ0FBMkI7NEJBQzVDO3dCQUNGLE9BQU87NEJBQ0wscUJBQXFCOzRCQUNyQixNQUFNLElBQUlILFFBQVFDLENBQUFBLFVBQVdDLFdBQVdELFNBQVM7NEJBQ2pELE9BQU87Z0NBQUVFLE1BQU07Z0NBQWlDc0MsZ0JBQWdCOzRCQUFNO3dCQUN4RTtvQkFDRjtnQkFDRjtnQkFDQTtvQkFDRTVDLE9BQU9qSCxxRUFBWUEsQ0FBQ3lILG1CQUFtQjtvQkFDdkNOLFdBQVc7d0JBQ1QseUJBQXlCO3dCQUN6QixJQUFJOzRCQUNGcEIsUUFBUWdFLEdBQUcsQ0FBQzs0QkFDWmhFLFFBQVFnRSxHQUFHLENBQUMsU0FBUztnQ0FDbkI5SSxjQUFjQTtnQ0FDZCtJLGNBQWNmO2dDQUNkZ0Isb0JBQW9CO2dDQUNwQm5ELDBCQUEwQmpGOzRCQUM1Qjs0QkFFQSxNQUFNcUksb0JBQW9CLE1BQU1wSyxpREFBV0EsQ0FBQ3FLLDhCQUE4QixDQUFDO2dDQUN6RWxKLGNBQWNBO2dDQUNkK0ksY0FBY2Y7Z0NBQ2RnQixvQkFBb0I7Z0NBQ3BCbkQsMEJBQTBCakY7NEJBQzVCOzRCQUVBa0UsUUFBUWdFLEdBQUcsQ0FBQyxrQkFBa0JHOzRCQUU5QixJQUFJQSxrQkFBa0JFLE9BQU8sRUFBRTtvQ0FZWkYsMkNBRUxBO2dDQWJaLHNCQUFzQjtnQ0FDdEJmLG1CQUFtQjtvQ0FDakJrQixhQUFhSCxrQkFBa0JJLFlBQVk7b0NBQzNDQyxjQUFjTCxrQkFBa0JNLGVBQWU7b0NBQy9DQyxxQkFBcUJ4SjtvQ0FDckJ5Six5QkFBeUJSLGtCQUFrQlMsSUFBSTtvQ0FDL0NDLHNCQUFzQjt3Q0FDcEJDLFVBQVU7d0NBQ1ZDLFdBQVc7d0NBQ1hDLGdCQUFnQmIsa0JBQWtCUyxJQUFJLENBQUNLLGlCQUFpQjtvQ0FDMUQ7b0NBQ0FDLGVBQWVmLEVBQUFBLDRDQUFBQSxrQkFBa0JTLElBQUksQ0FBQ0ssaUJBQWlCLGNBQXhDZCxnRUFBQUEsMENBQTBDZSxhQUFhLEtBQUksQ0FBQztvQ0FDM0VDLGlCQUFpQmhCLGtCQUFrQlMsSUFBSSxDQUFDTyxlQUFlLElBQUksQ0FBQztvQ0FDNURDLFVBQVVqQixFQUFBQSw4Q0FBQUEsa0JBQWtCUyxJQUFJLENBQUNTLG1CQUFtQixjQUExQ2xCLGtFQUFBQSw0Q0FBNENpQixRQUFRLEtBQUksQ0FBQztvQ0FDbkVFLGNBQWM7d0NBQ1o7d0NBQ0E7d0NBQ0E7d0NBQ0E7d0NBQ0E7d0NBQ0E7cUNBQ0Q7b0NBQ0RDLFlBQVk7d0NBQ1Y7d0NBQ0E7d0NBQ0E7cUNBQ0Q7b0NBQ0RDLGNBQWM7d0NBQ1pDLE9BQU87d0NBQ1BDLE9BQU87b0NBQ1Q7b0NBQ0FDLG9CQUFvQjt3Q0FDbEJoQix5QkFBeUI7d0NBQ3pCaUIsMEJBQTBCO3dDQUMxQkMsd0JBQXdCO3dDQUN4QkMsNEJBQTRCO3dDQUM1QkMscUJBQXFCO29DQUN2QjtnQ0FDRjs0QkFDRixPQUFPO2dDQUNMLE1BQU0sSUFBSUMsTUFBTTs0QkFDbEI7d0JBQ0YsRUFBRSxPQUFPQyxnQkFBZ0I7NEJBQ3ZCakcsUUFBUWxGLEtBQUssQ0FBQyxrQkFBa0JtTDs0QkFDaENqRyxRQUFRbEYsS0FBSyxDQUFDLFNBQVM7Z0NBQ3JCOEcsU0FBU3FFLGVBQWVyRSxPQUFPO2dDQUMvQnNFLE9BQU9ELGVBQWVDLEtBQUs7Z0NBQzNCNUksTUFBTTJJLGVBQWUzSSxJQUFJOzRCQUMzQjs0QkFFQSxZQUFZOzRCQUNaOEYsbUJBQW1CO2dDQUNqQmtCLGFBQWEsWUFBdUIsT0FBWDlHLEtBQUtxQixHQUFHO2dDQUNqQzJGLGNBQWMsSUFBSWhILE9BQU8ySSxXQUFXO2dDQUNwQ3pCLHFCQUFxQnhKO2dDQUNyQjJKLHNCQUFzQjtvQ0FDcEJDLFVBQVU7b0NBQ1ZDLFdBQVc7b0NBQ1hDLGdCQUFnQixDQUFDO2dDQUNuQjtnQ0FDQUUsZUFBZSxDQUFDO2dDQUNoQkMsaUJBQWlCLENBQUM7Z0NBQ2xCQyxVQUFVLENBQUM7Z0NBQ1hFLGNBQWM7b0NBQ1o7b0NBQ0E7b0NBQ0E7aUNBQ0Q7Z0NBQ0RDLFlBQVk7b0NBQ1Y7b0NBQ0E7b0NBQ0E7aUNBQ0Q7Z0NBQ0RDLGNBQWM7b0NBQ1pDLE9BQU87b0NBQ1BDLE9BQU87Z0NBQ1Q7Z0NBQ0FVLGlCQUFpQjtvQ0FDZnhFLFNBQVM7b0NBQ1R5RSxnQkFBZ0I7b0NBQ2hCQyxRQUFRO2dDQUNWOzRCQUNGO3dCQUNGO3dCQUNBLE9BQU9sRDtvQkFDVDtnQkFDRjtnQkFDQTtvQkFDRWxDLE9BQU9qSCxxRUFBWUEsQ0FBQ2lJLFVBQVU7b0JBQzlCZCxXQUFXO3dCQUNULHVCQUF1Qjt3QkFDdkIsTUFBTSxJQUFJQyxRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTO3dCQUNqRCxPQUFPOzRCQUFFRSxNQUFNO3dCQUFxQjtvQkFDdEM7Z0JBQ0Y7YUFDRCxFQUNELENBQUNOLE9BQU85RTtnQkFDTixJQUFJLENBQUNGLFdBQVc7b0JBQ2RHLGFBQWE2RTtnQkFDZixPQUFPO29CQUNMNUUsWUFBWTRFLE9BQU85RTtnQkFDckI7WUFDRixHQUNBO2dCQUNFK0YsWUFBWTtnQkFDWkMsWUFBWTtnQkFDWkMscUJBQXFCO2dCQUNyQkMsV0FBVztnQkFDWEMsU0FBUyxDQUFDQyxTQUFTMUg7d0JBQ2JBLGdCQUFvQ0E7b0JBQXhDLElBQUlBLEVBQUFBLGlCQUFBQSxNQUFNOEcsT0FBTyxjQUFiOUcscUNBQUFBLGVBQWUySCxRQUFRLENBQUMsZUFBWTNILGtCQUFBQSxNQUFNOEcsT0FBTyxjQUFiOUcsc0NBQUFBLGdCQUFlMkgsUUFBUSxDQUFDLFFBQU87d0JBQ3JFckksMENBQUtBLENBQUNzSSxJQUFJLENBQUU7b0JBQ2Q7Z0JBQ0Y7WUFDRjtZQUVBLFdBQVc7WUFDWC9ILFlBQVksQ0FBQ3lGLE9BQ1hBLEtBQUtSLEdBQUcsQ0FBQ1UsQ0FBQUEsTUFDUEEsSUFBSW5ELEVBQUUsS0FBSytDLFlBQVkvQyxFQUFFLEdBQ3JCO3dCQUFFLEdBQUdtRCxHQUFHO3dCQUFFN0MsUUFBUTtvQkFBZ0IsSUFDbEM2QztZQUlSLDRCQUE0QjtZQUM1QixNQUFNaUcsa0JBQWtCQyx1QkFBdUJwRCxrQkFBa0JDO1lBRWpFLHNCQUFzQjtZQUN0QixJQUFJVCxVQUFVO2dCQUFDO2dCQUFVO2dCQUFTO2FBQVM7WUFFM0MscUJBQXFCO1lBQ3JCLEtBQUlRLHlDQUFBQSxpQkFBaUJxRCxvQkFBb0IsY0FBckNyRCw2REFBQUEsdUNBQXVDc0QsZUFBZSxFQUFFO2dCQUMxRDlELFVBQVU7b0JBQUM7b0JBQVk7b0JBQVU7b0JBQVM7aUJBQVE7Z0JBQ2xELElBQUlRLGlCQUFpQnFELG9CQUFvQixDQUFDRSxnQkFBZ0IsRUFBRTtvQkFDMUQvRCxVQUFVQSxRQUFRZ0UsTUFBTSxDQUFDeEQsaUJBQWlCcUQsb0JBQW9CLENBQUNFLGdCQUFnQjtnQkFDakY7WUFDRixPQUFPLElBQUl2RCxFQUFBQSxnQ0FBQUEsaUJBQWlCa0IsV0FBVyxjQUE1QmxCLG9EQUFBQSw4QkFBOEJYLFFBQVEsQ0FBQyxtQkFBa0JXLGlCQUFpQnlELG9CQUFvQixFQUFFO2dCQUN6R2pFLFVBQVU7b0JBQUM7b0JBQVk7b0JBQVM7b0JBQVU7b0JBQVc7aUJBQVM7WUFDaEUsT0FBTyxJQUFJUSxpQkFBaUJnRCxlQUFlLEVBQUU7Z0JBQzNDeEQsVUFBVTtvQkFBQztvQkFBUztvQkFBUztpQkFBUztZQUN4QztZQUVBLElBQUlTLHFCQUFxQnZILHdCQUF3QjtnQkFDL0M4RyxRQUFRa0UsSUFBSSxDQUFDLFNBQVM7WUFDeEI7WUFFQSxNQUFNbkUsYUFBc0I7Z0JBQzFCeEYsSUFBSSxDQUFDSyxLQUFLcUIsR0FBRyxLQUFLLEdBQUdFLFFBQVE7Z0JBQzdCM0IsTUFBTTtnQkFDTkMsU0FBU2tKO2dCQUNUaEosV0FBVyxJQUFJQztnQkFDZkMsUUFBUTtnQkFDUkMsWUFBWTBGLEVBQUFBLDBDQUFBQSxpQkFBaUJxRCxvQkFBb0IsY0FBckNyRCw4REFBQUEsd0NBQXVDMkQsZ0JBQWdCLEtBQ3ZEMUQsQ0FBQUEsb0JBQW9CLE9BQ3BCRCxpQkFBaUJnRCxlQUFlLEdBQUcsT0FBTyxJQUFHO2dCQUN6RHhELFNBQVNBO2dCQUNUakYsYUFBYTtvQkFDWDtvQkFDQTtvQkFDQTtvQkFDQTtpQkFDRDtZQUNIO1lBRUFoRCxZQUFZLENBQUN5RjtnQkFDWCxNQUFNeUMsY0FBYzt1QkFBSXpDO29CQUFNdUM7aUJBQVc7Z0JBQ3pDLFNBQVM7Z0JBQ1RwQixXQUFXLElBQU0zRCxxQkFBcUJpRixjQUFjO2dCQUNwRCxPQUFPQTtZQUNUO1lBRUF0RztZQUNBbkMsMENBQUtBLENBQUNpSyxPQUFPLENBQUM7UUFFaEIsRUFBRSxPQUFPdkIsS0FBVTtZQUNqQjlDLFFBQVFsRixLQUFLLENBQUMsV0FBV2dJO1lBRXpCLGNBQWM7WUFDZG5JLFlBQVksQ0FBQ3lGLE9BQ1hBLEtBQUtSLEdBQUcsQ0FBQ1UsQ0FBQUEsTUFDUEEsSUFBSW5ELEVBQUUsS0FBSytDLFlBQVkvQyxFQUFFLEdBQ3JCO3dCQUFFLEdBQUdtRCxHQUFHO3dCQUFFN0MsUUFBUTtvQkFBaUIsSUFDbkM2QztZQUlSOUQ7UUFDQSxzQkFBc0I7UUFDeEI7SUFDRjtJQUVBLHVCQUF1QjtJQUN2QixNQUFNMkcsNkJBQTZCLENBQUM2RDtRQUNsQyxNQUFNQyxXQUFXLEVBQUU7UUFFbkJBLFNBQVNILElBQUksQ0FBQztRQUVkLFNBQVM7UUFDVCxJQUFJRSxLQUFLRSxXQUFXLEVBQUVELFNBQVNILElBQUksQ0FBQywwQkFBaUMsT0FBakJFLEtBQUtFLFdBQVc7UUFDcEUsSUFBSUYsS0FBS0csY0FBYyxFQUFFRixTQUFTSCxJQUFJLENBQUMsMEJBQW9DLE9BQXBCRSxLQUFLRyxjQUFjO1FBQzFFLElBQUlILEtBQUtyRCxZQUFZLEVBQUVzRCxTQUFTSCxJQUFJLENBQUMsMEJBQWtDLE9BQWxCRSxLQUFLckQsWUFBWTtRQUV0RSxPQUFPO1FBQ1AsSUFBSXFELEtBQUtwRCxVQUFVLElBQUlvRCxLQUFLSSxXQUFXLElBQUlKLEtBQUtLLGVBQWUsSUFBSUwsS0FBS00sU0FBUyxFQUFFO1lBQ2pGTCxTQUFTSCxJQUFJLENBQUM7WUFDZCxJQUFJRSxLQUFLcEQsVUFBVSxFQUFFcUQsU0FBU0gsSUFBSSxDQUFDLFdBQTJCLE9BQWhCRSxLQUFLcEQsVUFBVTtZQUM3RCxJQUFJb0QsS0FBS0ksV0FBVyxFQUFFSCxTQUFTSCxJQUFJLENBQUMsV0FBNEIsT0FBakJFLEtBQUtJLFdBQVc7WUFDL0QsSUFBSUosS0FBS0ssZUFBZSxFQUFFSixTQUFTSCxJQUFJLENBQUMsV0FBZ0MsT0FBckJFLEtBQUtLLGVBQWU7WUFDdkUsSUFBSUwsS0FBS08sZ0JBQWdCLEVBQUVOLFNBQVNILElBQUksQ0FBQyxXQUFpQyxPQUF0QkUsS0FBS08sZ0JBQWdCO1lBQ3pFLElBQUlQLEtBQUtNLFNBQVMsRUFBRUwsU0FBU0gsSUFBSSxDQUFDLFdBQTBCLE9BQWZFLEtBQUtNLFNBQVM7WUFDM0QsSUFBSU4sS0FBS1EsYUFBYSxFQUFFUCxTQUFTSCxJQUFJLENBQUMsV0FBOEIsT0FBbkJFLEtBQUtRLGFBQWE7UUFDckU7UUFFQSxPQUFPO1FBQ1AsSUFBSVIsS0FBS1MsTUFBTSxJQUFJVCxLQUFLNUIsUUFBUSxJQUFJNEIsS0FBS1UsWUFBWSxFQUFFO1lBQ3JEVCxTQUFTSCxJQUFJLENBQUM7WUFDZCxJQUFJRSxLQUFLUyxNQUFNLEVBQUVSLFNBQVNILElBQUksQ0FBQyxXQUF1QixPQUFaRSxLQUFLUyxNQUFNO1lBQ3JELElBQUlULEtBQUs1QixRQUFRLEVBQUU2QixTQUFTSCxJQUFJLENBQUMsV0FBeUIsT0FBZEUsS0FBSzVCLFFBQVE7WUFDekQsSUFBSTRCLEtBQUtVLFlBQVksRUFBRVQsU0FBU0gsSUFBSSxDQUFDLFdBQTZCLE9BQWxCRSxLQUFLVSxZQUFZO1FBQ25FO1FBRUEsT0FBTztRQUNQLElBQUlWLEtBQUtXLGVBQWUsSUFBSVgsS0FBS1ksWUFBWSxJQUFJWixLQUFLYSxpQkFBaUIsSUFBSWIsS0FBS2MsZ0JBQWdCLElBQUlkLEtBQUtlLG1CQUFtQixFQUFFO1lBQzVIZCxTQUFTSCxJQUFJLENBQUM7WUFDZCxJQUFJRSxLQUFLVyxlQUFlLEVBQUVWLFNBQVNILElBQUksQ0FBQyxXQUFnQyxPQUFyQkUsS0FBS1csZUFBZTtZQUN2RSxJQUFJWCxLQUFLWSxZQUFZLEVBQUVYLFNBQVNILElBQUksQ0FBQyxXQUE2QixPQUFsQkUsS0FBS1ksWUFBWTtZQUNqRSxJQUFJWixLQUFLYSxpQkFBaUIsRUFBRVosU0FBU0gsSUFBSSxDQUFDLGFBQW9DLE9BQXZCRSxLQUFLYSxpQkFBaUI7WUFDN0UsSUFBSWIsS0FBS2MsZ0JBQWdCLEVBQUViLFNBQVNILElBQUksQ0FBQyxXQUFpQyxPQUF0QkUsS0FBS2MsZ0JBQWdCO1lBQ3pFLElBQUlkLEtBQUtlLG1CQUFtQixFQUFFZCxTQUFTSCxJQUFJLENBQUMsV0FBb0MsT0FBekJFLEtBQUtlLG1CQUFtQjtRQUNqRjtRQUVBZCxTQUFTSCxJQUFJLENBQUM7UUFFZCxPQUFPRyxTQUFTZSxJQUFJLENBQUM7SUFDdkI7SUFFQSx5QkFBeUI7SUFDekIsTUFBTXhCLHlCQUF5QixDQUFDcEQsa0JBQXVCQztZQTRRNUJBO1FBM1F6QixJQUFJLENBQUNELGtCQUFrQixPQUFPO1FBRTlCLE1BQU0sRUFDSmtCLFdBQVcsRUFDWEksbUJBQW1CLEVBQ25CRyxvQkFBb0IsRUFDcEJLLGFBQWEsRUFDYkMsZUFBZSxFQUNmQyxRQUFRLEVBQ1JFLFlBQVksRUFDWkMsVUFBVSxFQUNWQyxZQUFZLEVBQ1pxQixvQkFBb0IsRUFDcEJvQixlQUFlLEVBQ2ZDLGVBQWUsRUFDZkMscUJBQXFCLEVBQ3JCMUIsb0JBQW9CLEVBQ3BCTCxlQUFlLEVBQ2hCLEdBQUdoRDtRQUVKLE1BQU02RCxXQUFXLEVBQUU7UUFFbkIsb0JBQW9CO1FBQ3BCLE1BQU1tQixpQkFBaUJELDBCQUEwQixVQUFVRjtRQUMzRCxNQUFNSSw4QkFBOEIvRCxDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWE3QixRQUFRLENBQUMsbUJBQWtCb0U7UUFDNUUsTUFBTXlCLGVBQWU3QixDQUFBQSxpQ0FBQUEsMkNBQUFBLHFCQUFzQkMsZUFBZSxNQUFJcEMsd0JBQUFBLGtDQUFBQSxZQUFhN0IsUUFBUSxDQUFDO1FBQ3BGLE1BQU04RixhQUFhbkMsb0JBQW1COUIsd0JBQUFBLGtDQUFBQSxZQUFhN0IsUUFBUSxDQUFDO1FBRTVELG1CQUFtQjtRQUNuQixJQUFJOEYsWUFBWTtZQUNkdEIsU0FBU0gsSUFBSSxDQUFDO1lBQ2RHLFNBQVNILElBQUksQ0FBQztZQUNkRyxTQUFTSCxJQUFJLENBQUMsU0FBcUIsT0FBWnhDO1lBQ3ZCMkMsU0FBU0gsSUFBSSxDQUFDO1lBRWQsU0FBUztZQUNULElBQUlWLGlCQUFpQjtnQkFDbkJhLFNBQVNILElBQUksQ0FBQztnQkFDZEcsU0FBU0gsSUFBSSxDQUFDLEtBQTZCLE9BQXhCVixnQkFBZ0J4RSxPQUFPO2dCQUMxQyxJQUFJd0UsZ0JBQWdCQyxjQUFjLEVBQUU7b0JBQ2xDWSxTQUFTSCxJQUFJLENBQUM7Z0JBQ2hCO2dCQUNBRyxTQUFTSCxJQUFJLENBQUMsU0FBZ0MsT0FBdkJWLGdCQUFnQkUsTUFBTTtnQkFDN0NXLFNBQVNILElBQUksQ0FBQztZQUNoQjtRQUNGLE9BQU8sSUFBSXdCLGdCQUFnQjdCLHNCQUFzQjtnQkFTM0NBO1lBUkpRLFNBQVNILElBQUksQ0FBQztZQUNkRyxTQUFTSCxJQUFJLENBQUM7WUFDZEcsU0FBU0gsSUFBSSxDQUFDLFNBQXFCLE9BQVp4QztZQUN2QjJDLFNBQVNILElBQUksQ0FBQztZQUVkLFdBQVc7WUFDWEcsU0FBU0gsSUFBSSxDQUFDO1lBQ2RHLFNBQVNILElBQUksQ0FBQyxXQUE2RSxPQUFsRWxJLEtBQUs0SixLQUFLLENBQUMsQ0FBQy9CLHFCQUFxQk0sZ0JBQWdCLElBQUksSUFBRyxJQUFLLE1BQUs7WUFDM0YsSUFBSU4sRUFBQUEseUNBQUFBLHFCQUFxQkUsZ0JBQWdCLGNBQXJDRiw2REFBQUEsdUNBQXVDNUksTUFBTSxJQUFHLEdBQUc7Z0JBQ3JEb0osU0FBU0gsSUFBSSxDQUFDLFVBQTJELE9BQWpETCxxQkFBcUJFLGdCQUFnQixDQUFDcUIsSUFBSSxDQUFDO1lBQ3JFO1lBQ0FmLFNBQVNILElBQUksQ0FBQztZQUNkRyxTQUFTSCxJQUFJLENBQUM7UUFDaEIsT0FBTyxJQUFJc0Isa0JBQWtCRixpQkFBaUI7WUFDNUNqQixTQUFTSCxJQUFJLENBQUMsK0JBQXFDLE9BQWhCb0IsaUJBQWdCO1lBQ25EakIsU0FBU0gsSUFBSSxDQUFFO1lBQ2ZHLFNBQVNILElBQUksQ0FBQyxTQUFxQixPQUFaeEM7WUFDdkIyQyxTQUFTSCxJQUFJLENBQUM7WUFFZCxXQUFXO1lBQ1gsSUFBSW1CLGlCQUFpQjtnQkFDbkJoQixTQUFTSCxJQUFJLENBQUM7Z0JBQ2RHLFNBQVNILElBQUksQ0FBQyxVQUFvRCxPQUExQ21CLGdCQUFnQkMsZUFBZSxJQUFJO2dCQUMzRGpCLFNBQVNILElBQUksQ0FBQyxVQUFtRCxPQUF6Q21CLGdCQUFnQlEsZUFBZSxJQUFJO2dCQUMzRHhCLFNBQVNILElBQUksQ0FBQyxVQUFnRCxPQUF0Q21CLGdCQUFnQlMsYUFBYSxJQUFJO2dCQUN6RHpCLFNBQVNILElBQUksQ0FBQyxVQUFpRSxPQUF2RG1CLGdCQUFnQlUsb0JBQW9CLElBQUk7Z0JBQ2hFMUIsU0FBU0gsSUFBSSxDQUFDLFNBQXNFLE9BQTdEbEksS0FBSzRKLEtBQUssQ0FBQyxDQUFDUCxnQkFBZ0JsQixnQkFBZ0IsSUFBSSxJQUFHLElBQUssTUFBSztnQkFDcEZFLFNBQVNILElBQUksQ0FBQztZQUNoQjtRQUNGLE9BQU8sSUFBSXVCLDZCQUE2QjtZQUN0Q3BCLFNBQVNILElBQUksQ0FBQztZQUNkRyxTQUFTSCxJQUFJLENBQUMsU0FBcUIsT0FBWnhDO1lBQ3ZCMkMsU0FBU0gsSUFBSSxDQUFDO1FBQ2hCLE9BQU87WUFDTEcsU0FBU0gsSUFBSSxDQUFDO1lBQ2RHLFNBQVNILElBQUksQ0FBQyxTQUFxQixPQUFaeEM7WUFDdkIyQyxTQUFTSCxJQUFJLENBQUM7UUFDaEI7UUFNQSxtQkFBbUI7UUFDbkIsSUFBSTNCLGlCQUFpQjtnQkFZZkEsa0NBYUtBO1lBeEJUOEIsU0FBU0gsSUFBSSxDQUFDO1lBRWQsSUFBSTNCLGdCQUFnQnlELG1CQUFtQixFQUFFO2dCQUN2QzNCLFNBQVNILElBQUksQ0FBQyxhQUFpRCxPQUFwQzNCLGdCQUFnQnlELG1CQUFtQjtZQUNoRTtZQUVBLElBQUl6RCxnQkFBZ0IwRCxrQkFBa0IsRUFBRTtnQkFDdEM1QixTQUFTSCxJQUFJLENBQUMsZUFBa0QsT0FBbkMzQixnQkFBZ0IwRCxrQkFBa0I7WUFDakU7WUFFQSxVQUFVO1lBQ1YsSUFBSTFELEVBQUFBLG1DQUFBQSxnQkFBZ0IyRCxlQUFlLGNBQS9CM0QsdURBQUFBLGlDQUFpQ3RILE1BQU0sSUFBRyxHQUFHO2dCQUMvQ29KLFNBQVNILElBQUksQ0FBQztnQkFDZDNCLGdCQUFnQjJELGVBQWUsQ0FBQ0MsT0FBTyxDQUFDLENBQUNDO29CQUN2QyxJQUFJLE9BQU9BLFNBQVMsVUFBVTt3QkFDNUIvQixTQUFTSCxJQUFJLENBQUMsS0FBbUJrQyxPQUFkQSxLQUFLQSxJQUFJLEVBQUMsTUFBcUIsT0FBakJBLEtBQUtDLFdBQVcsRUFBQzt3QkFDbERoQyxTQUFTSCxJQUFJLENBQUMsU0FBcUIsT0FBWmtDLEtBQUtFLE1BQU07d0JBQ2xDakMsU0FBU0gsSUFBSSxDQUFDLFNBQXlCLE9BQWhCa0MsS0FBS0csVUFBVTtvQkFDeEMsT0FBTzt3QkFDTGxDLFNBQVNILElBQUksQ0FBQyxLQUFVLE9BQUxrQztvQkFDckI7Z0JBQ0Y7WUFDRixPQUVLLElBQUk3RCxFQUFBQSxvQ0FBQUEsZ0JBQWdCaUUsZ0JBQWdCLGNBQWhDakUsd0RBQUFBLGtDQUFrQ3RILE1BQU0sSUFBRyxHQUFHO2dCQUNyRG9KLFNBQVNILElBQUksQ0FBQztnQkFDZDNCLGdCQUFnQmlFLGdCQUFnQixDQUFDTCxPQUFPLENBQUMsQ0FBQ0M7b0JBQ3hDL0IsU0FBU0gsSUFBSSxDQUFDLEtBQVUsT0FBTGtDO2dCQUNyQjtZQUNGO1lBQ0EvQixTQUFTSCxJQUFJLENBQUM7UUFDaEI7UUFJQSxvQkFBb0I7UUFDcEIsSUFBSUQsc0JBQXNCO2dCQUVwQkEsa0NBMERBQSxtRUFBQUEsa0RBd0JBQSxzQ0FTQUE7WUE1Rkosb0JBQW9CO1lBQ3BCLElBQUlBLEVBQUFBLG1DQUFBQSxxQkFBcUJ3QyxVQUFVLGNBQS9CeEMsdURBQUFBLGlDQUFpQ2hKLE1BQU0sSUFBRyxHQUFHO2dCQUMvQ29KLFNBQVNILElBQUksQ0FBQztnQkFDZEcsU0FBU0gsSUFBSSxDQUFDO2dCQUNkRyxTQUFTSCxJQUFJLENBQUM7Z0JBQ2RELHFCQUFxQndDLFVBQVUsQ0FBQ04sT0FBTyxDQUFDLENBQUNPLE9BQVlDO29CQUNuRCxNQUFNQyxhQUFhRixNQUFNRyxHQUFHLEdBQ3hCLHlDQUF1RSxPQUE5QkMsbUJBQW1CSixNQUFNRyxHQUFHLEdBQUUsV0FDdkUseUNBQXlFLE9BQWhDQyxtQkFBbUJKLE1BQU1wSyxLQUFLO29CQUMzRSxNQUFNeUssY0FBYyx3Q0FBd0UsT0FBaENELG1CQUFtQkosTUFBTXBLLEtBQUs7b0JBRTFGK0gsU0FBU0gsSUFBSSxDQUFDLE1BQW9Cd0MsT0FBZEMsUUFBUSxHQUFFLE1BQWdCLE9BQVpELE1BQU1wSyxLQUFLLEVBQUM7b0JBQzlDK0gsU0FBU0gsSUFBSSxDQUFDLGdCQUF1QndDLE9BQWpCQSxNQUFNTSxPQUFPLEVBQUMsS0FBNkQsT0FBMUROLE1BQU1PLGFBQWEsR0FBRyxRQUE0QixPQUFwQlAsTUFBTU8sYUFBYSxFQUFDLE9BQUs7b0JBQzVGNUMsU0FBU0gsSUFBSSxDQUFDLGdCQUFtQixPQUFid0MsTUFBTWhELE1BQU07b0JBQ2hDVyxTQUFTSCxJQUFJLENBQUMseUJBQWlENkMsT0FBbENILFlBQVcseUJBQW1DLE9BQVpHLGFBQVk7b0JBQzNFMUMsU0FBU0gsSUFBSSxDQUFDO2dCQUNoQjtZQUNGO1lBRUEsNEJBQTRCO1lBQzVCLElBQUlELHFCQUFxQmlELGlCQUFpQixFQUFFO29CQUd0Q0MsOEJBQTJDQTtnQkFGL0MsTUFBTUEsV0FBV2xELHFCQUFxQmlELGlCQUFpQjtnQkFFdkQsSUFBSUMsRUFBQUEsK0JBQUFBLFNBQVNDLGtCQUFrQixjQUEzQkQsbURBQUFBLDZCQUE2QmxNLE1BQU0sSUFBRyxLQUFLa00sRUFBQUEsMkJBQUFBLFNBQVNFLGNBQWMsY0FBdkJGLCtDQUFBQSx5QkFBeUJsTSxNQUFNLElBQUcsR0FBRzt3QkFJOUVrTSwrQkFVQUEsMkJBVUFBO29CQXZCSjlDLFNBQVNILElBQUksQ0FBQztvQkFDZEcsU0FBU0gsSUFBSSxDQUFDO29CQUVkLElBQUlpRCxFQUFBQSxnQ0FBQUEsU0FBU0Msa0JBQWtCLGNBQTNCRCxvREFBQUEsOEJBQTZCbE0sTUFBTSxJQUFHLEdBQUc7d0JBQzNDb0osU0FBU0gsSUFBSSxDQUFDO3dCQUNkaUQsU0FBU0Msa0JBQWtCLENBQUNuSyxLQUFLLENBQUMsR0FBRyxHQUFHa0osT0FBTyxDQUFDLENBQUNtQjs0QkFDL0MsTUFBTVYsYUFBYSx5Q0FBcUUsT0FBNUJFLG1CQUFtQlE7NEJBQy9FLE1BQU1QLGNBQWMsd0NBQW9FLE9BQTVCRCxtQkFBbUJROzRCQUMvRWpELFNBQVNILElBQUksQ0FBQyxLQUEyQjBDLE9BQXRCVSxTQUFRLGdCQUF5Q1AsT0FBM0JILFlBQVcsa0JBQTRCLE9BQVpHLGFBQVk7d0JBQ2xGO3dCQUNBMUMsU0FBU0gsSUFBSSxDQUFDO29CQUNoQjtvQkFFQSxJQUFJaUQsRUFBQUEsNEJBQUFBLFNBQVNFLGNBQWMsY0FBdkJGLGdEQUFBQSwwQkFBeUJsTSxNQUFNLElBQUcsR0FBRzt3QkFDdkNvSixTQUFTSCxJQUFJLENBQUM7d0JBQ2RpRCxTQUFTRSxjQUFjLENBQUNwSyxLQUFLLENBQUMsR0FBRyxHQUFHa0osT0FBTyxDQUFDLENBQUNtQjs0QkFDM0MsTUFBTVYsYUFBYSx5Q0FBcUUsT0FBNUJFLG1CQUFtQlE7NEJBQy9FLE1BQU1QLGNBQWMsd0NBQW9FLE9BQTVCRCxtQkFBbUJROzRCQUMvRWpELFNBQVNILElBQUksQ0FBQyxLQUEyQjBDLE9BQXRCVSxTQUFRLGdCQUF5Q1AsT0FBM0JILFlBQVcsa0JBQTRCLE9BQVpHLGFBQVk7d0JBQ2xGO3dCQUNBMUMsU0FBU0gsSUFBSSxDQUFDO29CQUNoQjtvQkFFQSxJQUFJaUQsRUFBQUEsOEJBQUFBLFNBQVNJLGlCQUFpQixjQUExQkosa0RBQUFBLDRCQUE0QmxNLE1BQU0sSUFBRyxHQUFHO3dCQUMxQ29KLFNBQVNILElBQUksQ0FBQzt3QkFDZGlELFNBQVNJLGlCQUFpQixDQUFDdEssS0FBSyxDQUFDLEdBQUcsR0FBR2tKLE9BQU8sQ0FBQyxDQUFDbUI7NEJBQzlDLE1BQU1WLGFBQWEseUNBQXFFLE9BQTVCRSxtQkFBbUJROzRCQUMvRWpELFNBQVNILElBQUksQ0FBQyxLQUEyQjBDLE9BQXRCVSxTQUFRLGdCQUF5QixPQUFYVixZQUFXO3dCQUN0RDt3QkFDQXZDLFNBQVNILElBQUksQ0FBQztvQkFDaEI7Z0JBQ0Y7WUFDRjtZQUVBLFdBQVc7WUFDWCxJQUFJRCxFQUFBQSxtREFBQUEscUJBQXFCZiwwQkFBMEIsY0FBL0NlLHdFQUFBQSxvRUFBQUEsaURBQWlEdUQsZ0JBQWdCLGNBQWpFdkQsd0ZBQUFBLGtFQUFtRWhKLE1BQU0sSUFBRyxHQUFHO2dCQUNqRm9KLFNBQVNILElBQUksQ0FBQztnQkFDZEcsU0FBU0gsSUFBSSxDQUFDO2dCQUNkRCxxQkFBcUJmLDBCQUEwQixDQUFDc0UsZ0JBQWdCLENBQUN2SyxLQUFLLENBQUMsR0FBRyxHQUFHa0osT0FBTyxDQUFDLENBQUNPLE9BQVlDO3dCQVM1RkQ7b0JBUkosTUFBTUUsYUFBYUYsTUFBTWUsU0FBUyxHQUM5QixtQ0FBbUQsT0FBaEJmLE1BQU1lLFNBQVMsRUFBQyxPQUNuRGYsTUFBTUcsR0FBRyxHQUNULHlDQUF1RSxPQUE5QkMsbUJBQW1CSixNQUFNRyxHQUFHLEdBQUUsV0FDdkUseUNBQXlFLE9BQWhDQyxtQkFBbUJKLE1BQU1wSyxLQUFLO29CQUUzRStILFNBQVNILElBQUksQ0FBQyxNQUFvQndDLE9BQWRDLFFBQVEsR0FBRSxNQUFnQixPQUFaRCxNQUFNcEssS0FBSyxFQUFDO29CQUM5QytILFNBQVNILElBQUksQ0FBQyxnQkFBdUJ3QyxPQUFqQkEsTUFBTU0sT0FBTyxFQUFDLEtBQStELE9BQTVETixNQUFNZ0IsZ0JBQWdCLEdBQUcsSUFBMkIsT0FBdkJoQixNQUFNZ0IsZ0JBQWdCLEVBQUMsT0FBSztvQkFDOUYsSUFBSWhCLEVBQUFBLGlCQUFBQSxNQUFNaUIsT0FBTyxjQUFiakIscUNBQUFBLGVBQWV6TCxNQUFNLElBQUcsR0FBRzt3QkFDN0JvSixTQUFTSCxJQUFJLENBQUMsZ0JBQTZDd0MsT0FBdkNBLE1BQU1pQixPQUFPLENBQUMxSyxLQUFLLENBQUMsR0FBRyxHQUFHbUksSUFBSSxDQUFDLE9BQWtELE9BQTFDc0IsTUFBTWlCLE9BQU8sQ0FBQzFNLE1BQU0sR0FBRyxJQUFJLFlBQVk7b0JBQ3BHO29CQUNBLElBQUl5TCxNQUFNa0IsUUFBUSxFQUFFO3dCQUNsQnZELFNBQVNILElBQUksQ0FBQyxnQkFBdUMsT0FBakN3QyxNQUFNa0IsUUFBUSxDQUFDOUwsU0FBUyxDQUFDLEdBQUcsTUFBSztvQkFDdkQ7b0JBQ0F1SSxTQUFTSCxJQUFJLENBQUMsdUJBQWtDd0MsT0FBckJFLFlBQVcsWUFBb0MsT0FBMUJGLE1BQU1tQixNQUFNLElBQUk7b0JBQ2hFeEQsU0FBU0gsSUFBSSxDQUFDO2dCQUNoQjtZQUNGO1lBRUEsU0FBUztZQUNULElBQUlELEVBQUFBLHVDQUFBQSxxQkFBcUI2RCxjQUFjLGNBQW5DN0QsMkRBQUFBLHFDQUFxQ2hKLE1BQU0sSUFBRyxHQUFHO2dCQUNuRG9KLFNBQVNILElBQUksQ0FBQztnQkFDZEQscUJBQXFCNkQsY0FBYyxDQUFDM0IsT0FBTyxDQUFDLENBQUM0QjtvQkFDM0MxRCxTQUFTSCxJQUFJLENBQUMsS0FBYSxPQUFSNkQ7Z0JBQ3JCO2dCQUNBMUQsU0FBU0gsSUFBSSxDQUFDO1lBQ2hCO1lBRUEsV0FBVztZQUNYLElBQUlELEVBQUFBLG9EQUFBQSxxQkFBcUIrRCwyQkFBMkIsY0FBaEQvRCx3RUFBQUEsa0RBQWtEaEosTUFBTSxJQUFHLEdBQUc7Z0JBQ2hFb0osU0FBU0gsSUFBSSxDQUFDO2dCQUNkRCxxQkFBcUIrRCwyQkFBMkIsQ0FBQzdCLE9BQU8sQ0FBQyxDQUFDOEI7b0JBQ3hENUQsU0FBU0gsSUFBSSxDQUFDLE9BQWlDK0QsT0FBMUJBLE9BQU9DLFdBQVcsRUFBQyxTQUFxQixPQUFkRCxPQUFPRSxNQUFNO29CQUM1RDlELFNBQVNILElBQUksQ0FBQyxLQUErQixPQUExQitELE9BQU9HLGtCQUFrQjtnQkFDOUM7Z0JBQ0EvRCxTQUFTSCxJQUFJLENBQUM7WUFDaEI7WUFFQSxXQUFXO1lBQ1gsSUFBSUQscUJBQXFCb0Usa0JBQWtCLEVBQUU7Z0JBQzNDaEUsU0FBU0gsSUFBSSxDQUFDO2dCQUNkRyxTQUFTSCxJQUFJLENBQUM7Z0JBRWRHLFNBQVNILElBQUksQ0FBQztnQkFDZEQscUJBQXFCb0Usa0JBQWtCLENBQUNDLGVBQWUsQ0FBQ25DLE9BQU8sQ0FBQyxDQUFDbUIsU0FBaUJYO29CQUNoRnRDLFNBQVNILElBQUksQ0FBQyxHQUFtQm9ELE9BQWhCWCxRQUFRLEdBQUUsUUFBYyxPQUFSVyxTQUFRO29CQUV6QyxpQkFBaUI7b0JBQ2pCLE1BQU1pQixRQUFRdEUscUJBQXFCb0Usa0JBQWtCLENBQUNHLFdBQVcsQ0FBQ2xCLFFBQVE7b0JBQzFFLElBQUlpQixTQUFTQSxNQUFNdE4sTUFBTSxHQUFHLEdBQUc7d0JBQzdCLE1BQU13TixZQUFZRixNQUFNdkwsR0FBRyxDQUFDLENBQUMwTCxPQUMzQixJQUFrQkEsT0FBZEEsS0FBS0MsS0FBSyxFQUFDLEtBQWlCRCxPQUFkQSxLQUFLaE8sSUFBSSxFQUFDLE1BQWEsT0FBVGdPLEtBQUtFLEdBQUcsRUFBQyxNQUN6Q3hELElBQUksQ0FBQzt3QkFDUGYsU0FBU0gsSUFBSSxDQUFDLE1BQWdCLE9BQVZ1RTtvQkFDdEI7b0JBQ0FwRSxTQUFTSCxJQUFJLENBQUM7Z0JBQ2hCO2dCQUVBLElBQUlELHFCQUFxQm9FLGtCQUFrQixDQUFDUSxpQkFBaUIsQ0FBQzVOLE1BQU0sR0FBRyxHQUFHO29CQUN4RW9KLFNBQVNILElBQUksQ0FBQztvQkFDZEQscUJBQXFCb0Usa0JBQWtCLENBQUNRLGlCQUFpQixDQUFDNUwsS0FBSyxDQUFDLEdBQUcsR0FBR2tKLE9BQU8sQ0FBQyxDQUFDbUI7d0JBQzdFakQsU0FBU0gsSUFBSSxDQUFDLEtBQWEsT0FBUm9EO29CQUNyQjtvQkFDQWpELFNBQVNILElBQUksQ0FBQztnQkFDaEI7Z0JBRUFHLFNBQVNILElBQUksQ0FBQztnQkFDZEcsU0FBU0gsSUFBSSxDQUFDO2dCQUNkRyxTQUFTSCxJQUFJLENBQUM7Z0JBQ2RHLFNBQVNILElBQUksQ0FBQztnQkFDZEcsU0FBU0gsSUFBSSxDQUFDO2dCQUNkRyxTQUFTSCxJQUFJLENBQUM7WUFDaEI7UUFDRjtRQUVBLHNCQUFzQjtRQUN0QixJQUFJekQscUJBQXFCQSxFQUFBQSx3Q0FBQUEsa0JBQWtCUSxrQkFBa0IsY0FBcENSLDREQUFBQSxzQ0FBc0N4RixNQUFNLElBQUcsR0FBRztZQUN6RW9KLFNBQVNILElBQUksQ0FBQztZQUNkRyxTQUFTSCxJQUFJLENBQUM7WUFDZEcsU0FBU0gsSUFBSSxDQUFDO1lBRWQsbUJBQW1CO1lBQ25CLE1BQU00RSxlQUFlckksa0JBQWtCUSxrQkFBa0IsQ0FBQ2hFLEtBQUssQ0FBQyxHQUFHO1lBQ25FNkwsYUFBYTNDLE9BQU8sQ0FBQyxDQUFDTyxPQUFZQztnQkFDaEN0QyxTQUFTSCxJQUFJLENBQUMsTUFBb0J3QyxPQUFkQyxRQUFRLEdBQUUsTUFBMEIsT0FBdEJELE1BQU1wSyxLQUFLLElBQUksUUFBTztnQkFFeEQsSUFBSW9LLE1BQU1pQixPQUFPLElBQUlqQixNQUFNaUIsT0FBTyxDQUFDMU0sTUFBTSxHQUFHLEdBQUc7b0JBQzdDLE1BQU04TixhQUFhckMsTUFBTWlCLE9BQU8sQ0FBQzFLLEtBQUssQ0FBQyxHQUFHLEdBQUdtSSxJQUFJLENBQUM7b0JBQ2xELE1BQU00RCxjQUFjdEMsTUFBTWlCLE9BQU8sQ0FBQzFNLE1BQU0sR0FBRyxJQUFJLE9BQU87b0JBQ3REb0osU0FBU0gsSUFBSSxDQUFDLElBQWlCOEUsT0FBYkQsWUFBeUIsT0FBWkMsYUFBWTtnQkFDN0M7Z0JBRUEsSUFBSXRDLE1BQU1NLE9BQU8sRUFBRTtvQkFDakIzQyxTQUFTSCxJQUFJLENBQUMsZ0JBQXNCd0MsT0FBaEJBLE1BQU1NLE9BQU8sRUFBZ0UsT0FBN0ROLE1BQU1nQixnQkFBZ0IsR0FBRyxLQUE0QixPQUF2QmhCLE1BQU1nQixnQkFBZ0IsRUFBQyxPQUFLO2dCQUNoRztnQkFFQSxJQUFJaEIsTUFBTXVDLFlBQVksRUFBRTtvQkFDdEI1RSxTQUFTSCxJQUFJLENBQUMsMEJBQW1DLE9BQW5Cd0MsTUFBTXVDLFlBQVk7Z0JBQ2xEO2dCQUVBLElBQUl2QyxNQUFNd0MsbUJBQW1CLEVBQUU7b0JBQzdCN0UsU0FBU0gsSUFBSSxDQUFDLHdCQUF3QyxPQUExQndDLE1BQU13QyxtQkFBbUI7Z0JBQ3ZEO2dCQUVBN0UsU0FBU0gsSUFBSSxDQUFDO1lBQ2hCO1lBRUEsV0FBVztZQUNYLElBQUl6RCxrQkFBa0IwSSxZQUFZLEdBQUcsR0FBRztnQkFDdEM5RSxTQUFTSCxJQUFJLENBQUMsOEJBQW9FNEUsT0FBaERySSxrQkFBa0IwSSxZQUFZLEVBQUMsbUJBQXFDLE9BQXBCTCxhQUFhN04sTUFBTSxFQUFDO2dCQUN0R29KLFNBQVNILElBQUksQ0FBQztZQUNoQjtZQUVBLFNBQVM7WUFDVCxJQUFJekQsa0JBQWtCVCxPQUFPLElBQUlTLGtCQUFrQlQsT0FBTyxDQUFDL0UsTUFBTSxHQUFHLEdBQUc7Z0JBQ3JFb0osU0FBU0gsSUFBSSxDQUFDLDBCQUFxRCxPQUFyQ3pELGtCQUFrQlQsT0FBTyxDQUFDb0YsSUFBSSxDQUFDO2dCQUM3RGYsU0FBU0gsSUFBSSxDQUFDO1lBQ2hCO1FBQ0Y7UUFFQSxzQkFBc0I7UUFDdEIsTUFBTSxFQUFFbkMsdUJBQXVCLEVBQUVnQixrQkFBa0IsRUFBRSxHQUFHdkM7UUFDeEQsSUFBSXVCLDRCQUEyQmdCLCtCQUFBQSx5Q0FBQUEsbUJBQW9CaEIsdUJBQXVCLEdBQUU7WUFDMUVzQyxTQUFTSCxJQUFJLENBQUM7WUFDZEcsU0FBU0gsSUFBSSxDQUFDO1lBQ2RHLFNBQVNILElBQUksQ0FBQztZQUVkLHVCQUF1QjtZQUN2QkcsU0FBU0gsSUFBSSxDQUFDO1lBQ2RHLFNBQVNILElBQUksQ0FBQzlJLEtBQUsrQixTQUFTLENBQUM7Z0JBQzNCM0MsTUFBTTtnQkFDTndILE1BQU1EO1lBQ1IsR0FBRyxNQUFNO1lBQ1RzQyxTQUFTSCxJQUFJLENBQUM7WUFDZEcsU0FBU0gsSUFBSSxDQUFDO1FBQ2hCO1FBRUEsMkJBQTJCO1FBQzNCLE1BQU0sRUFBRWtGLHNCQUFzQixFQUFFQyxpQkFBaUIsRUFBRSxHQUFHN0k7UUFDdEQsSUFBSTRJLDJCQUEwQkMsOEJBQUFBLHdDQUFBQSxrQkFBbUJDLG1CQUFtQixHQUFFO1lBQ3BFakYsU0FBU0gsSUFBSSxDQUFDO1lBQ2RHLFNBQVNILElBQUksQ0FBQztZQUNkRyxTQUFTSCxJQUFJLENBQUM7WUFFZCx5QkFBeUI7WUFDekJHLFNBQVNILElBQUksQ0FBQztZQUNkRyxTQUFTSCxJQUFJLENBQUM5SSxLQUFLK0IsU0FBUyxDQUFDO2dCQUMzQjNDLE1BQU07Z0JBQ053SCxNQUFNb0g7WUFDUixHQUFHLE1BQU07WUFDVC9FLFNBQVNILElBQUksQ0FBQztZQUVkRyxTQUFTSCxJQUFJLENBQUM7WUFDZEcsU0FBU0gsSUFBSSxDQUFDO1lBQ2RHLFNBQVNILElBQUksQ0FBQztZQUNkRyxTQUFTSCxJQUFJLENBQUM7WUFDZEcsU0FBU0gsSUFBSSxDQUFDO1lBQ2RHLFNBQVNILElBQUksQ0FBQztZQUNkRyxTQUFTSCxJQUFJLENBQUM7WUFDZEcsU0FBU0gsSUFBSSxDQUFDO1FBQ2hCO1FBRUFHLFNBQVNILElBQUksQ0FBQztRQUNkLElBQUl5QixZQUFZO1lBQ2R0QixTQUFTSCxJQUFJLENBQUM7WUFDZEcsU0FBU0gsSUFBSSxDQUFDO1lBQ2RHLFNBQVNILElBQUksQ0FBQztRQUNoQixPQUFPLElBQUl3QixjQUFjO1lBQ3ZCckIsU0FBU0gsSUFBSSxDQUFDO1lBQ2RHLFNBQVNILElBQUksQ0FBQztZQUNkLE1BQU1wSixhQUFhK0ksQ0FBQUEsaUNBQUFBLDJDQUFBQSxxQkFBc0JNLGdCQUFnQixJQUFHbkksS0FBSzRKLEtBQUssQ0FBQy9CLHFCQUFxQk0sZ0JBQWdCLEdBQUcsT0FBTztZQUN0SEUsU0FBU0gsSUFBSSxDQUFDLGdCQUEyQixPQUFYcEosWUFBVztRQUMzQyxPQUFPLElBQUkySyw2QkFBNkI7WUFDdENwQixTQUFTSCxJQUFJLENBQUM7WUFDZEcsU0FBU0gsSUFBSSxDQUFDO1lBQ2RHLFNBQVNILElBQUksQ0FBQztRQUNoQixPQUFPO1lBQ0xHLFNBQVNILElBQUksQ0FBQztRQUNoQjtRQUVBLE9BQU9HLFNBQVNlLElBQUksQ0FBQztJQUN2QjtJQUVBLHlCQUF5QjtJQUN6QixNQUFNbUUscUNBQXFDLE9BQU9uRjtRQUNoRCxNQUFNb0Ysb0JBQW9CLHVCQUVyQnBGLE9BREFBLEtBQUtyRCxZQUFZLEVBQUMsYUFFbEJxRCxPQURBQSxLQUFLcEQsVUFBVSxFQUFDLGFBRWhCb0QsT0FEQUEsS0FBS00sU0FBUyxFQUFDLGFBRWZOLE9BREFBLEtBQUtTLE1BQU0sRUFBQyxhQUNFLE9BQWRULEtBQUs1QixRQUFRLEVBQUM7UUFJbkIsSUFBSTtZQUNGLFVBQVU7WUFDVixJQUFJaUg7WUFFSixNQUFNclMscUVBQVlBLENBQUNpSCxxQkFBcUIsQ0FBQztnQkFDdkM7b0JBQ0VDLE9BQU9qSCxxRUFBWUEsQ0FBQ2tILFNBQVM7b0JBQzdCQyxXQUFXO3dCQUNULE1BQU0sSUFBSUMsUUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBUzt3QkFDakQsT0FBTzs0QkFBRUUsTUFBTTt3QkFBVztvQkFDNUI7Z0JBQ0Y7Z0JBQ0E7b0JBQ0VOLE9BQU9qSCxxRUFBWUEsQ0FBQ3lILG1CQUFtQjtvQkFDdkNOLFdBQVc7d0JBQ1RpTCxjQUFjLE1BQU14UyxxREFBZUEsQ0FBQzhILFdBQVcsQ0FBQzs0QkFDOUNDLFNBQVN3Szs0QkFDVHJLLG1CQUFtQjs0QkFDbkJFLFNBQVM7Z0NBQ1B4QixjQUFjO29DQUNadEQsRUFBRSxFQUFFbEIsaUJBQUFBLDJCQUFBQSxLQUFNa0IsRUFBRTtvQ0FDWnVELGNBQWN6RSxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU15RSxZQUFZLEtBQUk7b0NBQ3BDSCxNQUFNdEUsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNc0UsSUFBSSxLQUFJO2dDQUN0QjtnQ0FDQXJGLGNBQWM4TDs0QkFDaEI7d0JBQ0Y7d0JBQ0EsT0FBT3FGO29CQUNUO2dCQUNGO2FBQ0QsRUFDRCxDQUFDbkwsT0FBTzlFO2dCQUNOLElBQUksQ0FBQ0YsV0FBVztvQkFDZEcsYUFBYTZFO2dCQUNmLE9BQU87b0JBQ0w1RSxZQUFZNEUsT0FBTzlFO2dCQUNyQjtZQUNGLEdBQ0E7Z0JBQ0UrRixZQUFZO2dCQUNaQyxZQUFZO2dCQUNaQyxxQkFBcUI7Z0JBQ3JCQyxXQUFXO1lBQ2I7WUFFQSx5Q0FBeUM7WUFFekMsTUFBTWdLLGVBQXdCO2dCQUM1Qm5QLElBQUlLLEtBQUtxQixHQUFHLEdBQUdFLFFBQVE7Z0JBQ3ZCM0IsTUFBTTtnQkFDTkMsU0FBU2dQLFlBQVl6SyxPQUFPO2dCQUM1QnJFLFdBQVcsSUFBSUM7Z0JBQ2ZDLFFBQVE7Z0JBQ1JDLFlBQVkyTyxZQUFZM08sVUFBVTtnQkFDbENrRixTQUFTeUosWUFBWXpKLE9BQU87Z0JBQzVCakYsYUFBYTBPLFlBQVkxTyxXQUFXO1lBQ3RDO1lBRUFoRCxZQUFZLENBQUN5RixPQUFTO3VCQUFJQTtvQkFBTWtNO2lCQUFhO1lBQzdDL1A7WUFDQW5DLDBDQUFLQSxDQUFDaUssT0FBTyxDQUFDO1FBRWhCLEVBQUUsT0FBT3ZCLEtBQUs7WUFDWjlDLFFBQVFsRixLQUFLLENBQUMsV0FBV2dJO1lBQ3pCdEc7UUFDQSxzQkFBc0I7UUFDeEI7SUFDRjtJQUVBLDRCQUE0QjtJQUM1QixNQUFNK1AseUJBQXlCO1FBQzdCLE1BQU1DLGlCQUFpQjtZQUNyQjtnQkFBRUMsS0FBSztnQkFBZUMsT0FBTztnQkFBUUMsVUFBVTtZQUFhO1lBQzVEO2dCQUFFRixLQUFLO2dCQUFrQkMsT0FBTztnQkFBUUMsVUFBVTtZQUFpQjtZQUNuRTtnQkFBRUYsS0FBSztnQkFBZ0JDLE9BQU87Z0JBQVFDLFVBQVU7WUFBYTtZQUM3RDtnQkFBRUYsS0FBSztnQkFBY0MsT0FBTztnQkFBUUMsVUFBVTtZQUFjO1lBQzVEO2dCQUFFRixLQUFLO2dCQUFVQyxPQUFPO2dCQUFRQyxVQUFVO1lBQWU7U0FDMUQ7UUFFRCxJQUFJLENBQUN6UixjQUFjLE9BQU9zUjtRQUUxQixPQUFPQSxlQUFlSSxNQUFNLENBQUNDLENBQUFBLFFBQVMsQ0FBQzNSLFlBQVksQ0FBQzJSLE1BQU1KLEdBQUcsQ0FBMEI7SUFDekY7SUFFQSxzQkFBc0I7SUFDdEIsTUFBTUssc0JBQXNCO1FBQzFCLGVBQWU7UUFDZixNQUFNcE4sY0FBY2hGLFFBQVEsQ0FBQ0EsU0FBU21ELE1BQU0sR0FBRyxFQUFFO1FBQ2pELElBQUk2QixDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWF0QyxJQUFJLE1BQUssUUFBUXNDLFlBQVkvQixXQUFXLElBQUkrQixZQUFZL0IsV0FBVyxDQUFDRSxNQUFNLEdBQUcsR0FBRztZQUMvRixPQUFPO2dCQUNMVCxNQUFNO2dCQUNOOEIsT0FBTztnQkFDUDZOLE1BQU07Z0JBQ05DLE9BQU90TixZQUFZL0IsV0FBVyxDQUFDa0MsS0FBSyxDQUFDLEdBQUcsR0FBR0QsR0FBRyxDQUFDcU4sQ0FBQUEsYUFBZTt3QkFDNURDLE1BQU1EO3dCQUNORixNQUFNO29CQUNSO1lBQ0Y7UUFDRjtRQUVBLGlCQUFpQjtRQUNqQixNQUFNSSxjQUFjWjtRQUNwQixJQUFJWSxZQUFZdFAsTUFBTSxHQUFHLEtBQU0sRUFBQzNDLGdCQUFnQkEsYUFBYWtTLFlBQVksR0FBRyxFQUFDLEdBQUk7WUFDL0UsT0FBTztnQkFDTGhRLE1BQU07Z0JBQ044QixPQUFPO2dCQUNQNk4sTUFBTTtnQkFDTk0sVUFBVSxTQUE0QixPQUFuQkYsWUFBWXRQLE1BQU0sRUFBQztnQkFDdENtUCxPQUFPRyxZQUFZdE4sS0FBSyxDQUFDLEdBQUcsR0FBR0QsR0FBRyxDQUFDME4sQ0FBQUEsTUFBUTt3QkFDekNKLE1BQU1JLElBQUlYLFFBQVE7d0JBQ2xCSSxNQUFNO3dCQUNOUSxRQUFRO3dCQUNSVixPQUFPUyxJQUFJYixHQUFHO29CQUNoQjtZQUNGO1FBQ0Y7UUFFQSxxQkFBcUI7UUFDckIsSUFBSSxDQUFDdlIsZ0JBQWdCQSxhQUFha1MsWUFBWSxHQUFHLElBQUk7WUFDbkQsT0FBTztnQkFDTGhRLE1BQU07Z0JBQ044QixPQUFPO2dCQUNQNk4sTUFBTTtnQkFDTkMsT0FBTztvQkFDTDt3QkFBRUUsTUFBTTt3QkFBaUJILE1BQU07b0JBQUs7b0JBQ3BDO3dCQUFFRyxNQUFNO3dCQUFXSCxNQUFNO29CQUFJO29CQUM3Qjt3QkFBRUcsTUFBTTt3QkFBVUgsTUFBTTtvQkFBSztvQkFDN0I7d0JBQUVHLE1BQU07d0JBQVVILE1BQU07b0JBQUs7aUJBQzlCO1lBQ0g7UUFDRixPQUFPLElBQUk3UixhQUFha1MsWUFBWSxHQUFHLElBQUk7WUFDekMsT0FBTztnQkFDTGhRLE1BQU07Z0JBQ044QixPQUFPO2dCQUNQNk4sTUFBTTtnQkFDTkMsT0FBTztvQkFDTDt3QkFBRUUsTUFBTTt3QkFBWUgsTUFBTTtvQkFBSztvQkFDL0I7d0JBQUVHLE1BQU07d0JBQWFILE1BQU07b0JBQUs7b0JBQ2hDO3dCQUFFRyxNQUFNO3dCQUFVSCxNQUFNO29CQUFLO29CQUM3Qjt3QkFBRUcsTUFBTTt3QkFBVUgsTUFBTTtvQkFBSztpQkFDOUI7WUFDSDtRQUNGLE9BQU87WUFDTCxPQUFPO2dCQUNMM1AsTUFBTTtnQkFDTjhCLE9BQU87Z0JBQ1A2TixNQUFNO2dCQUNOQyxPQUFPO29CQUNMO3dCQUFFRSxNQUFNO3dCQUFZSCxNQUFNO29CQUFLO29CQUMvQjt3QkFBRUcsTUFBTTt3QkFBVUgsTUFBTTtvQkFBSztvQkFDN0I7d0JBQUVHLE1BQU07d0JBQVVILE1BQU07b0JBQUs7b0JBQzdCO3dCQUFFRyxNQUFNO3dCQUFVSCxNQUFNO29CQUFRO2lCQUNqQztZQUNIO1FBQ0Y7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNUyx3QkFBd0IsQ0FBQ0M7UUFDN0IsSUFBSUEsS0FBS0YsTUFBTSxLQUFLLG9CQUFvQjtZQUN0QywyQkFBMkI7WUFDM0JsUyx1QkFBdUI7WUFDdkIsaUJBQWlCO1lBQ2pCakIsMENBQUtBLENBQUNzSSxJQUFJLENBQUMsWUFBc0IsT0FBVitLLEtBQUtQLElBQUk7UUFDbEMsT0FBTztZQUNMLGVBQWU7WUFDZnJTLGNBQWM0UyxLQUFLUCxJQUFJO1FBQ3pCO0lBQ0Y7SUFFQSxTQUFTO0lBQ1QsTUFBTVEseUJBQXlCLENBQUN6TztRQUM5QnRFLFlBQVlzRSxRQUFRdkUsUUFBUTtRQUM1QmlCLG9CQUFvQnNELFFBQVE5QixFQUFFO1FBQzlCMUIsZUFBZTtRQUNmckIsMENBQUtBLENBQUNpSyxPQUFPLENBQUMsVUFBd0IsT0FBZHBGLFFBQVFDLEtBQUs7SUFDdkM7SUFFQSxTQUFTO0lBQ1QsTUFBTXlPLGdDQUFnQztRQUNwQyxpQ0FBaUM7UUFDakNsUyxlQUFlO0lBQ2pCO0lBRUEsUUFBUTtJQUNSLE1BQU1tUyx3QkFBd0I7UUFDNUJqVCxZQUFZLEVBQUU7UUFDZGdCLG9CQUFvQjtRQUNwQlIsZ0JBQWdCO1FBQ2hCSixTQUFTO1FBQ1RVLGVBQWU7UUFDZkosdUJBQXVCLE9BQU8sV0FBVzs7UUFDekNRLGdCQUFnQnVFLENBQUFBLE9BQVFBLE9BQU8sR0FBRyxhQUFhOztRQUMvQ2hHLDBDQUFLQSxDQUFDaUssT0FBTyxDQUFDO0lBQ2hCO0lBRUEscUJBQ0UsOERBQUN3SjtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQUlDLFdBQVcsb0RBRWYsT0FEQzFTLHNCQUFzQixXQUFXOztrQ0FHakMsOERBQUN5Uzt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQ3hVLHFNQUFLQTtvREFBQ3dVLFdBQVU7Ozs7Ozs7Ozs7OzBEQUVuQiw4REFBQ0Q7O2tFQUNDLDhEQUFDRTt3REFBR0QsV0FBVTtrRUFBK0I7Ozs7OztrRUFDN0MsOERBQUNFO3dEQUFFRixXQUFVO2tFQUF5Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7O29DQUd6QzVTLGdCQUFnQkEsYUFBYWtTLFlBQVksR0FBRyxtQkFDM0MsOERBQUN4VSx1REFBS0E7d0NBQUNxVixTQUFRO3dDQUFZSCxXQUFVOzs0Q0FBNEI7NENBQ3hENVMsYUFBYWtTLFlBQVk7NENBQUM7Ozs7Ozs7Ozs7Ozs7MENBS3ZDLDhEQUFDUztnQ0FBSUMsV0FBVTs7a0RBRWIsOERBQUNwVix5REFBTUE7d0NBQ0x1VixTQUFRO3dDQUNSQyxNQUFLO3dDQUNMQyxTQUFTLElBQU0xUyxlQUFlLENBQUNEO3dDQUMvQnNTLFdBQVU7OzBEQUVWLDhEQUFDdFUscU1BQU9BO2dEQUFDc1UsV0FBVTs7Ozs7OzRDQUFpQjs7Ozs7OztrREFLdEMsOERBQUNwVix5REFBTUE7d0NBQ0x1VixTQUFRO3dDQUNSQyxNQUFLO3dDQUNMQyxTQUFTUDt3Q0FDVEUsV0FBVTs7MERBRVYsOERBQUNyVSxxTUFBU0E7Z0RBQUNxVSxXQUFVOzs7Ozs7NENBQWlCOzs7Ozs7O29DQUl2QyxDQUFDeFMsMEJBQ0EsOERBQUM1Qyx5REFBTUE7d0NBQ0x1VixTQUFRO3dDQUNSQyxNQUFLO3dDQUNMQyxTQUFTLElBQU05Uyx1QkFBdUIsQ0FBQ0Q7d0NBQ3ZDMFMsV0FBVTtrREFFVDFTLG9DQUNDOzs4REFDRSw4REFBQ2pDLHFNQUFXQTtvREFBQzJVLFdBQVU7Ozs7OztnREFBaUI7O3lFQUkxQzs7Z0RBQUU7OERBRUEsOERBQUMxVSxxTUFBWUE7b0RBQUMwVSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQVNuQ2hULHVCQUNDLDhEQUFDK1M7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNwVSx1REFBS0E7NEJBQUN1VSxTQUFROzs4Q0FDYiw4REFBQ2pWLHFNQUFXQTtvQ0FBQzhVLFdBQVU7Ozs7Ozs4Q0FDdkIsOERBQUNuVSxrRUFBZ0JBOzhDQUFFbUI7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU16Qiw4REFBQytTO3dCQUFJQyxXQUFVO2tDQUViLDRFQUFDRDs0QkFBSUMsV0FBVTs7Z0NBQ1pwVCxTQUFTa0YsR0FBRyxDQUFDLENBQUNnQzt3Q0EwRUozRjt5REF6RVQsOERBQUM0Ujt3Q0FBcUJDLFdBQVcsUUFBa0UsT0FBMURsTSxRQUFReEUsSUFBSSxLQUFLLFNBQVMsZ0JBQWdCOzs0Q0FDaEZ3RSxRQUFReEUsSUFBSSxLQUFLLHNCQUNoQiw4REFBQ3lRO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDelUscU1BQVFBO29EQUFDeVUsV0FBVTs7Ozs7Ozs7Ozs7MERBSXhCLDhEQUFDRDtnREFBSUMsV0FBVyxHQUlmLE9BSENsTSxRQUFReEUsSUFBSSxLQUFLLFNBQ2IsbUJBQ0EsaUJBQWtCLDhCQUE4Qjs7O2tFQUVwRCw4REFBQ3lRO3dEQUNDQyxXQUFXLEdBS1RsTSxPQUpBQSxRQUFReEUsSUFBSSxLQUFLLFNBQ2Isb0NBQ0Esa0NBQW1DLCtCQUErQjswREFDdkUsS0FNQSxPQUxDd0UsUUFBUXhFLElBQUksS0FBSyxTQUNid0UsUUFBUW5FLE1BQU0sS0FBSyxVQUNqQiwwQkFDQSw0REFDRixrREFBbUQsMEJBQTBCOzs7NERBR2xGbUUsUUFBUXhFLElBQUksS0FBSyxxQkFDaEIsOERBQUM5QyxpRUFBZ0JBO2dFQUFDK0MsU0FBU3VFLFFBQVF2RSxPQUFPO2dFQUFFeVEsV0FBVTs7Ozs7cUZBRXRELDhEQUFDRDtnRUFBSUMsV0FBVTswRUFBK0NsTSxRQUFRdkUsT0FBTzs7Ozs7OzREQUk5RXVFLFFBQVF4RSxJQUFJLEtBQUssc0JBQ2hCLDhEQUFDeVE7Z0VBQUlDLFdBQVU7O29FQUNabE0sUUFBUWxFLFVBQVUsS0FBS29FLDJCQUN0Qiw4REFBQytMO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQ3ZVLHFNQUFHQTtnRkFBQ3VVLFdBQVU7Ozs7OzswRkFDZiw4REFBQ007Z0ZBQUtOLFdBQVU7O29GQUF5QjtvRkFDakNsUCxLQUFLNEosS0FBSyxDQUFDNUcsUUFBUWxFLFVBQVUsR0FBRztvRkFBSzs7Ozs7Ozs7Ozs7OztvRUFJaERrRSxRQUFRZ0IsT0FBTyxJQUFJaEIsUUFBUWdCLE9BQU8sQ0FBQy9FLE1BQU0sR0FBRyxtQkFDM0MsOERBQUNnUTt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUM3VSxxTUFBYUE7Z0ZBQUM2VSxXQUFVOzs7Ozs7MEZBQ3pCLDhEQUFDTTtnRkFBS04sV0FBVTs7b0ZBQXlCO29GQUNsQ2xNLFFBQVFnQixPQUFPLENBQUMvQyxLQUFLLENBQUMsR0FBRyxHQUFHbUksSUFBSSxDQUFDO29GQUNyQ3BHLFFBQVFnQixPQUFPLENBQUMvRSxNQUFNLEdBQUcsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFRM0MsOERBQUNnUTt3REFBSUMsV0FBVyxrQ0FFZixPQURDbE0sUUFBUXhFLElBQUksS0FBSyxTQUFTLCtCQUErQjs7MEVBRXpELDhEQUFDZ1I7MEVBQU14TSxRQUFRckUsU0FBUyxDQUFDOFEsa0JBQWtCOzs7Ozs7NERBQzFDek0sUUFBUXhFLElBQUksS0FBSyx3QkFDaEIsOERBQUNnUjtnRUFBS04sV0FBVTs7b0VBQ2JsTSxRQUFRbkUsTUFBTSxLQUFLLGFBQWE7b0VBQ2hDbUUsUUFBUW5FLE1BQU0sS0FBSyxVQUFVO29FQUM3Qm1FLFFBQVFuRSxNQUFNLEtBQUssVUFBVTtvRUFDN0JtRSxRQUFRbkUsTUFBTSxLQUFLLFdBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7NENBTXRDbUUsUUFBUXhFLElBQUksS0FBSyx3QkFDaEIsOERBQUN5UTtnREFBSUMsV0FBVTswREFDYiw0RUFBQ007b0RBQUtOLFdBQVU7OERBQ2I3UixDQUFBQSxpQkFBQUEsNEJBQUFBLGFBQUFBLEtBQU1xQixJQUFJLGNBQVZyQixpQ0FBQUEsV0FBWXFTLE1BQU0sQ0FBQyxPQUFNOzs7Ozs7Ozs7Ozs7dUNBekV4QjFNLFFBQVF6RSxFQUFFOzs7Ozs7Z0NBZ0ZyQmpCLDJCQUNDLDhEQUFDMlI7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ3pVLHFNQUFRQTtnREFBQ3lVLFdBQVU7Ozs7Ozs7Ozs7O3NEQUV0Qiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUM1VCxpRkFBd0JBO2dEQUN2QmdDLFdBQVdBO2dEQUNYZ0YsT0FBTy9FO2dEQUNQQyxVQUFVQTtnREFDVm1TLGVBQWU7Z0RBQ2ZDLFVBQVVoUztnREFDVitGLFNBQVM7b0RBQ1AvRjtvREFDQXlEO2dEQUNGO2dEQUNBd08sZ0JBQWdCO2dEQUNoQkMsYUFBYTs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBS3JCLDhEQUFDYjtvQ0FBSWMsS0FBSzNTOzs7Ozs7Ozs7Ozs7Ozs7OztvQkFLWjt3QkFDQSxNQUFNMkIsY0FBY21QO3dCQUNwQixxQkFDRSw4REFBQ2U7NEJBQUlDLFdBQVcsK0NBTWYsT0FMQ25RLFlBQVlQLElBQUksS0FBSyxtQkFDakIsK0NBQ0FPLFlBQVlQLElBQUksS0FBSywwQkFDckIsZ0RBQ0E7c0NBRUosNEVBQUN5UTtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzs0Q0FDWm5RLFlBQVlQLElBQUksS0FBSyxpQ0FDcEIsOERBQUMvRCxxTUFBUUE7Z0RBQUN5VSxXQUFVOzs7OztxRUFFcEIsOERBQUNNO2dEQUFLTixXQUFVOzBEQUFXblEsWUFBWW9QLElBQUk7Ozs7OzswREFFN0MsOERBQUNxQjtnREFBS04sV0FBVyx1QkFNaEIsT0FMQ25RLFlBQVlQLElBQUksS0FBSyxtQkFDakIsa0JBQ0FPLFlBQVlQLElBQUksS0FBSywwQkFDckIsbUJBQ0E7MERBRUhPLFlBQVl1QixLQUFLOzs7Ozs7Ozs7Ozs7b0NBS3JCdkIsWUFBWTBQLFFBQVEsa0JBQ25CLDhEQUFDUTt3Q0FBSUMsV0FBVTtrREFDWm5RLFlBQVkwUCxRQUFROzs7Ozs7a0RBSXpCLDhEQUFDUTt3Q0FBSUMsV0FBVTtrREFDWm5RLFlBQVlxUCxLQUFLLENBQUNwTixHQUFHLENBQUMsQ0FBQzZOLE1BQU1sRSxzQkFDNUIsOERBQUM3USx5REFBTUE7Z0RBRUx1VixTQUFRO2dEQUNSQyxNQUFLO2dEQUNMQyxTQUFTLElBQU1YLHNCQUFzQkM7Z0RBQ3JDSyxXQUFXLFdBTVYsT0FMQ25RLFlBQVlQLElBQUksS0FBSyxtQkFDakIsZ0ZBQ0FPLFlBQVlQLElBQUksS0FBSywwQkFDckIsb0ZBQ0E7O2tFQUdOLDhEQUFDZ1I7d0RBQUtOLFdBQVU7a0VBQVFMLEtBQUtWLElBQUk7Ozs7OztvREFDaENVLEtBQUtQLElBQUk7OytDQWJMM0Q7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQkFvQm5CO2tDQUdBLDhEQUFDc0U7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ3BWLHlEQUFNQTtvQ0FBQ3VWLFNBQVE7b0NBQVFDLE1BQUs7b0NBQU9KLFdBQVU7OENBQzVDLDRFQUFDaFYscU1BQVNBO3dDQUFDZ1YsV0FBVTs7Ozs7Ozs7Ozs7OENBRXZCLDhEQUFDcFYseURBQU1BO29DQUFDdVYsU0FBUTtvQ0FBUUMsTUFBSztvQ0FBT0osV0FBVTs4Q0FDNUMsNEVBQUMvVSxxTUFBR0E7d0NBQUMrVSxXQUFVOzs7Ozs7Ozs7Ozs4Q0FFakIsOERBQUNuVix1REFBS0E7b0NBQ0ppVyxPQUFPaFU7b0NBQ1BpVSxVQUFVLENBQUNDLElBQU1qVSxjQUFjaVUsRUFBRUMsTUFBTSxDQUFDSCxLQUFLO29DQUM3Q0ksYUFBWTtvQ0FDWkMsV0FBVyxDQUFDSCxJQUFNQSxFQUFFckMsR0FBRyxLQUFLLFdBQVcsQ0FBQ3FDLEVBQUVJLFFBQVEsSUFBSWpQO29DQUN0RDZOLFdBQVU7Ozs7Ozs4Q0FFWiw4REFBQ3BWLHlEQUFNQTtvQ0FDTHlWLFNBQVNsTztvQ0FDVGtQLFVBQVUsQ0FBQ3ZVLFdBQVc2RCxJQUFJLE1BQU12QztvQ0FDaEM0UixXQUFVOzhDQUVWLDRFQUFDalYscU1BQUlBO3dDQUFDaVYsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQU90QixFQUFDMVMsdUJBQXVCSSxXQUFVLG1CQUNsQyw4REFBQ3FTO2dCQUFJQyxXQUFXLHVGQUVmLE9BREN4UyxXQUFXLHdEQUF3RDs7a0NBR25FLDhEQUFDdVM7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNadFMsNEJBQ0M7OzhEQUNFLDhEQUFDaEMscU1BQU9BO29EQUFDc1UsV0FBVTs7Ozs7OzhEQUNuQiw4REFBQ3NCO29EQUFHdEIsV0FBVTs4REFBK0I7Ozs7Ozs7eUVBRy9DOzs4REFDRSw4REFBQzVVLHFNQUFhQTtvREFBQzRVLFdBQVU7Ozs7Ozs4REFDekIsOERBQUNzQjtvREFBR3RCLFdBQVU7OERBQStCOzs7Ozs7Ozs7Ozs7O2tEQUtuRCw4REFBQ0Q7d0NBQUlDLFdBQVU7OzRDQUVaLENBQUN0Uyw2QkFDQSw4REFBQzlDLHlEQUFNQTtnREFDTHVWLFNBQVE7Z0RBQ1JDLE1BQUs7Z0RBQ0xDLFNBQVMsSUFBTTFTLGVBQWU7Z0RBQzlCcVMsV0FBVTswREFFViw0RUFBQ3RVLHFNQUFPQTtvREFBQ3NVLFdBQVU7Ozs7Ozs7Ozs7OzRDQUd0QnRTLDZCQUNDLDhEQUFDOUMseURBQU1BO2dEQUNMdVYsU0FBUTtnREFDUkMsTUFBSztnREFDTEMsU0FBUyxJQUFNMVMsZUFBZTtnREFDOUJxUyxXQUFVOzBEQUVWLDRFQUFDNVUscU1BQWFBO29EQUFDNFUsV0FBVTs7Ozs7Ozs7Ozs7MERBSTdCLDhEQUFDcFYseURBQU1BO2dEQUNMdVYsU0FBUTtnREFDUkMsTUFBSztnREFDTEMsU0FBUztvREFDUDlTLHVCQUF1QjtvREFDdkJJLGVBQWU7Z0RBQ2pCO2dEQUNBcVMsV0FBVTtnREFDVjVPLE9BQU07MERBRU4sNEVBQUM5RixxTUFBWUE7b0RBQUMwVSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJOUIsOERBQUNFO2dDQUFFRixXQUFVOzBDQUNWdFMsY0FDRyxnQkFDQTs7Ozs7Ozs7Ozs7O2tDQU1SLDhEQUFDcVM7d0JBQUlDLFdBQVU7a0NBQ1p0Uyw0QkFDQyw4REFBQ3FTOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDdlQsdUVBQW1CQTtnQ0FDbEI4VSxvQkFBb0IzQjtnQ0FDcEI0QixpQkFBaUI1VTtnQ0FDakI2VSwyQkFBMkI1Qjs7Ozs7Ozs7OztpREFJL0IsOERBQUNFOzRCQUFJQyxXQUFVOzs4Q0FFYiw4REFBQ3RULGtFQUFzQkE7b0NBQ3JCZ1YsU0FBUzFUO29DQUNUMlQsVUFBVTFUO29DQUNWK1IsV0FBVTs7Ozs7OzhDQUlaLDhEQUFDelQsZ0dBQStCQTtvQ0FDOUJxVixzQkFBc0IzTTtvQ0FDdEI0TSxzQkFBc0IxTTtvQ0FDdEIyTSxXQUFXO29DQUNYaFUsY0FBY0E7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBU3pCTixZQUFZLENBQUNGLHFDQUNaLDhEQUFDeVM7Z0JBQ0NDLFdBQVU7Z0JBQ1ZLLFNBQVMsSUFBTTlTLHVCQUF1Qjs7Ozs7O1lBS3pDQyxZQUFZRixxQ0FDWCw4REFBQzFDLHlEQUFNQTtnQkFDTG9WLFdBQVU7Z0JBQ1ZLLFNBQVMsSUFBTTlTLHVCQUF1QjswQkFFdEMsNEVBQUNuQyxxTUFBYUE7b0JBQUM0VSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztBQUtuQztHQXBqRGdCclQ7O1FBYUdiLDJEQUFPQTtRQVdwQk8sd0VBQWVBOzs7S0F4QkxNIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGNjYWlcXERlc2t0b3BcXERldlxcQ2VsbEZvcmdlIEFJXFxmcm9udGVuZFxcY29tcG9uZW50c1xcY29udmVyc2F0aW9uLWludGVyZmFjZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZVJlZiwgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYnV0dG9uXCJcbmltcG9ydCB7IElucHV0IH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9pbnB1dFwiXG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYmFkZ2VcIlxuaW1wb3J0IHsgU2VuZCwgUGFwZXJjbGlwLCBNaWMsIEFsZXJ0Q2lyY2xlLCBNZXNzYWdlU3F1YXJlLCBDbGlwYm9hcmRMaXN0LCBDaGV2cm9uTGVmdCwgQ2hldnJvblJpZ2h0LCBTcGFya2xlcywgQnJhaW4sIFphcCwgSGlzdG9yeSwgUm90YXRlQ2N3IH0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiXG5pbXBvcnQgeyBBbGVydCwgQWxlcnREZXNjcmlwdGlvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYWxlcnRcIlxuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gXCJAL2NvbnRleHRzL2F1dGgtY29udGV4dFwiXG5pbXBvcnQgeyBjb252ZXJzYXRpb25BcGksIGxpdGVyYXR1cmVBcGksIHNvbHV0aW9uQXBpLCBpbnRlbGxpZ2VudFJlY29tbWVuZGF0aW9uQXBpIH0gZnJvbSBcIkAvbGliL2FwaVwiXG5pbXBvcnQgeyBnZW5lcmF0ZUludGVsbGlnZW50U2VhcmNoU3VnZ2VzdGlvbnMgfSBmcm9tIFwiQC9saWIvc2VhcmNoLWxpbmtzXCJcbmltcG9ydCB7IGludGVsbGlnZW50U29sdXRpb25HZW5lcmF0b3IgfSBmcm9tIFwiQC9saWIvaW50ZWxsaWdlbnQtc29sdXRpb24tZ2VuZXJhdG9yXCJcbmltcG9ydCB7IGVycm9ySGFuZGxlciwgTG9hZGluZ1N0YWdlIH0gZnJvbSBcIkAvbGliL2VuaGFuY2VkLWVycm9yLWhhbmRsZXJcIlxuaW1wb3J0IHsgRW5oYW5jZWRMb2FkaW5nSW5kaWNhdG9yLCB1c2VTdGFnZUxvYWRpbmcgfSBmcm9tIFwiLi9lbmhhbmNlZC1sb2FkaW5nLWluZGljYXRvclwiXG5pbXBvcnQgeyB0b2FzdCB9IGZyb20gXCJzb25uZXJcIlxuaW1wb3J0IHsgUHJvZ3Jlc3NpdmVSZXF1aXJlbWVudENvbGxlY3RvciB9IGZyb20gXCIuL3Byb2dyZXNzaXZlLXJlcXVpcmVtZW50LWNvbGxlY3RvclwiXG5pbXBvcnQgeyBGb3JtYXR0ZWRNZXNzYWdlIH0gZnJvbSBcIi4vZm9ybWF0dGVkLW1lc3NhZ2VcIlxuaW1wb3J0IHsgQ29udmVyc2F0aW9uSGlzdG9yeSB9IGZyb20gXCIuL2NvbnZlcnNhdGlvbi1oaXN0b3J5XCJcbmltcG9ydCBMaXRlcmF0dXJlU2VhcmNoVG9nZ2xlIGZyb20gXCIuL2xpdGVyYXR1cmUtc2VhcmNoLXRvZ2dsZVwiXG5cbmludGVyZmFjZSBNZXNzYWdlIHtcbiAgaWQ6IHN0cmluZ1xuICB0eXBlOiBcInVzZXJcIiB8IFwiYWlcIlxuICBjb250ZW50OiBzdHJpbmdcbiAgdGltZXN0YW1wOiBEYXRlXG4gIHN0YXR1czogXCJzZW5kaW5nXCIgfCBcInNlbnRcIiB8IFwicmVhZFwiIHwgXCJlcnJvclwiXG4gIGNvbmZpZGVuY2U/OiBudW1iZXJcbiAgc291cmNlcz86IHN0cmluZ1tdXG4gIHN1Z2dlc3Rpb25zPzogc3RyaW5nW11cbn1cblxuaW50ZXJmYWNlIFJlcXVpcmVtZW50RGF0YSB7XG4gIC8vIOesrDHmraXvvJrln7rnoYDliIbnsbvkv6Hmga9cbiAgc3BlY2llc1R5cGU6IHN0cmluZyAgICAgICAgICAvLyDniannp43nsbvlnosgLSDmnIDph43opoHnmoTln7rnoYDkv6Hmga9cbiAgZXhwZXJpbWVudFR5cGU6IHN0cmluZyAgICAgICAvLyDlrp7pqoznsbvlnosgLSDlhrPlrprmioDmnK/ot6/nur9cbiAgcmVzZWFyY2hHb2FsOiBzdHJpbmcgICAgICAgIC8vIOeglOeptuebruagh1xuXG4gIC8vIOesrDLmraXvvJrmoLfmnKzor6bnu4bkv6Hmga9cbiAgc2FtcGxlVHlwZTogc3RyaW5nICAgICAgICAgIC8vIOagt+acrOexu+Wei++8iOagueaNrueJqeenjeWKqOaAgeiwg+aVtO+8iVxuICBzYW1wbGVDb3VudDogc3RyaW5nICAgICAgICAgLy8g5qC35pys5pWw55uuXG4gIHNhbXBsZUNvbmRpdGlvbjogc3RyaW5nICAgICAvLyDmoLfmnKznirbmgIHvvIjmlrDpspwv5Ya35Ya7L+WbuuWumuetie+8iVxuICBzYW1wbGVQcm9jZXNzaW5nOiBzdHJpbmcgICAgLy8g5qC35pys5aSE55CG5pa55byPXG4gIGNlbGxDb3VudDogc3RyaW5nICAgICAgICAgICAvLyDpooTmnJ/nu4bog57mlbDph49cbiAgY2VsbFZpYWJpbGl0eTogc3RyaW5nICAgICAgIC8vIOmihOacn+e7huiDnua0u+WKm1xuXG4gIC8vIOesrDPmraXvvJrpobnnm67op4TliJJcbiAgYnVkZ2V0OiBzdHJpbmcgICAgICAgICAgICAgIC8vIOmihOeul+iMg+WbtFxuICB0aW1lbGluZTogc3RyaW5nICAgICAgICAgICAgLy8g6aG555uu5ZGo5pyfXG4gIHVyZ2VuY3lMZXZlbDogc3RyaW5nICAgICAgICAvLyDntKfmgKXnqIvluqZcblxuICAvLyDnrKw05q2l77ya5oqA5pyv57uG6IqC5ZKM6auY57qn6YCJ6aG5XG4gIHNlcXVlbmNpbmdEZXB0aDogc3RyaW5nICAgICAvLyDmtYvluo/mt7HluqZcbiAgYW5hbHlzaXNUeXBlOiBzdHJpbmcgICAgICAgIC8vIOWIhuaekOexu+Wei1xuICBkYXRhQW5hbHlzaXNOZWVkczogc3RyaW5nICAgLy8g5pWw5o2u5YiG5p6Q6ZyA5rGCXG4gIHNwZWNpYWxSZXF1aXJlbWVudHM6IHN0cmluZyAvLyDnibnmroropoHmsYJcbiAgbmVlZHNDZWxsU29ydGluZzogc3RyaW5nICAgIC8vIOaYr+WQpumcgOimgee7huiDnuWIhumAiVxuXG4gIC8vIOiHquWKqOaOqOaWreS/oeaBr1xuICByZWNvbW1lbmRlZFBsYXRmb3JtOiBzdHJpbmdcbiAgZXN0aW1hdGVkQ29zdDogc3RyaW5nXG4gIHJpc2tGYWN0b3JzOiBzdHJpbmdbXVxuXG4gIC8vIOWujOaIkOW6plxuICBjb21wbGV0ZW5lc3M6IG51bWJlclxuICBjb2xsZWN0ZWRGaWVsZHM6IHN0cmluZ1tdXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBDb252ZXJzYXRpb25JbnRlcmZhY2UoKSB7XG4gIGNvbnN0IFttZXNzYWdlcywgc2V0TWVzc2FnZXNdID0gdXNlU3RhdGU8TWVzc2FnZVtdPihbXSlcbiAgY29uc3QgW2lucHV0VmFsdWUsIHNldElucHV0VmFsdWVdID0gdXNlU3RhdGUoXCJcIilcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmc+KFwiXCIpXG4gIGNvbnN0IFtjb252ZXJzYXRpb25JZCwgc2V0Q29udmVyc2F0aW9uSWRdID0gdXNlU3RhdGU8bnVtYmVyIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW3JlcXVpcmVtZW50cywgc2V0UmVxdWlyZW1lbnRzXSA9IHVzZVN0YXRlPFJlcXVpcmVtZW50RGF0YSB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtyaWdodFBhbmVsQ29sbGFwc2VkLCBzZXRSaWdodFBhbmVsQ29sbGFwc2VkXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbaXNNb2JpbGUsIHNldElzTW9iaWxlXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbc2hvd0hpc3RvcnksIHNldFNob3dIaXN0b3J5XSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbY3VycmVudFNlc3Npb25JZCwgc2V0Q3VycmVudFNlc3Npb25JZF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKVxuICBjb25zdCBbcmVzZXRUcmlnZ2VyLCBzZXRSZXNldFRyaWdnZXJdID0gdXNlU3RhdGUoMCkgLy8g55So5LqO6Kem5Y+R6ZyA5rGC5pS26ZuG5Yqp5omL6YeN572uXG4gIGNvbnN0IFtlbmFibGVMaXRlcmF0dXJlU2VhcmNoLCBzZXRFbmFibGVMaXRlcmF0dXJlU2VhcmNoXSA9IHVzZVN0YXRlKGZhbHNlKSAvLyDmlofnjK7mkJzntKLlvIDlhbNcbiAgY29uc3QgbWVzc2FnZXNFbmRSZWYgPSB1c2VSZWY8SFRNTERpdkVsZW1lbnQ+KG51bGwpXG4gIGNvbnN0IHsgdXNlciB9ID0gdXNlQXV0aCgpXG5cbiAgLy8g5L2/55So5aKe5by655qE5Yqg6L2954q25oCB566h55CGXG4gIGNvbnN0IHtcbiAgICBpc0xvYWRpbmcsXG4gICAgY3VycmVudFN0YWdlLFxuICAgIHByb2dyZXNzLFxuICAgIHN0YXJ0TG9hZGluZyxcbiAgICB1cGRhdGVTdGFnZSxcbiAgICBmaW5pc2hMb2FkaW5nLFxuICAgIGNhbmNlbExvYWRpbmdcbiAgfSA9IHVzZVN0YWdlTG9hZGluZygpXG5cbiAgLy8g5ZON5bqU5byP5qOA5rWLXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgY2hlY2tNb2JpbGUgPSAoKSA9PiB7XG4gICAgICBzZXRJc01vYmlsZSh3aW5kb3cuaW5uZXJXaWR0aCA8IDEwMjQpXG4gICAgICBpZiAod2luZG93LmlubmVyV2lkdGggPCAxMDI0KSB7XG4gICAgICAgIHNldFJpZ2h0UGFuZWxDb2xsYXBzZWQodHJ1ZSlcbiAgICAgIH1cbiAgICB9XG5cbiAgICBjaGVja01vYmlsZSgpXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIGNoZWNrTW9iaWxlKVxuICAgIHJldHVybiAoKSA9PiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigncmVzaXplJywgY2hlY2tNb2JpbGUpXG4gIH0sIFtdKVxuXG4gIC8vIOa7muWKqOWIsOW6lemDqFxuICBjb25zdCBzY3JvbGxUb0JvdHRvbSA9ICgpID0+IHtcbiAgICBtZXNzYWdlc0VuZFJlZi5jdXJyZW50Py5zY3JvbGxJbnRvVmlldyh7IGJlaGF2aW9yOiBcInNtb290aFwiIH0pXG4gIH1cblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHNjcm9sbFRvQm90dG9tKClcbiAgfSwgW21lc3NhZ2VzXSlcblxuICAvLyDliJ3lp4vljJbmrKLov47mtojmga9cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCB3ZWxjb21lTWVzc2FnZTogTWVzc2FnZSA9IHtcbiAgICAgIGlkOiBcIndlbGNvbWVcIixcbiAgICAgIHR5cGU6IFwiYWlcIixcbiAgICAgIGNvbnRlbnQ6IGDmgqjlpb0ke3VzZXI/Lm5hbWUgPyBg77yMJHt1c2VyLm5hbWV9YCA6ICcnfe+8gfCfkYtcblxu5oiR5pivICoqQ2VsbEZvcmdlIEFJKiog5pm66IO96aG+6Zeu77yM5LiT5rOo5LqO5Y2V57uG6IOe5rWL5bqP5oqA5pyv5ZKo6K+i44CCXG5cbvCflKwgKirmiJHnmoTkuJPplb8qKu+8mlxu4oCiIOWNlee7huiDnlJOQea1i+W6jyAoc2NSTkEtc2VxKVxu4oCiIOWNlee7huiDnkFUQUPmtYvluo8gKHNjQVRBQy1zZXEpXG7igKIg5aSa57uE5a2m5rWL5bqPIChNdWx0aW9tZSlcbuKAoiDnqbrpl7TovazlvZXnu4TlraZcbuKAoiDlrp7pqozorr7orqHkuI7mlbDmja7liIbmnpBcblxu8J+SoSAqKuW8gOWni+aWueW8jyoq77yaXG7mgqjlj6/ku6Xnm7TmjqXmj5Dpl67vvIzmiJHkvJrmmbrog73mlLbpm4bmgqjnmoTpnIDmsYLkv6Hmga/vvIzmiJbogIXkvb/nlKjlj7PkvqfnmoTpnIDmsYLmlLbpm4bliqnmiYvlv6vpgJ/loavlhpnpobnnm67kv6Hmga/jgIJcblxu6K+35ZGK6K+J5oiR5oKo55qE56CU56m255uu5qCH77yM5oiR5p2l5Li65oKo5Yi25a6a5pyA5L2z55qE5oqA5pyv5pa55qGI77yBYCxcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSxcbiAgICAgIHN0YXR1czogXCJyZWFkXCIsXG4gICAgICBjb25maWRlbmNlOiAxLjAsXG4gICAgICBzdWdnZXN0aW9uczogW1xuICAgICAgICBcIuaIkemcgOimgei/m+ihjOWNlee7huiDnlJOQea1i+W6j1wiLFxuICAgICAgICBcIuivt+aOqOiNkOmAguWQiOeahOaKgOacr+W5s+WPsFwiLFxuICAgICAgICBcIuW4ruaIkeWIhuaekOmhueebruaIkOacrFwiLFxuICAgICAgICBcIuafpeeci+aIkOWKn+ahiOS+i1wiXG4gICAgICBdXG4gICAgfVxuICAgIHNldE1lc3NhZ2VzKFt3ZWxjb21lTWVzc2FnZV0pXG4gIH0sIFt1c2VyXSlcblxuICAvLyDoh6rliqjkv53lrZjlr7nor53liLDljoblj7JcbiAgY29uc3QgYXV0b1NhdmVDb252ZXJzYXRpb24gPSAobWVzc2FnZXM6IE1lc3NhZ2VbXSkgPT4ge1xuICAgIGlmIChtZXNzYWdlcy5sZW5ndGggPj0gMikgeyAvLyDoh7PlsJHmnInkuIDova7lr7nor51cbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHN0b3JhZ2VLZXkgPSBgY2VsbGZvcmdlX2NvbnZlcnNhdGlvbnNfJHt1c2VyPy5pZCB8fCAnYW5vbnltb3VzJ31gXG4gICAgICAgIGNvbnN0IGV4aXN0aW5nU2Vzc2lvbnMgPSBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKHN0b3JhZ2VLZXkpIHx8ICdbXScpXG5cbiAgICAgICAgLy8g55Sf5oiQ5a+56K+d5qCH6aKYXG4gICAgICAgIGNvbnN0IGdlbmVyYXRlVGl0bGUgPSAobXNnczogTWVzc2FnZVtdKTogc3RyaW5nID0+IHtcbiAgICAgICAgICBjb25zdCBmaXJzdFVzZXJNZXNzYWdlID0gbXNncy5maW5kKG0gPT4gbS50eXBlID09PSBcInVzZXJcIilcbiAgICAgICAgICBpZiAoZmlyc3RVc2VyTWVzc2FnZSkge1xuICAgICAgICAgICAgY29uc3QgY29udGVudCA9IGZpcnN0VXNlck1lc3NhZ2UuY29udGVudC50cmltKClcbiAgICAgICAgICAgIHJldHVybiBjb250ZW50Lmxlbmd0aCA+IDMwID8gY29udGVudC5zdWJzdHJpbmcoMCwgMzApICsgXCIuLi5cIiA6IGNvbnRlbnRcbiAgICAgICAgICB9XG4gICAgICAgICAgcmV0dXJuIFwi5paw5a+56K+dXCJcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IHNlc3Npb25JZCA9IGN1cnJlbnRTZXNzaW9uSWQgfHwgYHNlc3Npb25fJHtEYXRlLm5vdygpfV8ke01hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cigyLCA5KX1gXG4gICAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKClcblxuICAgICAgICBjb25zdCBzZXNzaW9uID0ge1xuICAgICAgICAgIGlkOiBzZXNzaW9uSWQsXG4gICAgICAgICAgdGl0bGU6IGdlbmVyYXRlVGl0bGUobWVzc2FnZXMpLFxuICAgICAgICAgIG1lc3NhZ2VzLFxuICAgICAgICAgIGNyZWF0ZWRBdDogY3VycmVudFNlc3Npb25JZCA/IGV4aXN0aW5nU2Vzc2lvbnMuZmluZCgoczogYW55KSA9PiBzLmlkID09PSBzZXNzaW9uSWQpPy5jcmVhdGVkQXQgfHwgbm93IDogbm93LFxuICAgICAgICAgIHVwZGF0ZWRBdDogbm93LFxuICAgICAgICAgIGlzU3RhcnJlZDogZmFsc2UsXG4gICAgICAgICAgaXNBcmNoaXZlZDogZmFsc2UsXG4gICAgICAgICAgdGFnczogW10sXG4gICAgICAgICAgbWVzc2FnZUNvdW50OiBtZXNzYWdlcy5sZW5ndGgsXG4gICAgICAgICAgbGFzdE1lc3NhZ2U6IG1lc3NhZ2VzW21lc3NhZ2VzLmxlbmd0aCAtIDFdPy5jb250ZW50LnN1YnN0cmluZygwLCAxMDApIHx8IFwiXCJcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOabtOaWsOaIlua3u+WKoOS8muivnVxuICAgICAgICBjb25zdCB1cGRhdGVkU2Vzc2lvbnMgPSBjdXJyZW50U2Vzc2lvbklkXG4gICAgICAgICAgPyBleGlzdGluZ1Nlc3Npb25zLm1hcCgoczogYW55KSA9PiBzLmlkID09PSBzZXNzaW9uSWQgPyBzZXNzaW9uIDogcylcbiAgICAgICAgICA6IFtzZXNzaW9uLCAuLi5leGlzdGluZ1Nlc3Npb25zXS5zbGljZSgwLCA1MCkgLy8g5pyA5aSa5L+d5a2YNTDkuKrlr7nor51cblxuICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShzdG9yYWdlS2V5LCBKU09OLnN0cmluZ2lmeSh1cGRhdGVkU2Vzc2lvbnMpKVxuICAgICAgICBzZXRDdXJyZW50U2Vzc2lvbklkKHNlc3Npb25JZClcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iHquWKqOS/neWtmOWvueivneWksei0pTonLCBlcnJvcilcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvLyDlpITnkIbooajljZXmj5DkuqQgLSDkvb/nlKjlop7lvLrnmoTplJnor6/lpITnkIblkozliIbpmLbmrrXliqDovb1cbiAgY29uc3QgaGFuZGxlU2VuZE1lc3NhZ2UgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFpbnB1dFZhbHVlLnRyaW0oKSkgcmV0dXJuXG5cbiAgICBjb25zdCB1c2VyTWVzc2FnZSA9IGlucHV0VmFsdWUudHJpbSgpXG4gICAgc2V0SW5wdXRWYWx1ZShcIlwiKVxuICAgIHNldEVycm9yKFwiXCIpXG5cbiAgICAvLyDliJvlu7rnlKjmiLfmtojmga9cbiAgICBjb25zdCBuZXdVc2VyTWVzc2FnZTogTWVzc2FnZSA9IHtcbiAgICAgIGlkOiBEYXRlLm5vdygpLnRvU3RyaW5nKCksXG4gICAgICB0eXBlOiBcInVzZXJcIixcbiAgICAgIGNvbnRlbnQ6IHVzZXJNZXNzYWdlLFxuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLFxuICAgICAgc3RhdHVzOiBcInNlbmRpbmdcIixcbiAgICB9XG5cbiAgICBzZXRNZXNzYWdlcygocHJldikgPT4gWy4uLnByZXYsIG5ld1VzZXJNZXNzYWdlXSlcblxuICAgIC8vIOaehOW7uuWvueivneWOhuWPsuWSjOS4iuS4i+aWh++8iOWcqOWIhumYtuauteaTjeS9nOWklumDqOWHhuWkh++8iVxuICAgIGNvbnN0IGNvbnZlcnNhdGlvbkhpc3RvcnkgPSBtZXNzYWdlcy5tYXAobXNnID0+ICh7XG4gICAgICByb2xlOiBtc2cudHlwZSA9PT0gXCJ1c2VyXCIgPyBcInVzZXJcIiA6IFwiYXNzaXN0YW50XCIsXG4gICAgICBjb250ZW50OiBtc2cuY29udGVudFxuICAgIH0pKVxuXG4gICAgY29uc3QgZW5oYW5jZWRDb250ZXh0ID0ge1xuICAgICAgdXNlcl9wcm9maWxlOiB7XG4gICAgICAgIGlkOiB1c2VyPy5pZCxcbiAgICAgICAgb3JnYW5pemF0aW9uOiB1c2VyPy5vcmdhbml6YXRpb24gfHwgXCJcIixcbiAgICAgICAgcm9sZTogdXNlcj8ucm9sZSB8fCBcIlwiLFxuICAgICAgICBleHBlcnRpc2U6IHVzZXI/LmV4cGVydGlzZV9hcmVhcyB8fCBcIlwiLFxuICAgICAgICByZXNlYXJjaF9pbnRlcmVzdHM6IHVzZXI/LnJlc2VhcmNoX2ludGVyZXN0cyB8fCBcIlwiXG4gICAgICB9LFxuICAgICAgcmVxdWlyZW1lbnRzOiByZXF1aXJlbWVudHMsXG4gICAgICBjb252ZXJzYXRpb25faGlzdG9yeTogY29udmVyc2F0aW9uSGlzdG9yeSxcbiAgICAgIGVuYWJsZV9saXRlcmF0dXJlX3NlYXJjaDogZW5hYmxlTGl0ZXJhdHVyZVNlYXJjaFxuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICAvLyDkvb/nlKjliIbpmLbmrrXliqDovb3lpITnkIZcbiAgICAgIGxldCBhaUFwaVJlc3BvbnNlOiBhbnlcblxuICAgICAgYXdhaXQgZXJyb3JIYW5kbGVyLmhhbmRsZVN0YWdlZE9wZXJhdGlvbihbXG4gICAgICAgIHtcbiAgICAgICAgICBzdGFnZTogTG9hZGluZ1N0YWdlLkFOQUxZWklORyxcbiAgICAgICAgICBvcGVyYXRpb246IGFzeW5jICgpID0+IHtcbiAgICAgICAgICAgIC8vIOaooeaLn+WIhuaekOmYtuautVxuICAgICAgICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDgwMCkpXG4gICAgICAgICAgICByZXR1cm4geyBzdGVwOiBcImFuYWx5emVkXCIgfVxuICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIHN0YWdlOiBMb2FkaW5nU3RhZ2UuU0VBUkNISU5HX0xJVEVSQVRVUkUsXG4gICAgICAgICAgb3BlcmF0aW9uOiBhc3luYyAoKSA9PiB7XG4gICAgICAgICAgICAvLyDmqKHmi5/mlofnjK7mkJzntKJcbiAgICAgICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMjAwKSlcbiAgICAgICAgICAgIHJldHVybiB7IHN0ZXA6IFwibGl0ZXJhdHVyZV9zZWFyY2hlZFwiIH1cbiAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICBzdGFnZTogTG9hZGluZ1N0YWdlLkdFTkVSQVRJTkdfUkVTUE9OU0UsXG4gICAgICAgICAgb3BlcmF0aW9uOiBhc3luYyAoKSA9PiB7XG4gICAgICAgICAgICAvLyDlrp7pmYXosIPnlKhBSSBBUElcbiAgICAgICAgICAgIGFpQXBpUmVzcG9uc2UgPSBhd2FpdCBjb252ZXJzYXRpb25BcGkuc2VuZE1lc3NhZ2Uoe1xuICAgICAgICAgICAgICBtZXNzYWdlOiB1c2VyTWVzc2FnZSxcbiAgICAgICAgICAgICAgY29udmVyc2F0aW9uX2lkOiBjb252ZXJzYXRpb25JZCB8fCB1bmRlZmluZWQsXG4gICAgICAgICAgICAgIGNvbnZlcnNhdGlvbl90eXBlOiBcImVuaGFuY2VkXCIsXG4gICAgICAgICAgICAgIGhpc3Rvcnk6IGNvbnZlcnNhdGlvbkhpc3RvcnksXG4gICAgICAgICAgICAgIGNvbnRleHQ6IGVuaGFuY2VkQ29udGV4dFxuICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIHJldHVybiBhaUFwaVJlc3BvbnNlXG4gICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgc3RhZ2U6IExvYWRpbmdTdGFnZS5GT1JNQVRUSU5HLFxuICAgICAgICAgIG9wZXJhdGlvbjogYXN5bmMgKCkgPT4ge1xuICAgICAgICAgICAgLy8g5qih5ouf5qC85byP5YyW5aSE55CGXG4gICAgICAgICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgNTAwKSlcbiAgICAgICAgICAgIHJldHVybiB7IHN0ZXA6IFwiZm9ybWF0dGVkXCIgfVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgXSxcbiAgICAgIChzdGFnZSwgcHJvZ3Jlc3MpID0+IHtcbiAgICAgICAgaWYgKCFpc0xvYWRpbmcpIHtcbiAgICAgICAgICBzdGFydExvYWRpbmcoc3RhZ2UpXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgdXBkYXRlU3RhZ2Uoc3RhZ2UsIHByb2dyZXNzKVxuICAgICAgICB9XG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBtYXhSZXRyaWVzOiAxLCAvLyDlh4/lsJHph43or5XmrKHmlbDvvIzpgb/lhY3popHnuYHph43or5VcbiAgICAgICAgcmV0cnlEZWxheTogMzAwMCwgLy8g5aKe5Yqg6YeN6K+V5bu26L+f5YiwM+enklxuICAgICAgICB1c2VyRnJpZW5kbHlNZXNzYWdlOiBcIkFJ5pyN5Yqh5pqC5pe25LiN5Y+v55So77yM6K+356iN5ZCO6YeN6K+VXCIsXG4gICAgICAgIHNob3dUb2FzdDogZmFsc2UsIC8vIOS4jeaYvuekuumHjeivlXRvYXN077yM5YeP5bCR5omT5omwXG4gICAgICAgIG9uUmV0cnk6IChhdHRlbXB0LCBlcnJvcikgPT4ge1xuICAgICAgICAgIC8vIOWPquWcqOe9kee7nOmUmeivr+aXtuaJjeaYvuekuumHjeivleS/oeaBr1xuICAgICAgICAgIGlmIChlcnJvci5tZXNzYWdlPy5pbmNsdWRlcygnZmV0Y2gnKSB8fCBlcnJvci5tZXNzYWdlPy5pbmNsdWRlcygn572R57ucJykpIHtcbiAgICAgICAgICAgIHRvYXN0LmluZm8oYOajgOa1i+WIsOe9kee7nOmXrumimO+8jOato+WcqOmHjeivlS4uLmApXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9KVxuXG4gICAgICAvLyBhaUFwaVJlc3BvbnNl5bey57uP5ZyoR0VORVJBVElOR19SRVNQT05TRemYtuauteS4reiuvue9rlxuXG4gICAgICAvLyDmm7TmlrDnlKjmiLfmtojmga/nirbmgIHkuLrlt7Llj5HpgIFcbiAgICAgIHNldE1lc3NhZ2VzKChwcmV2KSA9PlxuICAgICAgICBwcmV2Lm1hcChtc2cgPT5cbiAgICAgICAgICBtc2cuaWQgPT09IG5ld1VzZXJNZXNzYWdlLmlkXG4gICAgICAgICAgICA/IHsgLi4ubXNnLCBzdGF0dXM6IFwic2VudFwiIGFzIGNvbnN0IH1cbiAgICAgICAgICAgIDogbXNnXG4gICAgICAgIClcbiAgICAgIClcblxuICAgICAgLy8g5Yib5bu6QUnlm57lpI3mtojmga9cbiAgICAgIGNvbnN0IGFpUmVzcG9uc2U6IE1lc3NhZ2UgPSB7XG4gICAgICAgIGlkOiAoRGF0ZS5ub3coKSArIDEpLnRvU3RyaW5nKCksXG4gICAgICAgIHR5cGU6IFwiYWlcIixcbiAgICAgICAgY29udGVudDogYWlBcGlSZXNwb25zZS5tZXNzYWdlLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksXG4gICAgICAgIHN0YXR1czogXCJyZWFkXCIsXG4gICAgICAgIGNvbmZpZGVuY2U6IGFpQXBpUmVzcG9uc2UuY29uZmlkZW5jZSxcbiAgICAgICAgc291cmNlczogYWlBcGlSZXNwb25zZS5zb3VyY2VzLFxuICAgICAgICBzdWdnZXN0aW9uczogYWlBcGlSZXNwb25zZS5zdWdnZXN0aW9uc1xuICAgICAgfVxuXG4gICAgICBzZXRNZXNzYWdlcygocHJldikgPT4ge1xuICAgICAgICBjb25zdCBuZXdNZXNzYWdlcyA9IFsuLi5wcmV2LCBhaVJlc3BvbnNlXVxuICAgICAgICAvLyDoh6rliqjkv53lrZjlr7nor51cbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiBhdXRvU2F2ZUNvbnZlcnNhdGlvbihuZXdNZXNzYWdlcyksIDEwMDApXG4gICAgICAgIHJldHVybiBuZXdNZXNzYWdlc1xuICAgICAgfSlcblxuICAgICAgZmluaXNoTG9hZGluZygpXG5cbiAgICB9IGNhdGNoIChlcnI6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcihcIuWPkemAgea2iOaBr+Wksei0pTpcIiwgZXJyKVxuXG4gICAgICAvLyDmm7TmlrDnlKjmiLfmtojmga/nirbmgIHkuLrplJnor69cbiAgICAgIHNldE1lc3NhZ2VzKChwcmV2KSA9PlxuICAgICAgICBwcmV2Lm1hcChtc2cgPT5cbiAgICAgICAgICBtc2cuaWQgPT09IG5ld1VzZXJNZXNzYWdlLmlkXG4gICAgICAgICAgICA/IHsgLi4ubXNnLCBzdGF0dXM6IFwiZXJyb3JcIiBhcyBjb25zdCB9XG4gICAgICAgICAgICA6IG1zZ1xuICAgICAgICApXG4gICAgICApXG5cbiAgICAgIC8vIOmUmeivr+W3sue7j+eUsWVycm9ySGFuZGxlcuWkhOeQhu+8jOi/memHjOWPqumcgOimgeabtOaWsFVJ54q25oCBXG4gICAgICBzZXRFcnJvcihcIuWPkemAgea2iOaBr+Wksei0pe+8jOivt+eojeWQjumHjeivlVwiKVxuICAgICAgY2FuY2VsTG9hZGluZygpXG4gICAgfVxuICB9XG5cbiAgLy8g5aSE55CG6ZyA5rGC5Y+Y5pu0XG4gIGNvbnN0IGhhbmRsZVJlcXVpcmVtZW50c0NoYW5nZSA9IChuZXdSZXF1aXJlbWVudHM6IFJlcXVpcmVtZW50RGF0YSkgPT4ge1xuICAgIHNldFJlcXVpcmVtZW50cyhuZXdSZXF1aXJlbWVudHMpXG4gIH1cblxuICAvLyDlpITnkIbpnIDmsYLmj5DkuqQgLSDkvb/nlKjmlrDnmoTmlrnmoYjnlJ/miJBBUElcbiAgY29uc3QgaGFuZGxlUmVxdWlyZW1lbnRzU3VibWl0ID0gYXN5bmMgKHJlcXVpcmVtZW50czogUmVxdWlyZW1lbnREYXRhKSA9PiB7XG4gICAgLy8g55Sf5oiQ6ZyA5rGC5oC757uT5raI5oGvXG4gICAgY29uc3QgcmVxdWlyZW1lbnRTdW1tYXJ5ID0gZ2VuZXJhdGVSZXF1aXJlbWVudFN1bW1hcnkocmVxdWlyZW1lbnRzKVxuXG4gICAgLy8g5re75Yqg55So5oi35raI5oGv5Yiw5a+56K+d5LitXG4gICAgY29uc3QgdXNlck1lc3NhZ2U6IE1lc3NhZ2UgPSB7XG4gICAgICBpZDogRGF0ZS5ub3coKS50b1N0cmluZygpLFxuICAgICAgdHlwZTogXCJ1c2VyXCIsXG4gICAgICBjb250ZW50OiByZXF1aXJlbWVudFN1bW1hcnksXG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksXG4gICAgICBzdGF0dXM6IFwic2VuZGluZ1wiLFxuICAgIH1cblxuICAgIHNldE1lc3NhZ2VzKChwcmV2KSA9PiBbLi4ucHJldiwgdXNlck1lc3NhZ2VdKVxuXG4gICAgdHJ5IHtcbiAgICAgIC8vIOS9v+eUqOWIhumYtuauteWKoOi9veWkhOeQhumcgOaxguaPkOS6pO+8jOS9hueOsOWcqOS4k+azqOS6juaWueahiOeUn+aIkFxuICAgICAgbGV0IHNvbHV0aW9uUmVzcG9uc2U6IGFueVxuICAgICAgbGV0IGxpdGVyYXR1cmVSZXN1bHRzOiBhbnkgPSBudWxsXG5cbiAgICAgIGF3YWl0IGVycm9ySGFuZGxlci5oYW5kbGVTdGFnZWRPcGVyYXRpb24oW1xuICAgICAgICB7XG4gICAgICAgICAgc3RhZ2U6IExvYWRpbmdTdGFnZS5BTkFMWVpJTkcsXG4gICAgICAgICAgb3BlcmF0aW9uOiBhc3luYyAoKSA9PiB7XG4gICAgICAgICAgICAvLyDpqozor4HpnIDmsYLlrozmlbTmgKdcbiAgICAgICAgICAgIGNvbnN0IHZhbGlkYXRpb24gPSBhd2FpdCBzb2x1dGlvbkFwaS52YWxpZGF0ZVJlcXVpcmVtZW50cyhyZXF1aXJlbWVudHMpXG4gICAgICAgICAgICByZXR1cm4gdmFsaWRhdGlvblxuICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIHN0YWdlOiBMb2FkaW5nU3RhZ2UuU0VBUkNISU5HX0xJVEVSQVRVUkUsXG4gICAgICAgICAgb3BlcmF0aW9uOiBhc3luYyAoKSA9PiB7XG4gICAgICAgICAgICAvLyDlpoLmnpzlkK/nlKjkuobmlofnjK7mkJzntKLvvIzlhYjmiafooYzmlofnjK7mkJzntKLojrflvpflj6/op4HnmoTnu5PmnpxcbiAgICAgICAgICAgIGlmIChlbmFibGVMaXRlcmF0dXJlU2VhcmNoKSB7XG4gICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgLy8g6LCD55So5pm66IO95paH54yu5pCc57SiQVBJ6I635b6X55So5oi35Y+v6KeB55qE5pCc57Si6L+H56iLXG4gICAgICAgICAgICAgICAgY29uc3QgbGl0ZXJhdHVyZVJlc3VsdCA9IGF3YWl0IGxpdGVyYXR1cmVBcGkuZW5oYW5jZWRMaXRlcmF0dXJlU2VhcmNoKHtcbiAgICAgICAgICAgICAgICAgIHF1ZXJ5OiBg5Y2V57uG6IOe5rWL5bqPICR7cmVxdWlyZW1lbnRzLnJlc2VhcmNoR29hbH0gJHtyZXF1aXJlbWVudHMuc2FtcGxlVHlwZX1gLFxuICAgICAgICAgICAgICAgICAgcmVxdWlyZW1lbnRzOiByZXF1aXJlbWVudHMsXG4gICAgICAgICAgICAgICAgICBlbmFibGVfbGl0ZXJhdHVyZV9zZWFyY2g6IHRydWVcbiAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIC8vIOS/neWtmOaWh+eMruaQnOe0oue7k+aenOS7peS+v+WQjue7reS9v+eUqFxuICAgICAgICAgICAgICAgIGxpdGVyYXR1cmVSZXN1bHRzID0gbGl0ZXJhdHVyZVJlc3VsdFxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIHJldHVybiB7IFxuICAgICAgICAgICAgICAgICAgc3RlcDogXCJsaXRlcmF0dXJlX2V2aWRlbmNlX2NvbGxlY3RlZFwiLFxuICAgICAgICAgICAgICAgICAgbGl0ZXJhdHVyZV9yZXN1bHRzOiBsaXRlcmF0dXJlUmVzdWx0LFxuICAgICAgICAgICAgICAgICAgc2VhcmNoX3Zpc2libGU6IHRydWVcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS53YXJuKFwi5paH54yu5pCc57Si5aSx6LSl77yM57un57ut5pa55qGI55Sf5oiQOlwiLCBlcnJvcilcbiAgICAgICAgICAgICAgICAvLyDmkJzntKLlpLHotKXkuZ/nu6fnu63mtYHnqIvvvIzlnKjmlrnmoYjnlJ/miJDml7bkvJrmnInlhoXpg6jmlofnjK7mlK/mkpFcbiAgICAgICAgICAgICAgICByZXR1cm4geyBzdGVwOiBcImxpdGVyYXR1cmVfZmFsbGJhY2tfbW9kZVwiIH1cbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgLy8g5paH54yu5pCc57Si5Zyo5ZCO56uv5YaF6YOo6L+b6KGM77yI55So5oi35LiN5Y+v6KeB77yJXG4gICAgICAgICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCA4MDApKVxuICAgICAgICAgICAgICByZXR1cm4geyBzdGVwOiBcImxpdGVyYXR1cmVfZXZpZGVuY2VfY29sbGVjdGVkXCIsIHNlYXJjaF92aXNpYmxlOiBmYWxzZSB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgc3RhZ2U6IExvYWRpbmdTdGFnZS5HRU5FUkFUSU5HX1JFU1BPTlNFLFxuICAgICAgICAgIG9wZXJhdGlvbjogYXN5bmMgKCkgPT4ge1xuICAgICAgICAgICAgLy8g5LyY5YWI6LCD55So57u85ZCI5pa55qGI5qGG5p62QVBJ77yI5oyJ6K6+6K6h5paH5qGj6KaB5rGC77yJXG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+agCDlvIDlp4vosIPnlKjnu7zlkIjmlrnmoYjmoYbmnrZBUEkuLi4nKVxuICAgICAgICAgICAgICBjb25zb2xlLmxvZygn6K+35rGC5pWw5o2uOicsIHtcbiAgICAgICAgICAgICAgICByZXF1aXJlbWVudHM6IHJlcXVpcmVtZW50cyxcbiAgICAgICAgICAgICAgICB1c2VyX21lc3NhZ2U6IHJlcXVpcmVtZW50U3VtbWFyeSxcbiAgICAgICAgICAgICAgICBmcmFtZXdvcmtfdGVtcGxhdGU6ICdzdGFuZGFyZCcsXG4gICAgICAgICAgICAgICAgZW5hYmxlX2xpdGVyYXR1cmVfc2VhcmNoOiBlbmFibGVMaXRlcmF0dXJlU2VhcmNoXG4gICAgICAgICAgICAgIH0pXG5cbiAgICAgICAgICAgICAgY29uc3QgZnJhbWV3b3JrUmVzcG9uc2UgPSBhd2FpdCBzb2x1dGlvbkFwaS5nZW5lcmF0ZUNvbXByZWhlbnNpdmVGcmFtZXdvcmsoe1xuICAgICAgICAgICAgICAgIHJlcXVpcmVtZW50czogcmVxdWlyZW1lbnRzLFxuICAgICAgICAgICAgICAgIHVzZXJfbWVzc2FnZTogcmVxdWlyZW1lbnRTdW1tYXJ5LFxuICAgICAgICAgICAgICAgIGZyYW1ld29ya190ZW1wbGF0ZTogJ3N0YW5kYXJkJyxcbiAgICAgICAgICAgICAgICBlbmFibGVfbGl0ZXJhdHVyZV9zZWFyY2g6IGVuYWJsZUxpdGVyYXR1cmVTZWFyY2hcbiAgICAgICAgICAgICAgfSlcblxuICAgICAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIOe7vOWQiOaWueahiOahhuaetkFQSeWTjeW6lDonLCBmcmFtZXdvcmtSZXNwb25zZSlcblxuICAgICAgICAgICAgICBpZiAoZnJhbWV3b3JrUmVzcG9uc2Uuc3VjY2Vzcykge1xuICAgICAgICAgICAgICAgIC8vIOWwhue7vOWQiOaWueahiOahhuaetue7k+aenOi9rOaNouS4uuWJjeerr+mcgOimgeeahOagvOW8j1xuICAgICAgICAgICAgICAgIHNvbHV0aW9uUmVzcG9uc2UgPSB7XG4gICAgICAgICAgICAgICAgICBzb2x1dGlvbl9pZDogZnJhbWV3b3JrUmVzcG9uc2UuZnJhbWV3b3JrX2lkLFxuICAgICAgICAgICAgICAgICAgZ2VuZXJhdGVkX2F0OiBmcmFtZXdvcmtSZXNwb25zZS5nZW5lcmF0aW9uX3RpbWUsXG4gICAgICAgICAgICAgICAgICBjbGllbnRfcmVxdWlyZW1lbnRzOiByZXF1aXJlbWVudHMsXG4gICAgICAgICAgICAgICAgICBjb21wcmVoZW5zaXZlX2ZyYW1ld29yazogZnJhbWV3b3JrUmVzcG9uc2UuZGF0YSwgLy8g5a6M5pW05qGG5p625pWw5o2uXG4gICAgICAgICAgICAgICAgICByZWNvbW1lbmRlZF9zb2x1dGlvbjoge1xuICAgICAgICAgICAgICAgICAgICBwbGF0Zm9ybTogXCLljZXnu4bog57mtYvluo/op6PlhrPmlrnmoYhcIixcbiAgICAgICAgICAgICAgICAgICAgcmVhc29uaW5nOiBcIuWfuuS6juiuvuiuoeaWh+aho+eahOWujOaVtOaWueahiOahhuaetu+8jOWMheWQq+aWueahiOamguiniOOAgeeglOeptuaEj+WbvuWIhuaekOOAgeWFs+mUruimgee0oOOAgeaWh+eMruaOqOiNkOOAgeW5s+WPsOWvueavlOWSjOWunuaWveinhOWIklwiLFxuICAgICAgICAgICAgICAgICAgICBzcGVjaWZpY2F0aW9uczogZnJhbWV3b3JrUmVzcG9uc2UuZGF0YS5zb2x1dGlvbl9vdmVydmlld1xuICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgIGNvc3RfYW5hbHlzaXM6IGZyYW1ld29ya1Jlc3BvbnNlLmRhdGEuc29sdXRpb25fb3ZlcnZpZXc/LmNvc3RfYW5hbHlzaXMgfHwge30sXG4gICAgICAgICAgICAgICAgICByaXNrX2Fzc2Vzc21lbnQ6IGZyYW1ld29ya1Jlc3BvbnNlLmRhdGEucmlza19hc3Nlc3NtZW50IHx8IHt9LFxuICAgICAgICAgICAgICAgICAgdGltZWxpbmU6IGZyYW1ld29ya1Jlc3BvbnNlLmRhdGEuaW1wbGVtZW50YXRpb25fcGxhbj8udGltZWxpbmUgfHwge30sXG4gICAgICAgICAgICAgICAgICBkZWxpdmVyYWJsZXM6IFtcbiAgICAgICAgICAgICAgICAgICAgXCLmlrnmoYjmpoLop4jljaHniYdcIixcbiAgICAgICAgICAgICAgICAgICAgXCLnoJTnqbbmhI/lm77nsr7lh4bmkJzntKLpk77mjqVcIixcbiAgICAgICAgICAgICAgICAgICAgXCLlhbPplK7opoHntKDliIbmnpBcIixcbiAgICAgICAgICAgICAgICAgICAgXCLmmbrog73mlofnjK7mjqjojZBcIixcbiAgICAgICAgICAgICAgICAgICAgXCLlubPlj7Dlr7nmr5TliIbmnpBcIixcbiAgICAgICAgICAgICAgICAgICAgXCLpobnnm67lrp7mlr3op4TliJJcIlxuICAgICAgICAgICAgICAgICAgXSxcbiAgICAgICAgICAgICAgICAgIG5leHRfc3RlcHM6IFtcbiAgICAgICAgICAgICAgICAgICAgXCLmn6XnnIvlrozmlbTnmoTmlrnmoYjmoYbmnrbliIbmnpBcIixcbiAgICAgICAgICAgICAgICAgICAgXCLkvb/nlKjnsr7lh4bmkJzntKLpk77mjqXojrflj5bmlofnjK5cIixcbiAgICAgICAgICAgICAgICAgICAgXCLmoLnmja7lubPlj7Dlr7nmr5TpgInmi6nmioDmnK/mlrnmoYhcIlxuICAgICAgICAgICAgICAgICAgXSxcbiAgICAgICAgICAgICAgICAgIGNvbnRhY3RfaW5mbzoge1xuICAgICAgICAgICAgICAgICAgICBlbWFpbDogXCJzdXBwb3J0QGNlbGxmb3JnZS5haVwiLFxuICAgICAgICAgICAgICAgICAgICBwaG9uZTogXCI0MDAtMTIzLTQ1NjdcIlxuICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgIGZyYW1ld29ya19mZWF0dXJlczoge1xuICAgICAgICAgICAgICAgICAgICBjb21wcmVoZW5zaXZlX2ZyYW1ld29yazogdHJ1ZSxcbiAgICAgICAgICAgICAgICAgICAgcmVzZWFyY2hfaW50ZW50X2FuYWx5c2lzOiB0cnVlLFxuICAgICAgICAgICAgICAgICAgICBwcmVjaXNpb25fc2VhcmNoX2xpbmtzOiB0cnVlLFxuICAgICAgICAgICAgICAgICAgICBsaXRlcmF0dXJlX3JlY29tbWVuZGF0aW9uczogdHJ1ZSxcbiAgICAgICAgICAgICAgICAgICAgcGxhdGZvcm1fY29tcGFyaXNvbjogdHJ1ZVxuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ+e7vOWQiOaWueahiOahhuaetkFQSeiwg+eUqOWksei0pScpXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0gY2F0Y2ggKGZyYW1ld29ya0Vycm9yKSB7XG4gICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDnu7zlkIjmlrnmoYjmoYbmnrZBUEnlpLHotKU6JywgZnJhbWV3b3JrRXJyb3IpXG4gICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+mUmeivr+ivpuaDhTonLCB7XG4gICAgICAgICAgICAgICAgbWVzc2FnZTogZnJhbWV3b3JrRXJyb3IubWVzc2FnZSxcbiAgICAgICAgICAgICAgICBzdGFjazogZnJhbWV3b3JrRXJyb3Iuc3RhY2ssXG4gICAgICAgICAgICAgICAgbmFtZTogZnJhbWV3b3JrRXJyb3IubmFtZVxuICAgICAgICAgICAgICB9KVxuXG4gICAgICAgICAgICAgIC8vIOS9v+eUqOeugOWMlueahOmZjee6p+aWueahiFxuICAgICAgICAgICAgICBzb2x1dGlvblJlc3BvbnNlID0ge1xuICAgICAgICAgICAgICAgIHNvbHV0aW9uX2lkOiBgZmFsbGJhY2tfJHtEYXRlLm5vdygpfWAsXG4gICAgICAgICAgICAgICAgZ2VuZXJhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgICAgICAgY2xpZW50X3JlcXVpcmVtZW50czogcmVxdWlyZW1lbnRzLFxuICAgICAgICAgICAgICAgIHJlY29tbWVuZGVkX3NvbHV0aW9uOiB7XG4gICAgICAgICAgICAgICAgICBwbGF0Zm9ybTogXCJDZWxsRm9yZ2UgQUkg5Z+656GA5pa55qGIXCIsXG4gICAgICAgICAgICAgICAgICByZWFzb25pbmc6IFwi55Sx5LqO5oqA5pyv5Y6f5Zug77yM5b2T5YmN5o+Q5L6b5Z+656GA5pa55qGI44CC5oiR5Lus55qE5LiT5a625bCG5Li65oKo6L+b6KGM5Lq65bel5YiG5p6Q77yM56Gu5L+d5pa55qGI55qE5YeG56Gu5oCn5ZKM5Liq5oCn5YyW44CCXCIsXG4gICAgICAgICAgICAgICAgICBzcGVjaWZpY2F0aW9uczoge31cbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIGNvc3RfYW5hbHlzaXM6IHt9LFxuICAgICAgICAgICAgICAgIHJpc2tfYXNzZXNzbWVudDoge30sXG4gICAgICAgICAgICAgICAgdGltZWxpbmU6IHt9LFxuICAgICAgICAgICAgICAgIGRlbGl2ZXJhYmxlczogW1xuICAgICAgICAgICAgICAgICAgXCLkurrlt6XkuJPlrrbliIbmnpDmiqXlkYpcIixcbiAgICAgICAgICAgICAgICAgIFwi5Liq5oCn5YyW5oqA5pyv5pa55qGI6K6+6K6hXCIsXG4gICAgICAgICAgICAgICAgICBcIuivpue7huaIkOacrOaViOebiuWIhuaekFwiXG4gICAgICAgICAgICAgICAgXSxcbiAgICAgICAgICAgICAgICBuZXh0X3N0ZXBzOiBbXG4gICAgICAgICAgICAgICAgICBcIuiBlOezu+S4k+Wutui/m+ihjOS6uuW3peWIhuaekFwiLFxuICAgICAgICAgICAgICAgICAgXCLojrflj5bkuKrmgKfljJbmlrnmoYjorr7orqFcIixcbiAgICAgICAgICAgICAgICAgIFwi56Gu5a6a5pyA5LyY5oqA5pyv6Lev57q/XCJcbiAgICAgICAgICAgICAgICBdLFxuICAgICAgICAgICAgICAgIGNvbnRhY3RfaW5mbzoge1xuICAgICAgICAgICAgICAgICAgZW1haWw6IFwic3VwcG9ydEBjZWxsZm9yZ2UuYWlcIixcbiAgICAgICAgICAgICAgICAgIHBob25lOiBcIjQwMC0xMjMtNDU2N1wiXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICBmYWxsYmFja19ub3RpY2U6IHtcbiAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6IFwi5Li656Gu5L+d5pa55qGI6LSo6YeP77yM5bu66K6u6IGU57O75LiT5a626L+b6KGM5Lq65bel5YiG5p6QXCIsXG4gICAgICAgICAgICAgICAgICBleHBlcnRfY29udGFjdDogdHJ1ZSxcbiAgICAgICAgICAgICAgICAgIHJlYXNvbjogXCLmioDmnK/mnI3liqHmmoLml7bkuI3lj6/nlKhcIlxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIHNvbHV0aW9uUmVzcG9uc2VcbiAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICBzdGFnZTogTG9hZGluZ1N0YWdlLkZPUk1BVFRJTkcsXG4gICAgICAgICAgb3BlcmF0aW9uOiBhc3luYyAoKSA9PiB7XG4gICAgICAgICAgICAvLyDmoLzlvI/ljJbmlrnmoYjlhoXlrrnvvIzlpoLmnpzmnInmlofnjK7nu5PmnpzliJnljIXlkKvov5vljrtcbiAgICAgICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCA0MDApKVxuICAgICAgICAgICAgcmV0dXJuIHsgc3RlcDogXCJzb2x1dGlvbl9mb3JtYXR0ZWRcIiB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICBdLFxuICAgICAgKHN0YWdlLCBwcm9ncmVzcykgPT4ge1xuICAgICAgICBpZiAoIWlzTG9hZGluZykge1xuICAgICAgICAgIHN0YXJ0TG9hZGluZyhzdGFnZSlcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICB1cGRhdGVTdGFnZShzdGFnZSwgcHJvZ3Jlc3MpXG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIG1heFJldHJpZXM6IDEsXG4gICAgICAgIHJldHJ5RGVsYXk6IDMwMDAsXG4gICAgICAgIHVzZXJGcmllbmRseU1lc3NhZ2U6IFwi5pa55qGI55Sf5oiQ5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VXCIsXG4gICAgICAgIHNob3dUb2FzdDogZmFsc2UsXG4gICAgICAgIG9uUmV0cnk6IChhdHRlbXB0LCBlcnJvcikgPT4ge1xuICAgICAgICAgIGlmIChlcnJvci5tZXNzYWdlPy5pbmNsdWRlcygnZmV0Y2gnKSB8fCBlcnJvci5tZXNzYWdlPy5pbmNsdWRlcygn572R57ucJykpIHtcbiAgICAgICAgICAgIHRvYXN0LmluZm8oYOe9kee7nOmXrumimO+8jOato+WcqOmHjeivleeUn+aIkOaWueahiC4uLmApXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9KVxuXG4gICAgICAvLyDmm7TmlrDnlKjmiLfmtojmga/nirbmgIFcbiAgICAgIHNldE1lc3NhZ2VzKChwcmV2KSA9PlxuICAgICAgICBwcmV2Lm1hcChtc2cgPT5cbiAgICAgICAgICBtc2cuaWQgPT09IHVzZXJNZXNzYWdlLmlkXG4gICAgICAgICAgICA/IHsgLi4ubXNnLCBzdGF0dXM6IFwic2VudFwiIGFzIGNvbnN0IH1cbiAgICAgICAgICAgIDogbXNnXG4gICAgICAgIClcbiAgICAgIClcblxuICAgICAgLy8g55Sf5oiQ5pa55qGI5bGV56S655qEQUnlm57lpI3vvIzljIXlkKvmlofnjK7mkJzntKLnu5PmnpzvvIjlpoLmnpzmnInvvIlcbiAgICAgIGNvbnN0IHNvbHV0aW9uQ29udGVudCA9IGZvcm1hdFNvbHV0aW9uUmVzcG9uc2Uoc29sdXRpb25SZXNwb25zZSwgbGl0ZXJhdHVyZVJlc3VsdHMpXG4gICAgICBcbiAgICAgIC8vIOabtOaWsHNvdXJjZXPku6Xlj43mmKDlrp7pmYXnmoTmlbDmja7mnaXmupBcbiAgICAgIGxldCBzb3VyY2VzID0gW1wi5pa55qGI55Sf5oiQ57O757ufXCIsIFwi5oqA5pyv55+l6K+G5bqTXCIsIFwi5oiQ5pys5YiG5p6Q5byV5pOOXCJdXG4gICAgICBcbiAgICAgIC8vIOagueaNruS4jeWQjOeahOaWueahiOexu+Wei+S9v+eUqOS4jeWQjOeahOaVsOaNruadpea6kFxuICAgICAgaWYgKHNvbHV0aW9uUmVzcG9uc2UuYWlfYW5hbHlzaXNfZmVhdHVyZXM/LmludGVudF9hbmFseXplZCkge1xuICAgICAgICBzb3VyY2VzID0gW1wiQUnmhI/lm77liIbmnpDlvJXmk45cIiwgXCLnlKjmiLfooYzkuLrliIbmnpBcIiwgXCLkuKrmgKfljJbnrpfms5VcIiwgXCLkuJPkuJrnn6Xor4blupNcIl1cbiAgICAgICAgaWYgKHNvbHV0aW9uUmVzcG9uc2UuYWlfYW5hbHlzaXNfZmVhdHVyZXMuYW5hbHlzaXNfc291cmNlcykge1xuICAgICAgICAgIHNvdXJjZXMgPSBzb3VyY2VzLmNvbmNhdChzb2x1dGlvblJlc3BvbnNlLmFpX2FuYWx5c2lzX2ZlYXR1cmVzLmFuYWx5c2lzX3NvdXJjZXMpXG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSBpZiAoc29sdXRpb25SZXNwb25zZS5zb2x1dGlvbl9pZD8uaW5jbHVkZXMoJ2ludGVsbGlnZW50JykgfHwgc29sdXRpb25SZXNwb25zZS5pbnRlbGxpZ2VudF9mZWF0dXJlcykge1xuICAgICAgICBzb3VyY2VzID0gW1wiQUnmmbrog73mjqjojZDlvJXmk45cIiwgXCLkuJPkuJrnn6Xor4blupNcIiwgXCLng63ngrnmlofnjK7lj5HnjrBcIiwgXCLmioDmnK/op4TmoLzmlbDmja7lupNcIiwgXCLpo47pmanor4TkvLDnrpfms5VcIl1cbiAgICAgIH0gZWxzZSBpZiAoc29sdXRpb25SZXNwb25zZS5mYWxsYmFja19ub3RpY2UpIHtcbiAgICAgICAgc291cmNlcyA9IFtcIuWfuuehgOefpeivhuW6k1wiLCBcIuS4k+Wutue7j+mqjOW6k1wiLCBcIuagh+WHhuaWueahiOaooeadv1wiXVxuICAgICAgfVxuICAgICAgXG4gICAgICBpZiAobGl0ZXJhdHVyZVJlc3VsdHMgJiYgZW5hYmxlTGl0ZXJhdHVyZVNlYXJjaCkge1xuICAgICAgICBzb3VyY2VzLnB1c2goXCLmlofnjK7mlbDmja7lupNcIiwgXCLmmbrog73mkJzntKLlvJXmk45cIilcbiAgICAgIH1cbiAgICAgIFxuICAgICAgY29uc3QgYWlSZXNwb25zZTogTWVzc2FnZSA9IHtcbiAgICAgICAgaWQ6IChEYXRlLm5vdygpICsgMSkudG9TdHJpbmcoKSxcbiAgICAgICAgdHlwZTogXCJhaVwiLFxuICAgICAgICBjb250ZW50OiBzb2x1dGlvbkNvbnRlbnQsXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSxcbiAgICAgICAgc3RhdHVzOiBcInJlYWRcIixcbiAgICAgICAgY29uZmlkZW5jZTogc29sdXRpb25SZXNwb25zZS5haV9hbmFseXNpc19mZWF0dXJlcz8uY29uZmlkZW5jZV9zY29yZSB8fCBcbiAgICAgICAgICAgICAgICAgICAobGl0ZXJhdHVyZVJlc3VsdHMgPyAwLjk4IDogXG4gICAgICAgICAgICAgICAgICAgIHNvbHV0aW9uUmVzcG9uc2UuZmFsbGJhY2tfbm90aWNlID8gMC43MCA6IDAuOTUpLFxuICAgICAgICBzb3VyY2VzOiBzb3VyY2VzLFxuICAgICAgICBzdWdnZXN0aW9uczogW1xuICAgICAgICAgIFwi5p+l55yL6K+m57uG55qE5oiQ5pys5YiG5p6QXCIsXG4gICAgICAgICAgXCLkuobop6Ppobnnm67ml7bpl7Top4TliJJcIixcbiAgICAgICAgICBcIuiOt+WPluaKgOacr+W5s+WPsOWvueavlFwiLFxuICAgICAgICAgIFwi6IGU57O75LiT5a626L+b6KGM5ZKo6K+iXCJcbiAgICAgICAgXVxuICAgICAgfVxuXG4gICAgICBzZXRNZXNzYWdlcygocHJldikgPT4ge1xuICAgICAgICBjb25zdCBuZXdNZXNzYWdlcyA9IFsuLi5wcmV2LCBhaVJlc3BvbnNlXVxuICAgICAgICAvLyDoh6rliqjkv53lrZjlr7nor51cbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiBhdXRvU2F2ZUNvbnZlcnNhdGlvbihuZXdNZXNzYWdlcyksIDEwMDApXG4gICAgICAgIHJldHVybiBuZXdNZXNzYWdlc1xuICAgICAgfSlcblxuICAgICAgZmluaXNoTG9hZGluZygpXG4gICAgICB0b2FzdC5zdWNjZXNzKFwi5bey5Z+65LqO5oKo55qE6ZyA5rGC55Sf5oiQ5LiT5Lia5pa55qGI77yBXCIpXG5cbiAgICB9IGNhdGNoIChlcnI6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcihcIueUn+aIkOW7uuiuruWksei0pTpcIiwgZXJyKVxuXG4gICAgICAvLyDmm7TmlrDnlKjmiLfmtojmga/nirbmgIHkuLrplJnor69cbiAgICAgIHNldE1lc3NhZ2VzKChwcmV2KSA9PlxuICAgICAgICBwcmV2Lm1hcChtc2cgPT5cbiAgICAgICAgICBtc2cuaWQgPT09IHVzZXJNZXNzYWdlLmlkXG4gICAgICAgICAgICA/IHsgLi4ubXNnLCBzdGF0dXM6IFwiZXJyb3JcIiBhcyBjb25zdCB9XG4gICAgICAgICAgICA6IG1zZ1xuICAgICAgICApXG4gICAgICApXG5cbiAgICAgIGNhbmNlbExvYWRpbmcoKVxuICAgICAgLy8g6ZSZ6K+v5bey57uP55SxZXJyb3JIYW5kbGVy5aSE55CGXG4gICAgfVxuICB9XG5cbiAgLy8g55Sf5oiQ6ZyA5rGC5oC757uTIC0g5pu05paw5Lul5Yy56YWN5paw55qE5a2X5q6157uT5p6EXG4gIGNvbnN0IGdlbmVyYXRlUmVxdWlyZW1lbnRTdW1tYXJ5ID0gKHJlcXM6IFJlcXVpcmVtZW50RGF0YSk6IHN0cmluZyA9PiB7XG4gICAgY29uc3Qgc2VjdGlvbnMgPSBbXVxuXG4gICAgc2VjdGlvbnMucHVzaChcIvCfk4sgKirpobnnm67pnIDmsYLmgLvnu5MqKlxcblwiKVxuXG4gICAgLy8g5Z+656GA5YiG57G75L+h5oGvXG4gICAgaWYgKHJlcXMuc3BlY2llc1R5cGUpIHNlY3Rpb25zLnB1c2goYPCfp6wgKirniannp43nsbvlnosqKjogJHtyZXFzLnNwZWNpZXNUeXBlfWApXG4gICAgaWYgKHJlcXMuZXhwZXJpbWVudFR5cGUpIHNlY3Rpb25zLnB1c2goYPCflKwgKirlrp7pqoznsbvlnosqKjogJHtyZXFzLmV4cGVyaW1lbnRUeXBlfWApXG4gICAgaWYgKHJlcXMucmVzZWFyY2hHb2FsKSBzZWN0aW9ucy5wdXNoKGDwn46vICoq56CU56m255uu5qCHKio6ICR7cmVxcy5yZXNlYXJjaEdvYWx9YClcblxuICAgIC8vIOagt+acrOS/oeaBr1xuICAgIGlmIChyZXFzLnNhbXBsZVR5cGUgfHwgcmVxcy5zYW1wbGVDb3VudCB8fCByZXFzLnNhbXBsZUNvbmRpdGlvbiB8fCByZXFzLmNlbGxDb3VudCkge1xuICAgICAgc2VjdGlvbnMucHVzaChcIlxcbvCfp6ogKirmoLfmnKzkv6Hmga8qKlwiKVxuICAgICAgaWYgKHJlcXMuc2FtcGxlVHlwZSkgc2VjdGlvbnMucHVzaChg4oCiIOagt+acrOexu+WeizogJHtyZXFzLnNhbXBsZVR5cGV9YClcbiAgICAgIGlmIChyZXFzLnNhbXBsZUNvdW50KSBzZWN0aW9ucy5wdXNoKGDigKIg5qC35pys5pWw55uuOiAke3JlcXMuc2FtcGxlQ291bnR9YClcbiAgICAgIGlmIChyZXFzLnNhbXBsZUNvbmRpdGlvbikgc2VjdGlvbnMucHVzaChg4oCiIOagt+acrOeKtuaAgTogJHtyZXFzLnNhbXBsZUNvbmRpdGlvbn1gKVxuICAgICAgaWYgKHJlcXMuc2FtcGxlUHJvY2Vzc2luZykgc2VjdGlvbnMucHVzaChg4oCiIOWkhOeQhuaWueW8jzogJHtyZXFzLnNhbXBsZVByb2Nlc3Npbmd9YClcbiAgICAgIGlmIChyZXFzLmNlbGxDb3VudCkgc2VjdGlvbnMucHVzaChg4oCiIOe7huiDnuaVsOmHjzogJHtyZXFzLmNlbGxDb3VudH1gKVxuICAgICAgaWYgKHJlcXMuY2VsbFZpYWJpbGl0eSkgc2VjdGlvbnMucHVzaChg4oCiIOe7huiDnua0u+WKmzogJHtyZXFzLmNlbGxWaWFiaWxpdHl9YClcbiAgICB9XG5cbiAgICAvLyDpobnnm67op4TliJJcbiAgICBpZiAocmVxcy5idWRnZXQgfHwgcmVxcy50aW1lbGluZSB8fCByZXFzLnVyZ2VuY3lMZXZlbCkge1xuICAgICAgc2VjdGlvbnMucHVzaChcIlxcbvCfk4UgKirpobnnm67op4TliJIqKlwiKVxuICAgICAgaWYgKHJlcXMuYnVkZ2V0KSBzZWN0aW9ucy5wdXNoKGDigKIg6aKE566X6IyD5Zu0OiAke3JlcXMuYnVkZ2V0fWApXG4gICAgICBpZiAocmVxcy50aW1lbGluZSkgc2VjdGlvbnMucHVzaChg4oCiIOmhueebruWRqOacnzogJHtyZXFzLnRpbWVsaW5lfWApXG4gICAgICBpZiAocmVxcy51cmdlbmN5TGV2ZWwpIHNlY3Rpb25zLnB1c2goYOKAoiDntKfmgKXnqIvluqY6ICR7cmVxcy51cmdlbmN5TGV2ZWx9YClcbiAgICB9XG5cbiAgICAvLyDmioDmnK/nu4boioJcbiAgICBpZiAocmVxcy5zZXF1ZW5jaW5nRGVwdGggfHwgcmVxcy5hbmFseXNpc1R5cGUgfHwgcmVxcy5kYXRhQW5hbHlzaXNOZWVkcyB8fCByZXFzLm5lZWRzQ2VsbFNvcnRpbmcgfHwgcmVxcy5zcGVjaWFsUmVxdWlyZW1lbnRzKSB7XG4gICAgICBzZWN0aW9ucy5wdXNoKFwiXFxu4pqZ77iPICoq5oqA5pyv57uG6IqCKipcIilcbiAgICAgIGlmIChyZXFzLnNlcXVlbmNpbmdEZXB0aCkgc2VjdGlvbnMucHVzaChg4oCiIOa1i+W6j+a3seW6pjogJHtyZXFzLnNlcXVlbmNpbmdEZXB0aH1gKVxuICAgICAgaWYgKHJlcXMuYW5hbHlzaXNUeXBlKSBzZWN0aW9ucy5wdXNoKGDigKIg5YiG5p6Q57G75Z6LOiAke3JlcXMuYW5hbHlzaXNUeXBlfWApXG4gICAgICBpZiAocmVxcy5kYXRhQW5hbHlzaXNOZWVkcykgc2VjdGlvbnMucHVzaChg4oCiIOaVsOaNruWIhuaekOmcgOaxgjogJHtyZXFzLmRhdGFBbmFseXNpc05lZWRzfWApXG4gICAgICBpZiAocmVxcy5uZWVkc0NlbGxTb3J0aW5nKSBzZWN0aW9ucy5wdXNoKGDigKIg57uG6IOe5YiG6YCJOiAke3JlcXMubmVlZHNDZWxsU29ydGluZ31gKVxuICAgICAgaWYgKHJlcXMuc3BlY2lhbFJlcXVpcmVtZW50cykgc2VjdGlvbnMucHVzaChg4oCiIOeJueauiuimgeaxgjogJHtyZXFzLnNwZWNpYWxSZXF1aXJlbWVudHN9YClcbiAgICB9XG5cbiAgICBzZWN0aW9ucy5wdXNoKFwiXFxu6K+35Li65oiR55Sf5oiQ6K+m57uG55qE5oqA5pyv5pa55qGI5bu66K6u44CCXCIpXG5cbiAgICByZXR1cm4gc2VjdGlvbnMuam9pbihcIlxcblwiKVxuICB9XG5cbiAgLy8g5qC85byP5YyW5pa55qGI5ZON5bqU77yM5YyF5ZCr5paH54yu5pCc57Si57uT5p6c5ZKM5Liq5oCn5YyW5L+h5oGvXG4gIGNvbnN0IGZvcm1hdFNvbHV0aW9uUmVzcG9uc2UgPSAoc29sdXRpb25SZXNwb25zZTogYW55LCBsaXRlcmF0dXJlUmVzdWx0cz86IGFueSk6IHN0cmluZyA9PiB7XG4gICAgaWYgKCFzb2x1dGlvblJlc3BvbnNlKSByZXR1cm4gXCLmirHmrYnvvIzmlrnmoYjnlJ/miJDlpLHotKXjgIJcIlxuXG4gICAgY29uc3Qge1xuICAgICAgc29sdXRpb25faWQsXG4gICAgICBjbGllbnRfcmVxdWlyZW1lbnRzLFxuICAgICAgcmVjb21tZW5kZWRfc29sdXRpb24sXG4gICAgICBjb3N0X2FuYWx5c2lzLFxuICAgICAgcmlza19hc3Nlc3NtZW50LFxuICAgICAgdGltZWxpbmUsXG4gICAgICBkZWxpdmVyYWJsZXMsXG4gICAgICBuZXh0X3N0ZXBzLFxuICAgICAgY29udGFjdF9pbmZvLFxuICAgICAgaW50ZWxsaWdlbnRfZmVhdHVyZXMsXG4gICAgICBpbnRlbnRfYW5hbHlzaXMsXG4gICAgICByZXNlYXJjaF9kb21haW4sXG4gICAgICBwZXJzb25hbGl6YXRpb25fbGV2ZWwsXG4gICAgICBhaV9hbmFseXNpc19mZWF0dXJlcyxcbiAgICAgIGZhbGxiYWNrX25vdGljZVxuICAgIH0gPSBzb2x1dGlvblJlc3BvbnNlXG5cbiAgICBjb25zdCBzZWN0aW9ucyA9IFtdXG5cbiAgICAvLyDmo4DmtYvmmK/lkKbkuLrkuKrmgKfljJbmjqjojZDmiJZBSeWIhuaekOe7k+aenFxuICAgIGNvbnN0IGlzUGVyc29uYWxpemVkID0gcGVyc29uYWxpemF0aW9uX2xldmVsID09PSBcImhpZ2hcIiB8fCBpbnRlbnRfYW5hbHlzaXNcbiAgICBjb25zdCBpc0ludGVsbGlnZW50UmVjb21tZW5kYXRpb24gPSBzb2x1dGlvbl9pZD8uaW5jbHVkZXMoJ2ludGVsbGlnZW50JykgfHwgaW50ZWxsaWdlbnRfZmVhdHVyZXNcbiAgICBjb25zdCBpc0FpQW5hbHl6ZWQgPSBhaV9hbmFseXNpc19mZWF0dXJlcz8uaW50ZW50X2FuYWx5emVkIHx8IHNvbHV0aW9uX2lkPy5pbmNsdWRlcygnYWlfYW5hbHl6ZWQnKVxuICAgIGNvbnN0IGlzRmFsbGJhY2sgPSBmYWxsYmFja19ub3RpY2UgfHwgc29sdXRpb25faWQ/LmluY2x1ZGVzKCdmYWxsYmFjaycpXG5cbiAgICAvLyDmoLnmja7kuKrmgKfljJbnqIvluqblkoznoJTnqbbpoobln5/lrprliLbmoIfpophcbiAgICBpZiAoaXNGYWxsYmFjaykge1xuICAgICAgc2VjdGlvbnMucHVzaChcIuKaoO+4jyAqKkNlbGxGb3JnZSBBSSDln7rnoYDmlrnmoYgqKlwiKVxuICAgICAgc2VjdGlvbnMucHVzaChcIuKPsCDns7vnu5/mraPlnKjljYfnuqfvvIzkuJPlrrbkurrlt6XliIbmnpDkuK0uLi5cIilcbiAgICAgIHNlY3Rpb25zLnB1c2goYOaWueahiOe8luWPtzogJHtzb2x1dGlvbl9pZH1gKVxuICAgICAgc2VjdGlvbnMucHVzaChcIlwiKVxuICAgICAgXG4gICAgICAvLyDmt7vliqDlm57pgIDpgJrnn6VcbiAgICAgIGlmIChmYWxsYmFja19ub3RpY2UpIHtcbiAgICAgICAgc2VjdGlvbnMucHVzaChcIvCfk6IgKirph43opoHmj5DnpLoqKlwiKVxuICAgICAgICBzZWN0aW9ucy5wdXNoKGDigKIgJHtmYWxsYmFja19ub3RpY2UubWVzc2FnZX1gKVxuICAgICAgICBpZiAoZmFsbGJhY2tfbm90aWNlLmV4cGVydF9jb250YWN0KSB7XG4gICAgICAgICAgc2VjdGlvbnMucHVzaChcIuKAoiDkuJPlrrblm6LpmJ/lsIblnKgy5bCP5pe25YaF5LiO5oKo6IGU57O7XCIpXG4gICAgICAgIH1cbiAgICAgICAgc2VjdGlvbnMucHVzaChg4oCiIOWOn+WboDogJHtmYWxsYmFja19ub3RpY2UucmVhc29ufWApXG4gICAgICAgIHNlY3Rpb25zLnB1c2goXCJcIilcbiAgICAgIH1cbiAgICB9IGVsc2UgaWYgKGlzQWlBbmFseXplZCAmJiBhaV9hbmFseXNpc19mZWF0dXJlcykge1xuICAgICAgc2VjdGlvbnMucHVzaChcIvCfp6AgKipDZWxsRm9yZ2UgQUkg5pm66IO95oSP5Zu+5YiG5p6Q5pa55qGIKipcIilcbiAgICAgIHNlY3Rpb25zLnB1c2goXCLinKgg5Z+65LqO5rex5bqm55So5oi35oSP5Zu+5YiG5p6Q55qE5Liq5oCn5YyW5o6o6I2QXCIpXG4gICAgICBzZWN0aW9ucy5wdXNoKGDmlrnmoYjnvJblj7c6ICR7c29sdXRpb25faWR9YClcbiAgICAgIHNlY3Rpb25zLnB1c2goXCJcIilcbiAgICAgIFxuICAgICAgLy8g5re75YqgQUnliIbmnpDmkZjopoFcbiAgICAgIHNlY3Rpb25zLnB1c2goXCLwn5SNICoqQUnmhI/lm77liIbmnpDnu5PmnpwqKlwiKVxuICAgICAgc2VjdGlvbnMucHVzaChg4oCiIOWIhuaekOe9ruS/oeW6pu+8miR7TWF0aC5yb3VuZCgoYWlfYW5hbHlzaXNfZmVhdHVyZXMuY29uZmlkZW5jZV9zY29yZSB8fCAwLjg1KSAqIDEwMCl9JWApXG4gICAgICBpZiAoYWlfYW5hbHlzaXNfZmVhdHVyZXMuYW5hbHlzaXNfc291cmNlcz8ubGVuZ3RoID4gMCkge1xuICAgICAgICBzZWN0aW9ucy5wdXNoKGDigKIg5YiG5p6Q5p2l5rqQ77yaJHthaV9hbmFseXNpc19mZWF0dXJlcy5hbmFseXNpc19zb3VyY2VzLmpvaW4oXCIsIFwiKX1gKVxuICAgICAgfVxuICAgICAgc2VjdGlvbnMucHVzaChcIuKAoiDkuKrmgKfljJbnqIvluqbvvJrpq5jluqblrprliLZcIilcbiAgICAgIHNlY3Rpb25zLnB1c2goXCJcIilcbiAgICB9IGVsc2UgaWYgKGlzUGVyc29uYWxpemVkICYmIHJlc2VhcmNoX2RvbWFpbikge1xuICAgICAgc2VjdGlvbnMucHVzaChg8J+OryAqKkNlbGxGb3JnZSBBSSAke3Jlc2VhcmNoX2RvbWFpbn3kuJPkuJrmlrnmoYgqKmApXG4gICAgICBzZWN0aW9ucy5wdXNoKGDinKgg5Z+65LqO5oSP5Zu+5YiG5p6Q55qE5Liq5oCn5YyW5o6o6I2QYClcbiAgICAgIHNlY3Rpb25zLnB1c2goYOaWueahiOe8luWPtzogJHtzb2x1dGlvbl9pZH1gKVxuICAgICAgc2VjdGlvbnMucHVzaChcIlwiKVxuXG4gICAgICAvLyDmt7vliqDmhI/lm77liIbmnpDmkZjopoFcbiAgICAgIGlmIChpbnRlbnRfYW5hbHlzaXMpIHtcbiAgICAgICAgc2VjdGlvbnMucHVzaChcIvCfp6AgKirnlKjmiLfmhI/lm77liIbmnpAqKlwiKVxuICAgICAgICBzZWN0aW9ucy5wdXNoKGDigKIg56CU56m26aKG5Z+f77yaJHtpbnRlbnRfYW5hbHlzaXMucmVzZWFyY2hfZG9tYWluIHx8ICfpgJrnlKjnoJTnqbYnfWApXG4gICAgICAgIHNlY3Rpb25zLnB1c2goYOKAoiDpooTnrpfnsbvliKvvvJoke2ludGVudF9hbmFseXNpcy5idWRnZXRfY2F0ZWdvcnkgfHwgJ+agh+WHhuWeiyd9YClcbiAgICAgICAgc2VjdGlvbnMucHVzaChg4oCiIOe0p+aApeeoi+W6pu+8miR7aW50ZW50X2FuYWx5c2lzLnVyZ2VuY3lfbGV2ZWwgfHwgJ+W4uOinhCd9YClcbiAgICAgICAgc2VjdGlvbnMucHVzaChg4oCiIOaKgOacr+WBj+Wlve+8miR7aW50ZW50X2FuYWx5c2lzLnRlY2huaWNhbF9wcmVmZXJlbmNlIHx8ICcxMHggR2Vub21pY3MnfWApXG4gICAgICAgIHNlY3Rpb25zLnB1c2goYOKAoiDnva7kv6HluqbvvJoke01hdGgucm91bmQoKGludGVudF9hbmFseXNpcy5jb25maWRlbmNlX3Njb3JlIHx8IDAuODUpICogMTAwKX0lYClcbiAgICAgICAgc2VjdGlvbnMucHVzaChcIlwiKVxuICAgICAgfVxuICAgIH0gZWxzZSBpZiAoaXNJbnRlbGxpZ2VudFJlY29tbWVuZGF0aW9uKSB7XG4gICAgICBzZWN0aW9ucy5wdXNoKFwi8J+OryAqKkNlbGxGb3JnZSBBSSDmmbrog73mjqjojZDmlrnmoYgqKlwiKVxuICAgICAgc2VjdGlvbnMucHVzaChg5pa55qGI57yW5Y+3OiAke3NvbHV0aW9uX2lkfWApXG4gICAgICBzZWN0aW9ucy5wdXNoKFwiXCIpXG4gICAgfSBlbHNlIHtcbiAgICAgIHNlY3Rpb25zLnB1c2goXCLwn46vICoqQ2VsbEZvcmdlIEFJIOS4k+S4muaWueahiCoqXCIpXG4gICAgICBzZWN0aW9ucy5wdXNoKGDmlrnmoYjnvJblj7c6ICR7c29sdXRpb25faWR9YClcbiAgICAgIHNlY3Rpb25zLnB1c2goXCJcIilcbiAgICB9XG5cblxuXG5cblxuICAgIC8vIOmjjumZqeivhOS8sCAtIOaUr+aMgeaWsOagvOW8j+WSjOaXp+agvOW8j1xuICAgIGlmIChyaXNrX2Fzc2Vzc21lbnQpIHtcbiAgICAgIHNlY3Rpb25zLnB1c2goXCLimqDvuI8gKirpo47pmanor4TkvLAqKlwiKVxuICAgICAgXG4gICAgICBpZiAocmlza19hc3Nlc3NtZW50LnN1Y2Nlc3NfcHJvYmFiaWxpdHkpIHtcbiAgICAgICAgc2VjdGlvbnMucHVzaChgKirmiJDlip/mpoLnjocqKjogJHtyaXNrX2Fzc2Vzc21lbnQuc3VjY2Vzc19wcm9iYWJpbGl0eX1gKVxuICAgICAgfVxuICAgICAgXG4gICAgICBpZiAocmlza19hc3Nlc3NtZW50Lm92ZXJhbGxfcmlza19sZXZlbCkge1xuICAgICAgICBzZWN0aW9ucy5wdXNoKGAqKuaVtOS9k+mjjumZqeetiee6pyoqOiAke3Jpc2tfYXNzZXNzbWVudC5vdmVyYWxsX3Jpc2tfbGV2ZWx9YClcbiAgICAgIH1cbiAgICAgIFxuICAgICAgLy8g5paw5qC85byP5oqA5pyv6aOO6ZmpXG4gICAgICBpZiAocmlza19hc3Nlc3NtZW50LnRlY2huaWNhbF9yaXNrcz8ubGVuZ3RoID4gMCkge1xuICAgICAgICBzZWN0aW9ucy5wdXNoKFwiKirkuLvopoHpo47pmakqKjpcIilcbiAgICAgICAgcmlza19hc3Nlc3NtZW50LnRlY2huaWNhbF9yaXNrcy5mb3JFYWNoKChyaXNrOiBhbnkpID0+IHtcbiAgICAgICAgICBpZiAodHlwZW9mIHJpc2sgPT09ICdvYmplY3QnKSB7XG4gICAgICAgICAgICBzZWN0aW9ucy5wdXNoKGDigKIgJHtyaXNrLnJpc2t9ICgke3Jpc2sucHJvYmFiaWxpdHl9KWApXG4gICAgICAgICAgICBzZWN0aW9ucy5wdXNoKGAgIOW9seWTjTogJHtyaXNrLmltcGFjdH1gKVxuICAgICAgICAgICAgc2VjdGlvbnMucHVzaChgICDnvJPop6M6ICR7cmlzay5taXRpZ2F0aW9ufWApXG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHNlY3Rpb25zLnB1c2goYOKAoiAke3Jpc2t9YClcbiAgICAgICAgICB9XG4gICAgICAgIH0pXG4gICAgICB9XG4gICAgICAvLyDml6fmoLzlvI/po47pmalcbiAgICAgIGVsc2UgaWYgKHJpc2tfYXNzZXNzbWVudC5pZGVudGlmaWVkX3Jpc2tzPy5sZW5ndGggPiAwKSB7XG4gICAgICAgIHNlY3Rpb25zLnB1c2goXCIqKuS4u+imgemjjumZqSoqOlwiKVxuICAgICAgICByaXNrX2Fzc2Vzc21lbnQuaWRlbnRpZmllZF9yaXNrcy5mb3JFYWNoKChyaXNrOiBzdHJpbmcpID0+IHtcbiAgICAgICAgICBzZWN0aW9ucy5wdXNoKGDigKIgJHtyaXNrfWApXG4gICAgICAgIH0pXG4gICAgICB9XG4gICAgICBzZWN0aW9ucy5wdXNoKFwiXCIpXG4gICAgfVxuXG5cblxuICAgIC8vIOa3u+WKoOaZuuiDveaOqOiNkOeJueaAp++8iOWmguaenOaYr+aZuuiDveaOqOiNkO+8iVxuICAgIGlmIChpbnRlbGxpZ2VudF9mZWF0dXJlcykge1xuICAgICAgLy8g5pm66IO95paH54yu5o6o6I2QIC0g5L+u5aSN5pWw5o2u57uT5p6E6K6/6ZeuXG4gICAgICBpZiAoaW50ZWxsaWdlbnRfZmVhdHVyZXMuaG90X3BhcGVycz8ubGVuZ3RoID4gMCkge1xuICAgICAgICBzZWN0aW9ucy5wdXNoKFwi8J+nrCAqKuaZuuiDveaWh+eMruaOqOiNkOaKpeWRiioqXCIpXG4gICAgICAgIHNlY3Rpb25zLnB1c2goXCJcIilcbiAgICAgICAgc2VjdGlvbnMucHVzaChcIvCflKUgKirng63ngrnmlofnjK7lj5HnjrAqKjpcIilcbiAgICAgICAgaW50ZWxsaWdlbnRfZmVhdHVyZXMuaG90X3BhcGVycy5mb3JFYWNoKChwYXBlcjogYW55LCBpbmRleDogbnVtYmVyKSA9PiB7XG4gICAgICAgICAgY29uc3QgcHVibWVkTGluayA9IHBhcGVyLmRvaSBcbiAgICAgICAgICAgID8gYGh0dHBzOi8vcHVibWVkLm5jYmkubmxtLm5paC5nb3YvP3Rlcm09JHtlbmNvZGVVUklDb21wb25lbnQocGFwZXIuZG9pKX1bRE9JXWBcbiAgICAgICAgICAgIDogYGh0dHBzOi8vcHVibWVkLm5jYmkubmxtLm5paC5nb3YvP3Rlcm09JHtlbmNvZGVVUklDb21wb25lbnQocGFwZXIudGl0bGUpfWBcbiAgICAgICAgICBjb25zdCBzY2hvbGFyTGluayA9IGBodHRwczovL3NjaG9sYXIuZ29vZ2xlLmNvbS9zY2hvbGFyP3E9JHtlbmNvZGVVUklDb21wb25lbnQocGFwZXIudGl0bGUpfWBcbiAgICAgICAgICBcbiAgICAgICAgICBzZWN0aW9ucy5wdXNoKGAqKlske2luZGV4ICsgMX1dICR7cGFwZXIudGl0bGV9KipgKVxuICAgICAgICAgIHNlY3Rpb25zLnB1c2goYPCfk5YgJHtwYXBlci5qb3VybmFsfSAke3BhcGVyLmltcGFjdF9mYWN0b3IgPyBgKElGOiAke3BhcGVyLmltcGFjdF9mYWN0b3J9KWAgOiAnJ31gKVxuICAgICAgICAgIHNlY3Rpb25zLnB1c2goYPCfkqEgJHtwYXBlci5yZWFzb259YClcbiAgICAgICAgICBzZWN0aW9ucy5wdXNoKGDwn5SXIFtQdWJNZWRdKCR7cHVibWVkTGlua30pIHwgW0dvb2dsZSBTY2hvbGFyXSgke3NjaG9sYXJMaW5rfSlgKVxuICAgICAgICAgIHNlY3Rpb25zLnB1c2goXCJcIilcbiAgICAgICAgfSlcbiAgICAgIH1cbiAgICAgIFxuICAgICAgLy8g5pm66IO95YWz6ZSu6K+N5omp5bGVIC0g5L+u5aSN5pWw5o2u57uT5p6E6K6/6Zeu5bm25re75Yqg5pCc57Si6ZO+5o6lXG4gICAgICBpZiAoaW50ZWxsaWdlbnRfZmVhdHVyZXMuZXhwYW5kZWRfa2V5d29yZHMpIHtcbiAgICAgICAgY29uc3Qga2V5d29yZHMgPSBpbnRlbGxpZ2VudF9mZWF0dXJlcy5leHBhbmRlZF9rZXl3b3Jkc1xuICAgICAgICBcbiAgICAgICAgaWYgKGtleXdvcmRzLnNlbWFudGljX2V4cGFuc2lvbj8ubGVuZ3RoID4gMCB8fCBrZXl3b3Jkcy50cmVuZGluZ190ZXJtcz8ubGVuZ3RoID4gMCkge1xuICAgICAgICAgIHNlY3Rpb25zLnB1c2goXCLwn5SNICoq5pm66IO95YWz6ZSu6K+N5omp5bGV5LiO5pCc57Si6ZO+5o6lKio6XCIpXG4gICAgICAgICAgc2VjdGlvbnMucHVzaChcIlwiKVxuICAgICAgICAgIFxuICAgICAgICAgIGlmIChrZXl3b3Jkcy5zZW1hbnRpY19leHBhbnNpb24/Lmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgIHNlY3Rpb25zLnB1c2goXCIqKuivreS5ieaJqeWxleWFs+mUruivjSoqOlwiKVxuICAgICAgICAgICAga2V5d29yZHMuc2VtYW50aWNfZXhwYW5zaW9uLnNsaWNlKDAsIDUpLmZvckVhY2goKGtleXdvcmQ6IHN0cmluZykgPT4ge1xuICAgICAgICAgICAgICBjb25zdCBwdWJtZWRMaW5rID0gYGh0dHBzOi8vcHVibWVkLm5jYmkubmxtLm5paC5nb3YvP3Rlcm09JHtlbmNvZGVVUklDb21wb25lbnQoa2V5d29yZCl9YFxuICAgICAgICAgICAgICBjb25zdCBzY2hvbGFyTGluayA9IGBodHRwczovL3NjaG9sYXIuZ29vZ2xlLmNvbS9zY2hvbGFyP3E9JHtlbmNvZGVVUklDb21wb25lbnQoa2V5d29yZCl9YFxuICAgICAgICAgICAgICBzZWN0aW9ucy5wdXNoKGDigKIgJHtrZXl3b3JkfSAtIFtQdWJNZWRdKCR7cHVibWVkTGlua30pIHwgW1NjaG9sYXJdKCR7c2Nob2xhckxpbmt9KWApXG4gICAgICAgICAgICB9KVxuICAgICAgICAgICAgc2VjdGlvbnMucHVzaChcIlwiKVxuICAgICAgICAgIH1cbiAgICAgICAgICBcbiAgICAgICAgICBpZiAoa2V5d29yZHMudHJlbmRpbmdfdGVybXM/Lmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgIHNlY3Rpb25zLnB1c2goXCIqKueDreeCuei2i+WKv+WFs+mUruivjSoqOlwiKVxuICAgICAgICAgICAga2V5d29yZHMudHJlbmRpbmdfdGVybXMuc2xpY2UoMCwgNSkuZm9yRWFjaCgoa2V5d29yZDogc3RyaW5nKSA9PiB7XG4gICAgICAgICAgICAgIGNvbnN0IHB1Ym1lZExpbmsgPSBgaHR0cHM6Ly9wdWJtZWQubmNiaS5ubG0ubmloLmdvdi8/dGVybT0ke2VuY29kZVVSSUNvbXBvbmVudChrZXl3b3JkKX1gXG4gICAgICAgICAgICAgIGNvbnN0IHNjaG9sYXJMaW5rID0gYGh0dHBzOi8vc2Nob2xhci5nb29nbGUuY29tL3NjaG9sYXI/cT0ke2VuY29kZVVSSUNvbXBvbmVudChrZXl3b3JkKX1gXG4gICAgICAgICAgICAgIHNlY3Rpb25zLnB1c2goYOKAoiAke2tleXdvcmR9IC0gW1B1Yk1lZF0oJHtwdWJtZWRMaW5rfSkgfCBbU2Nob2xhcl0oJHtzY2hvbGFyTGlua30pYClcbiAgICAgICAgICAgIH0pXG4gICAgICAgICAgICBzZWN0aW9ucy5wdXNoKFwiXCIpXG4gICAgICAgICAgfVxuICAgICAgICAgIFxuICAgICAgICAgIGlmIChrZXl3b3Jkcy5tb2xlY3VsYXJfdGFyZ2V0cz8ubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgc2VjdGlvbnMucHVzaChcIioq5YiG5a2Q6Z2254K55YWz6ZSu6K+NKio6XCIpXG4gICAgICAgICAgICBrZXl3b3Jkcy5tb2xlY3VsYXJfdGFyZ2V0cy5zbGljZSgwLCAzKS5mb3JFYWNoKChrZXl3b3JkOiBzdHJpbmcpID0+IHtcbiAgICAgICAgICAgICAgY29uc3QgcHVibWVkTGluayA9IGBodHRwczovL3B1Ym1lZC5uY2JpLm5sbS5uaWguZ292Lz90ZXJtPSR7ZW5jb2RlVVJJQ29tcG9uZW50KGtleXdvcmQpfWBcbiAgICAgICAgICAgICAgc2VjdGlvbnMucHVzaChg4oCiICR7a2V5d29yZH0gLSBbUHViTWVkXSgke3B1Ym1lZExpbmt9KWApXG4gICAgICAgICAgICB9KVxuICAgICAgICAgICAgc2VjdGlvbnMucHVzaChcIlwiKVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgXG4gICAgICAvLyDlpJbpg6jmlofnjK7mkJzntKLnu5PmnpxcbiAgICAgIGlmIChpbnRlbGxpZ2VudF9mZWF0dXJlcy5saXRlcmF0dXJlX3JlY29tbWVuZGF0aW9ucz8uY29tYmluZWRfcmVzdWx0cz8ubGVuZ3RoID4gMCkge1xuICAgICAgICBzZWN0aW9ucy5wdXNoKFwi8J+TmiAqKuebuOWFs+aWh+eMruaQnOe0oue7k+aenCoqOlwiKVxuICAgICAgICBzZWN0aW9ucy5wdXNoKFwiXCIpXG4gICAgICAgIGludGVsbGlnZW50X2ZlYXR1cmVzLmxpdGVyYXR1cmVfcmVjb21tZW5kYXRpb25zLmNvbWJpbmVkX3Jlc3VsdHMuc2xpY2UoMCwgMykuZm9yRWFjaCgocGFwZXI6IGFueSwgaW5kZXg6IG51bWJlcikgPT4ge1xuICAgICAgICAgIGNvbnN0IHB1Ym1lZExpbmsgPSBwYXBlci5wdWJtZWRfaWQgXG4gICAgICAgICAgICA/IGBodHRwczovL3B1Ym1lZC5uY2JpLm5sbS5uaWguZ292LyR7cGFwZXIucHVibWVkX2lkfS9gXG4gICAgICAgICAgICA6IHBhcGVyLmRvaSBcbiAgICAgICAgICAgID8gYGh0dHBzOi8vcHVibWVkLm5jYmkubmxtLm5paC5nb3YvP3Rlcm09JHtlbmNvZGVVUklDb21wb25lbnQocGFwZXIuZG9pKX1bRE9JXWBcbiAgICAgICAgICAgIDogYGh0dHBzOi8vcHVibWVkLm5jYmkubmxtLm5paC5nb3YvP3Rlcm09JHtlbmNvZGVVUklDb21wb25lbnQocGFwZXIudGl0bGUpfWBcbiAgICAgICAgICBcbiAgICAgICAgICBzZWN0aW9ucy5wdXNoKGAqKlske2luZGV4ICsgMX1dICR7cGFwZXIudGl0bGV9KipgKVxuICAgICAgICAgIHNlY3Rpb25zLnB1c2goYPCfk5YgJHtwYXBlci5qb3VybmFsfSAke3BhcGVyLnB1YmxpY2F0aW9uX3llYXIgPyBgKCR7cGFwZXIucHVibGljYXRpb25feWVhcn0pYCA6ICcnfWApXG4gICAgICAgICAgaWYgKHBhcGVyLmF1dGhvcnM/Lmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgIHNlY3Rpb25zLnB1c2goYPCfkaUgJHtwYXBlci5hdXRob3JzLnNsaWNlKDAsIDMpLmpvaW4oJywgJyl9JHtwYXBlci5hdXRob3JzLmxlbmd0aCA+IDMgPyAnIGV0IGFsLicgOiAnJ31gKVxuICAgICAgICAgIH1cbiAgICAgICAgICBpZiAocGFwZXIuYWJzdHJhY3QpIHtcbiAgICAgICAgICAgIHNlY3Rpb25zLnB1c2goYPCfk50gJHtwYXBlci5hYnN0cmFjdC5zdWJzdHJpbmcoMCwgMjAwKX0uLi5gKVxuICAgICAgICAgIH1cbiAgICAgICAgICBzZWN0aW9ucy5wdXNoKGDwn5SXIFvmn6XnnIvljp/mloddKCR7cHVibWVkTGlua30pIHwg5p2l5rqQOiAke3BhcGVyLnNvdXJjZSB8fCAnVW5rbm93bid9YClcbiAgICAgICAgICBzZWN0aW9ucy5wdXNoKFwiXCIpXG4gICAgICAgIH0pXG4gICAgICB9XG4gICAgICBcbiAgICAgIC8vIEFJ5pm66IO95rSe5a+fXG4gICAgICBpZiAoaW50ZWxsaWdlbnRfZmVhdHVyZXMuc21hcnRfaW5zaWdodHM/Lmxlbmd0aCA+IDApIHtcbiAgICAgICAgc2VjdGlvbnMucHVzaChcIvCfkqEgKipBSeaZuuiDvea0nuWvnyoqXCIpXG4gICAgICAgIGludGVsbGlnZW50X2ZlYXR1cmVzLnNtYXJ0X2luc2lnaHRzLmZvckVhY2goKGluc2lnaHQ6IHN0cmluZykgPT4ge1xuICAgICAgICAgIHNlY3Rpb25zLnB1c2goYOKAoiAke2luc2lnaHR9YClcbiAgICAgICAgfSlcbiAgICAgICAgc2VjdGlvbnMucHVzaChcIlwiKVxuICAgICAgfVxuICAgICAgXG4gICAgICAvLyDkuJPlrrblkIjkvZzmnLrkvJrmjqjojZBcbiAgICAgIGlmIChpbnRlbGxpZ2VudF9mZWF0dXJlcy5jb2xsYWJvcmF0aW9uX29wcG9ydHVuaXRpZXM/Lmxlbmd0aCA+IDApIHtcbiAgICAgICAgc2VjdGlvbnMucHVzaChcIvCfpJ0gKirkuJPlrrblkIjkvZzmnLrkvJrmjqjojZAqKlwiKVxuICAgICAgICBpbnRlbGxpZ2VudF9mZWF0dXJlcy5jb2xsYWJvcmF0aW9uX29wcG9ydHVuaXRpZXMuZm9yRWFjaCgoY29sbGFiOiBhbnkpID0+IHtcbiAgICAgICAgICBzZWN0aW9ucy5wdXNoKGDigKIgKioke2NvbGxhYi5pbnN0aXR1dGlvbn0qKiAtICR7Y29sbGFiLmV4cGVydH1gKVxuICAgICAgICAgIHNlY3Rpb25zLnB1c2goYCAgJHtjb2xsYWIuY29sbGFib3JhdGlvbl90eXBlfWApXG4gICAgICAgIH0pXG4gICAgICAgIHNlY3Rpb25zLnB1c2goXCJcIilcbiAgICAgIH1cbiAgICAgIFxuICAgICAgLy8g5pm66IO95paH54yu5qOA57Si5bu66K6uXG4gICAgICBpZiAoaW50ZWxsaWdlbnRfZmVhdHVyZXMuc2VhcmNoX3N1Z2dlc3Rpb25zKSB7XG4gICAgICAgIHNlY3Rpb25zLnB1c2goXCLwn5SNICoq5pm66IO95paH54yu5qOA57Si5bu66K6uKipcIilcbiAgICAgICAgc2VjdGlvbnMucHVzaChcIlwiKVxuICAgICAgICBcbiAgICAgICAgc2VjdGlvbnMucHVzaChcIvCfk4wgKirmjqjojZDmo4DntKLlhbPplK7or40qKjpcIilcbiAgICAgICAgaW50ZWxsaWdlbnRfZmVhdHVyZXMuc2VhcmNoX3N1Z2dlc3Rpb25zLnByaW1hcnlLZXl3b3Jkcy5mb3JFYWNoKChrZXl3b3JkOiBzdHJpbmcsIGluZGV4OiBudW1iZXIpID0+IHtcbiAgICAgICAgICBzZWN0aW9ucy5wdXNoKGAke2luZGV4ICsgMX0uICoqJHtrZXl3b3JkfSoqYClcbiAgICAgICAgICBcbiAgICAgICAgICAvLyDkuLrmr4/kuKrkuLvopoHlhbPplK7or43mt7vliqDmkJzntKLpk77mjqVcbiAgICAgICAgICBjb25zdCBsaW5rcyA9IGludGVsbGlnZW50X2ZlYXR1cmVzLnNlYXJjaF9zdWdnZXN0aW9ucy5zZWFyY2hMaW5rc1trZXl3b3JkXVxuICAgICAgICAgIGlmIChsaW5rcyAmJiBsaW5rcy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICBjb25zdCBsaW5rVGV4dHMgPSBsaW5rcy5tYXAoKGxpbms6IGFueSkgPT4gXG4gICAgICAgICAgICAgIGBbJHtsaW5rLmVtb2ppfSAke2xpbmsubmFtZX1dKCR7bGluay51cmx9KWBcbiAgICAgICAgICAgICkuam9pbignIOKAoiAnKVxuICAgICAgICAgICAgc2VjdGlvbnMucHVzaChgICAgJHtsaW5rVGV4dHN9YClcbiAgICAgICAgICB9XG4gICAgICAgICAgc2VjdGlvbnMucHVzaChcIlwiKVxuICAgICAgICB9KVxuICAgICAgICBcbiAgICAgICAgaWYgKGludGVsbGlnZW50X2ZlYXR1cmVzLnNlYXJjaF9zdWdnZXN0aW9ucy5zZWNvbmRhcnlLZXl3b3Jkcy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgc2VjdGlvbnMucHVzaChcIvCflJYgKirmianlsZXmo4DntKLlhbPplK7or40qKjpcIilcbiAgICAgICAgICBpbnRlbGxpZ2VudF9mZWF0dXJlcy5zZWFyY2hfc3VnZ2VzdGlvbnMuc2Vjb25kYXJ5S2V5d29yZHMuc2xpY2UoMCwgNCkuZm9yRWFjaCgoa2V5d29yZDogc3RyaW5nKSA9PiB7XG4gICAgICAgICAgICBzZWN0aW9ucy5wdXNoKGDigKIgJHtrZXl3b3JkfWApXG4gICAgICAgICAgfSlcbiAgICAgICAgICBzZWN0aW9ucy5wdXNoKFwiXCIpXG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIHNlY3Rpb25zLnB1c2goXCLwn5KhICoq5qOA57Si5bu66K6uKio6XCIpXG4gICAgICAgIHNlY3Rpb25zLnB1c2goXCLigKIg5bu66K6u5YWI55So5Li76KaB5YWz6ZSu6K+N5ZyoUHViTWVk5Lit5qOA57Si5pyA5paw5paH54yuXCIpXG4gICAgICAgIHNlY3Rpb25zLnB1c2goXCLigKIg5L2/55SoR29vZ2xlIFNjaG9sYXLmn6Xmib7lvJXnlKjmrKHmlbDpq5jnmoTnu4/lhbjmlofnjK5cIikgIFxuICAgICAgICBzZWN0aW9ucy5wdXNoKFwi4oCiIFNlbWFudGljIFNjaG9sYXLlj6/lj5HnjrDnm7jlhbPnmoTnu7zov7Dmlofnq6BcIilcbiAgICAgICAgc2VjdGlvbnMucHVzaChcIuKAoiDnu4TlkIjlhbPplK7or43lj6/ojrflvpfmm7Tnsr7lh4bnmoTmkJzntKLnu5PmnpxcIilcbiAgICAgICAgc2VjdGlvbnMucHVzaChcIlwiKVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIOa3u+WKoOaWh+eMruaQnOe0oue7k+aenO+8iOWmguaenOWQr+eUqOS6huaWh+eMruaQnOe0ou+8iVxuICAgIGlmIChsaXRlcmF0dXJlUmVzdWx0cyAmJiBsaXRlcmF0dXJlUmVzdWx0cy5saXRlcmF0dXJlX3Jlc3VsdHM/Lmxlbmd0aCA+IDApIHtcbiAgICAgIHNlY3Rpb25zLnB1c2goXCJcIilcbiAgICAgIHNlY3Rpb25zLnB1c2goXCLwn5OaICoq55u45YWz5paH54yu5pSv5pKRKipcIilcbiAgICAgIHNlY3Rpb25zLnB1c2goXCJcIilcbiAgICAgIFxuICAgICAgLy8g5pi+56S65pCc57Si5Yiw55qE5paH54yu77yI5pyA5aSa5pi+56S6M+evh++8iVxuICAgICAgY29uc3QgcGFwZXJzVG9TaG93ID0gbGl0ZXJhdHVyZVJlc3VsdHMubGl0ZXJhdHVyZV9yZXN1bHRzLnNsaWNlKDAsIDMpXG4gICAgICBwYXBlcnNUb1Nob3cuZm9yRWFjaCgocGFwZXI6IGFueSwgaW5kZXg6IG51bWJlcikgPT4ge1xuICAgICAgICBzZWN0aW9ucy5wdXNoKGAqKlske2luZGV4ICsgMX1dICR7cGFwZXIudGl0bGUgfHwgJ+aWh+eMruagh+mimCd9KipgKVxuICAgICAgICBcbiAgICAgICAgaWYgKHBhcGVyLmF1dGhvcnMgJiYgcGFwZXIuYXV0aG9ycy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgY29uc3QgYXV0aG9yTGlzdCA9IHBhcGVyLmF1dGhvcnMuc2xpY2UoMCwgMykuam9pbihcIiwgXCIpXG4gICAgICAgICAgY29uc3QgbW9yZUF1dGhvcnMgPSBwYXBlci5hdXRob3JzLmxlbmd0aCA+IDMgPyBcIiDnrYlcIiA6IFwiXCJcbiAgICAgICAgICBzZWN0aW9ucy5wdXNoKGAqJHthdXRob3JMaXN0fSR7bW9yZUF1dGhvcnN9KmApXG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIGlmIChwYXBlci5qb3VybmFsKSB7XG4gICAgICAgICAgc2VjdGlvbnMucHVzaChg8J+TliAke3BhcGVyLmpvdXJuYWx9JHtwYXBlci5wdWJsaWNhdGlvbl95ZWFyID8gYCAoJHtwYXBlci5wdWJsaWNhdGlvbl95ZWFyfSlgIDogJyd9YClcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgaWYgKHBhcGVyLmtleV9maW5kaW5ncykge1xuICAgICAgICAgIHNlY3Rpb25zLnB1c2goYPCfkqEgKirmoLjlv4Plj5HnjrAqKjogJHtwYXBlci5rZXlfZmluZGluZ3N9YClcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgaWYgKHBhcGVyLm1ldGhvZG9sb2d5X3N1bW1hcnkpIHtcbiAgICAgICAgICBzZWN0aW9ucy5wdXNoKGDwn5SsICoq5pa55rOVKio6ICR7cGFwZXIubWV0aG9kb2xvZ3lfc3VtbWFyeX1gKVxuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICBzZWN0aW9ucy5wdXNoKFwiXCIpXG4gICAgICB9KVxuICAgICAgXG4gICAgICAvLyDmmL7npLrmkJzntKLnu5/orqHkv6Hmga9cbiAgICAgIGlmIChsaXRlcmF0dXJlUmVzdWx0cy50b3RhbF9wYXBlcnMgPiAzKSB7XG4gICAgICAgIHNlY3Rpb25zLnB1c2goYPCfk4ogKirmkJzntKLnu5/orqEqKjog5YWx5om+5YiwICR7bGl0ZXJhdHVyZVJlc3VsdHMudG90YWxfcGFwZXJzfSDnr4fnm7jlhbPmlofnjK7vvIzlt7LmmL7npLrmnIDnm7jlhbPnmoQgJHtwYXBlcnNUb1Nob3cubGVuZ3RofSDnr4dgKVxuICAgICAgICBzZWN0aW9ucy5wdXNoKFwiXCIpXG4gICAgICB9XG4gICAgICBcbiAgICAgIC8vIOaYvuekuuaVsOaNruadpea6kFxuICAgICAgaWYgKGxpdGVyYXR1cmVSZXN1bHRzLnNvdXJjZXMgJiYgbGl0ZXJhdHVyZVJlc3VsdHMuc291cmNlcy5sZW5ndGggPiAwKSB7XG4gICAgICAgIHNlY3Rpb25zLnB1c2goYPCflI0gKirmlbDmja7mnaXmupAqKjogJHtsaXRlcmF0dXJlUmVzdWx0cy5zb3VyY2VzLmpvaW4oXCIsIFwiKX1gKVxuICAgICAgICBzZWN0aW9ucy5wdXNoKFwiXCIpXG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8g5aSE55CG57u85ZCI5pa55qGI5qGG5p6277yI5oyJ55So5oi36KaB5rGC55qE5paw57uT5p6E77yJXG4gICAgY29uc3QgeyBjb21wcmVoZW5zaXZlX2ZyYW1ld29yaywgZnJhbWV3b3JrX2ZlYXR1cmVzIH0gPSBzb2x1dGlvblJlc3BvbnNlXG4gICAgaWYgKGNvbXByZWhlbnNpdmVfZnJhbWV3b3JrICYmIGZyYW1ld29ya19mZWF0dXJlcz8uY29tcHJlaGVuc2l2ZV9mcmFtZXdvcmspIHtcbiAgICAgIHNlY3Rpb25zLnB1c2goXCJcIilcbiAgICAgIHNlY3Rpb25zLnB1c2goXCLwn46vICoq5Y2V57uG6IOe5rWL5bqP6Kej5Yaz5pa55qGIKipcIilcbiAgICAgIHNlY3Rpb25zLnB1c2goXCJcIilcblxuICAgICAgLy8g5re75Yqg566A5YyW5pa55qGI55qESlNPTuaVsOaNruS+m+WJjeerr+e7hOS7tuino+aekFxuICAgICAgc2VjdGlvbnMucHVzaChcImBgYGpzb25cIilcbiAgICAgIHNlY3Rpb25zLnB1c2goSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICB0eXBlOiBcInN0cmVhbWxpbmVkX3NvbHV0aW9uXCIsXG4gICAgICAgIGRhdGE6IGNvbXByZWhlbnNpdmVfZnJhbWV3b3JrXG4gICAgICB9LCBudWxsLCAyKSlcbiAgICAgIHNlY3Rpb25zLnB1c2goXCJgYGBcIilcbiAgICAgIHNlY3Rpb25zLnB1c2goXCJcIilcbiAgICB9XG5cbiAgICAvLyDlpITnkIbnu7zlkIjop6PlhrPmlrnmoYjvvIg15Liq5qC45b+D5Yqf6IO977yJLSDkvZzkuLrpmY3nuqfmlrnmoYhcbiAgICBjb25zdCB7IGNvbXByZWhlbnNpdmVfc29sdXRpb24sIGVuaGFuY2VkX2ZlYXR1cmVzIH0gPSBzb2x1dGlvblJlc3BvbnNlXG4gICAgaWYgKGNvbXByZWhlbnNpdmVfc29sdXRpb24gJiYgZW5oYW5jZWRfZmVhdHVyZXM/LmZpdmVfY29yZV9mdW5jdGlvbnMpIHtcbiAgICAgIHNlY3Rpb25zLnB1c2goXCJcIilcbiAgICAgIHNlY3Rpb25zLnB1c2goXCLwn46vICoqQ2VsbEZvcmdlIEFJIDXlpKfmoLjlv4Plip/og73nu7zlkIjliIbmnpAqKlwiKVxuICAgICAgc2VjdGlvbnMucHVzaChcIlwiKVxuXG4gICAgICAvLyDmt7vliqDnu7zlkIjop6PlhrPmlrnmoYjnmoRKU09O5pWw5o2u5L6b5YmN56uv57uE5Lu26Kej5p6QXG4gICAgICBzZWN0aW9ucy5wdXNoKFwiYGBganNvblwiKVxuICAgICAgc2VjdGlvbnMucHVzaChKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgIHR5cGU6IFwiY29tcHJlaGVuc2l2ZV9zb2x1dGlvblwiLFxuICAgICAgICBkYXRhOiBjb21wcmVoZW5zaXZlX3NvbHV0aW9uXG4gICAgICB9LCBudWxsLCAyKSlcbiAgICAgIHNlY3Rpb25zLnB1c2goXCJgYGBcIilcblxuICAgICAgc2VjdGlvbnMucHVzaChcIlwiKVxuICAgICAgc2VjdGlvbnMucHVzaChcIvCfkqEgKirlip/og73or7TmmI4qKjpcIilcbiAgICAgIHNlY3Rpb25zLnB1c2goXCLigKIgKirkuKrmgKfljJbmlrnmoYgqKjog5Z+65LqO5oKo55qE6ZyA5rGC5a6a5Yi255qE5LiT5Lia5oqA5pyv5pa55qGIXCIpXG4gICAgICBzZWN0aW9ucy5wdXNoKFwi4oCiICoq5paH54yu5o6o6I2QKio6IOmihuWfn+ebuOWFs+eahOmrmOi0qOmHj+Wtpuacr+aWh+eMrlwiKVxuICAgICAgc2VjdGlvbnMucHVzaChcIuKAoiAqKuaQnOe0ouWFs+mUruivjSoqOiDkvJjljJbnmoTlrabmnK/mo4DntKLlhbPplK7or43nu4TlkIhcIilcbiAgICAgIHNlY3Rpb25zLnB1c2goXCLigKIgKirnl5vngrnliIbmnpAqKjog6K+l6aKG5Z+f5bi46KeB5oqA5pyv5oyR5oiY5Y+K6Kej5Yaz5pa55qGIXCIpXG4gICAgICBzZWN0aW9ucy5wdXNoKFwi4oCiICoq6aOO6Zmp6K+E5LywKio6IOmhueebruWunuaWvemjjumZqeivhOS8sOWPiue8k+ino+etlueVpVwiKVxuICAgICAgc2VjdGlvbnMucHVzaChcIlwiKVxuICAgIH1cblxuICAgIHNlY3Rpb25zLnB1c2goXCItLS1cIilcbiAgICBpZiAoaXNGYWxsYmFjaykge1xuICAgICAgc2VjdGlvbnMucHVzaChcIvCfp6wgKipDZWxsRm9yZ2UgQUkqKiAtIOaCqOeahOWNlee7huiDnua1i+W6j+S4k+S4muS8meS8tFwiKVxuICAgICAgc2VjdGlvbnMucHVzaChcIlwiKVxuICAgICAgc2VjdGlvbnMucHVzaChcIioq5pyN5Yqh54q25oCBKio6IOS4tOaXtuaWueahiCDigKIgKirkuJPlrrbmlK/mjIEqKjog5Lq65bel5YiG5p6Q5LitIOKAoiAqKumihOiuoeWTjeW6lCoqOiAy5bCP5pe25YaFXCIpXG4gICAgfSBlbHNlIGlmIChpc0FpQW5hbHl6ZWQpIHtcbiAgICAgIHNlY3Rpb25zLnB1c2goXCLwn6esICoqQ2VsbEZvcmdlIEFJKiogLSDmgqjnmoTmmbrog73ljZXnu4bog57mtYvluo/kuJPkuJrkvJnkvLRcIilcbiAgICAgIHNlY3Rpb25zLnB1c2goXCJcIilcbiAgICAgIGNvbnN0IGNvbmZpZGVuY2UgPSBhaV9hbmFseXNpc19mZWF0dXJlcz8uY29uZmlkZW5jZV9zY29yZSA/IE1hdGgucm91bmQoYWlfYW5hbHlzaXNfZmVhdHVyZXMuY29uZmlkZW5jZV9zY29yZSAqIDEwMCkgOiA5NVxuICAgICAgc2VjdGlvbnMucHVzaChgKipBSeWIhuaekOe9ruS/oeW6pioqOiAke2NvbmZpZGVuY2V9JSDigKIgKirmnaXmupAqKjogQUnmhI/lm77liIbmnpDlvJXmk44sIOS4k+S4muefpeivhuW6kywg5Liq5oCn5YyW566X5rOVYClcbiAgICB9IGVsc2UgaWYgKGlzSW50ZWxsaWdlbnRSZWNvbW1lbmRhdGlvbikge1xuICAgICAgc2VjdGlvbnMucHVzaChcIvCfp6wgKipDZWxsRm9yZ2UgQUkqKiAtIOaCqOeahOaZuuiDveWNlee7huiDnua1i+W6j+S4k+S4muS8meS8tFwiKVxuICAgICAgc2VjdGlvbnMucHVzaChcIlwiKVxuICAgICAgc2VjdGlvbnMucHVzaChcIioq572u5L+h5bqmKio6IDk1JSDigKIgKirmnaXmupAqKjogQUnmmbrog73mjqjojZDlvJXmk44sIOS4k+S4muefpeivhuW6kywg54Ot54K55paH54yu5Y+R546wXCIpXG4gICAgfSBlbHNlIHtcbiAgICAgIHNlY3Rpb25zLnB1c2goXCLwn6esICoqQ2VsbEZvcmdlIEFJKiogLSDmgqjnmoTljZXnu4bog57mtYvluo/kuJPkuJrkvJnkvLRcIilcbiAgICB9XG5cbiAgICByZXR1cm4gc2VjdGlvbnMuam9pbihcIlxcblwiKVxuICB9XG5cbiAgLy8g5Z+65LqO6ZyA5rGC55Sf5oiQQUnlu7rorq4gLSDkvb/nlKjlop7lvLrnmoTliqDovb3nirbmgIFcbiAgY29uc3QgZ2VuZXJhdGVSZXF1aXJlbWVudEJhc2VkU3VnZ2VzdGlvbiA9IGFzeW5jIChyZXFzOiBSZXF1aXJlbWVudERhdGEpID0+IHtcbiAgICBjb25zdCBzdWdnZXN0aW9uTWVzc2FnZSA9IGDln7rkuo7mgqjloavlhpnnmoTpnIDmsYLkv6Hmga/vvJpcbi0g56CU56m255uu5qCH77yaJHtyZXFzLnJlc2VhcmNoR29hbH1cbi0g5qC35pys57G75Z6L77yaJHtyZXFzLnNhbXBsZVR5cGV9XG4tIOe7huiDnuaVsOmHj++8miR7cmVxcy5jZWxsQ291bnR9XG4tIOmihOeul+iMg+WbtO+8miR7cmVxcy5idWRnZXR9XG4tIOmhueebruWRqOacn++8miR7cmVxcy50aW1lbGluZX1cblxu5oiR5p2l5Li65oKo55Sf5oiQ5LiT5Lia55qE5oqA5pyv5pa55qGI5bu66K6u44CCYFxuXG4gICAgdHJ5IHtcbiAgICAgIC8vIOS9v+eUqOWIhumYtuauteWKoOi9vVxuICAgICAgbGV0IGFwaVJlc3BvbnNlOiBhbnlcblxuICAgICAgYXdhaXQgZXJyb3JIYW5kbGVyLmhhbmRsZVN0YWdlZE9wZXJhdGlvbihbXG4gICAgICAgIHtcbiAgICAgICAgICBzdGFnZTogTG9hZGluZ1N0YWdlLkFOQUxZWklORyxcbiAgICAgICAgICBvcGVyYXRpb246IGFzeW5jICgpID0+IHtcbiAgICAgICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCA1MDApKVxuICAgICAgICAgICAgcmV0dXJuIHsgc3RlcDogXCJhbmFseXplZFwiIH1cbiAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICBzdGFnZTogTG9hZGluZ1N0YWdlLkdFTkVSQVRJTkdfUkVTUE9OU0UsXG4gICAgICAgICAgb3BlcmF0aW9uOiBhc3luYyAoKSA9PiB7XG4gICAgICAgICAgICBhcGlSZXNwb25zZSA9IGF3YWl0IGNvbnZlcnNhdGlvbkFwaS5zZW5kTWVzc2FnZSh7XG4gICAgICAgICAgICAgIG1lc3NhZ2U6IHN1Z2dlc3Rpb25NZXNzYWdlLFxuICAgICAgICAgICAgICBjb252ZXJzYXRpb25fdHlwZTogXCJyZXF1aXJlbWVudF9iYXNlZFwiLFxuICAgICAgICAgICAgICBjb250ZXh0OiB7XG4gICAgICAgICAgICAgICAgdXNlcl9wcm9maWxlOiB7XG4gICAgICAgICAgICAgICAgICBpZDogdXNlcj8uaWQsXG4gICAgICAgICAgICAgICAgICBvcmdhbml6YXRpb246IHVzZXI/Lm9yZ2FuaXphdGlvbiB8fCBcIlwiLFxuICAgICAgICAgICAgICAgICAgcm9sZTogdXNlcj8ucm9sZSB8fCBcIlwiXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICByZXF1aXJlbWVudHM6IHJlcXNcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIHJldHVybiBhcGlSZXNwb25zZVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgXSxcbiAgICAgIChzdGFnZSwgcHJvZ3Jlc3MpID0+IHtcbiAgICAgICAgaWYgKCFpc0xvYWRpbmcpIHtcbiAgICAgICAgICBzdGFydExvYWRpbmcoc3RhZ2UpXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgdXBkYXRlU3RhZ2Uoc3RhZ2UsIHByb2dyZXNzKVxuICAgICAgICB9XG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBtYXhSZXRyaWVzOiAxLFxuICAgICAgICByZXRyeURlbGF5OiAzMDAwLFxuICAgICAgICB1c2VyRnJpZW5kbHlNZXNzYWdlOiBcIueUn+aIkOW7uuiuruWksei0pe+8jOivt+eojeWQjumHjeivlVwiLFxuICAgICAgICBzaG93VG9hc3Q6IGZhbHNlXG4gICAgICB9KVxuXG4gICAgICAvLyBhcGlSZXNwb25zZeW3sue7j+WcqEdFTkVSQVRJTkdfUkVTUE9OU0XpmLbmrrXkuK3orr7nva5cblxuICAgICAgY29uc3QgYWlTdWdnZXN0aW9uOiBNZXNzYWdlID0ge1xuICAgICAgICBpZDogRGF0ZS5ub3coKS50b1N0cmluZygpLFxuICAgICAgICB0eXBlOiBcImFpXCIsXG4gICAgICAgIGNvbnRlbnQ6IGFwaVJlc3BvbnNlLm1lc3NhZ2UsXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSxcbiAgICAgICAgc3RhdHVzOiBcInJlYWRcIixcbiAgICAgICAgY29uZmlkZW5jZTogYXBpUmVzcG9uc2UuY29uZmlkZW5jZSxcbiAgICAgICAgc291cmNlczogYXBpUmVzcG9uc2Uuc291cmNlcyxcbiAgICAgICAgc3VnZ2VzdGlvbnM6IGFwaVJlc3BvbnNlLnN1Z2dlc3Rpb25zXG4gICAgICB9XG5cbiAgICAgIHNldE1lc3NhZ2VzKChwcmV2KSA9PiBbLi4ucHJldiwgYWlTdWdnZXN0aW9uXSlcbiAgICAgIGZpbmlzaExvYWRpbmcoKVxuICAgICAgdG9hc3Quc3VjY2VzcyhcIuW3suWfuuS6juaCqOeahOmcgOaxgueUn+aIkOS4k+S4muW7uuiuru+8gVwiKVxuXG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwi55Sf5oiQ5bu66K6u5aSx6LSlOlwiLCBlcnIpXG4gICAgICBjYW5jZWxMb2FkaW5nKClcbiAgICAgIC8vIOmUmeivr+W3sue7j+eUsWVycm9ySGFuZGxlcuWkhOeQhlxuICAgIH1cbiAgfVxuXG4gIC8vIOiOt+WPlue8uuWkseeahOWFs+mUrumcgOaxguWtl+autSAtIOabtOaWsOS7peWMuemFjeaWsOeahOW/hemcgOWtl+autVxuICBjb25zdCBnZXRNaXNzaW5nUmVxdWlyZW1lbnRzID0gKCkgPT4ge1xuICAgIGNvbnN0IHJlcXVpcmVkRmllbGRzID0gW1xuICAgICAgeyBrZXk6IFwic3BlY2llc1R5cGVcIiwgbGFiZWw6IFwi54mp56eN57G75Z6LXCIsIHF1ZXN0aW9uOiBcIuaCqOeglOeptueahOaYr+S7gOS5iOeJqeenje+8n1wiIH0sXG4gICAgICB7IGtleTogXCJleHBlcmltZW50VHlwZVwiLCBsYWJlbDogXCLlrp7pqoznsbvlnotcIiwgcXVlc3Rpb246IFwi5oKo6ZyA6KaB5LuA5LmI57G75Z6L55qE5Y2V57uG6IOe5a6e6aqM77yfXCIgfSxcbiAgICAgIHsga2V5OiBcInJlc2VhcmNoR29hbFwiLCBsYWJlbDogXCLnoJTnqbbnm67moIdcIiwgcXVlc3Rpb246IFwi5oKo55qE56CU56m255uu5qCH5piv5LuA5LmI77yfXCIgfSxcbiAgICAgIHsga2V5OiBcInNhbXBsZVR5cGVcIiwgbGFiZWw6IFwi5qC35pys57G75Z6LXCIsIHF1ZXN0aW9uOiBcIuaCqOS9v+eUqOS7gOS5iOexu+Wei+eahOagt+acrO+8n1wiIH0sXG4gICAgICB7IGtleTogXCJidWRnZXRcIiwgbGFiZWw6IFwi6aKE566X6IyD5Zu0XCIsIHF1ZXN0aW9uOiBcIuaCqOeahOmhueebrumihOeul+Wkp+amguaYr+WkmuWwke+8n1wiIH1cbiAgICBdXG5cbiAgICBpZiAoIXJlcXVpcmVtZW50cykgcmV0dXJuIHJlcXVpcmVkRmllbGRzXG5cbiAgICByZXR1cm4gcmVxdWlyZWRGaWVsZHMuZmlsdGVyKGZpZWxkID0+ICFyZXF1aXJlbWVudHNbZmllbGQua2V5IGFzIGtleW9mIFJlcXVpcmVtZW50RGF0YV0pXG4gIH1cblxuICAvLyDojrflj5bmmbrog73lu7rorq7vvIjlkIjlubZBSeW7uuiuruWSjOW/q+mAn+WbnuWkje+8iVxuICBjb25zdCBnZXRTbWFydFN1Z2dlc3Rpb25zID0gKCkgPT4ge1xuICAgIC8vIOS8mOWFiOaYvuekukFJ5Zue5aSN5Lit55qE5bu66K6uXG4gICAgY29uc3QgbGFzdE1lc3NhZ2UgPSBtZXNzYWdlc1ttZXNzYWdlcy5sZW5ndGggLSAxXVxuICAgIGlmIChsYXN0TWVzc2FnZT8udHlwZSA9PT0gXCJhaVwiICYmIGxhc3RNZXNzYWdlLnN1Z2dlc3Rpb25zICYmIGxhc3RNZXNzYWdlLnN1Z2dlc3Rpb25zLmxlbmd0aCA+IDApIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHR5cGU6IFwiYWlfc3VnZ2VzdGlvbnNcIixcbiAgICAgICAgdGl0bGU6IFwiQUnmmbrog73lu7rorq5cIixcbiAgICAgICAgaWNvbjogXCLinKhcIixcbiAgICAgICAgaXRlbXM6IGxhc3RNZXNzYWdlLnN1Z2dlc3Rpb25zLnNsaWNlKDAsIDQpLm1hcChzdWdnZXN0aW9uID0+ICh7XG4gICAgICAgICAgdGV4dDogc3VnZ2VzdGlvbixcbiAgICAgICAgICBpY29uOiBcIvCfkqFcIlxuICAgICAgICB9KSlcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDmo4Dmn6XmmK/lkKbmnInnvLrlpLHnmoTlhbPplK7pnIDmsYLkv6Hmga9cbiAgICBjb25zdCBtaXNzaW5nUmVxcyA9IGdldE1pc3NpbmdSZXF1aXJlbWVudHMoKVxuICAgIGlmIChtaXNzaW5nUmVxcy5sZW5ndGggPiAwICYmICghcmVxdWlyZW1lbnRzIHx8IHJlcXVpcmVtZW50cy5jb21wbGV0ZW5lc3MgPCA2MCkpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHR5cGU6IFwicmVxdWlyZW1lbnRfZnJhbWV3b3JrXCIsXG4gICAgICAgIHRpdGxlOiBcIuWujOWWhOmcgOaxguS/oeaBr1wiLFxuICAgICAgICBpY29uOiBcIvCfk4tcIixcbiAgICAgICAgc3VidGl0bGU6IGDov5jpnIDopoHloavlhpkgJHttaXNzaW5nUmVxcy5sZW5ndGh9IOmhueWFs+mUruS/oeaBr++8jOW7uuiuruWFiOWujOWWhOWQjuWGjeaPkOS6pGAsXG4gICAgICAgIGl0ZW1zOiBtaXNzaW5nUmVxcy5zbGljZSgwLCAzKS5tYXAocmVxID0+ICh7XG4gICAgICAgICAgdGV4dDogcmVxLnF1ZXN0aW9uLFxuICAgICAgICAgIGljb246IFwi4p2TXCIsXG4gICAgICAgICAgYWN0aW9uOiBcImZpbGxfcmVxdWlyZW1lbnRcIixcbiAgICAgICAgICBmaWVsZDogcmVxLmtleVxuICAgICAgICB9KSlcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDlkKbliJnmmL7npLrln7rkuo7pnIDmsYLlrozmiJDluqbnmoTlv6vpgJ/lvIDlp4vlu7rorq5cbiAgICBpZiAoIXJlcXVpcmVtZW50cyB8fCByZXF1aXJlbWVudHMuY29tcGxldGVuZXNzIDwgMjApIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHR5cGU6IFwicXVpY2tfc3RhcnRcIixcbiAgICAgICAgdGl0bGU6IFwi5b+r6YCf5byA5aeLXCIsXG4gICAgICAgIGljb246IFwi8J+SrFwiLFxuICAgICAgICBpdGVtczogW1xuICAgICAgICAgIHsgdGV4dDogXCLmiJHpnIDopoHljZXnu4bog55STkHmtYvluo/mlrnmoYhcIiwgaWNvbjogXCLwn6esXCIgfSxcbiAgICAgICAgICB7IHRleHQ6IFwi6K+35o6o6I2Q5oqA5pyv5bmz5Y+wXCIsIGljb246IFwi4pqhXCIgfSxcbiAgICAgICAgICB7IHRleHQ6IFwi5YiG5p6Q6aG555uu5oiQ5pysXCIsIGljb246IFwi8J+SsFwiIH0sXG4gICAgICAgICAgeyB0ZXh0OiBcIuafpeeci+aIkOWKn+ahiOS+i1wiLCBpY29uOiBcIvCfk4pcIiB9XG4gICAgICAgIF1cbiAgICAgIH1cbiAgICB9IGVsc2UgaWYgKHJlcXVpcmVtZW50cy5jb21wbGV0ZW5lc3MgPCA4MCkge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgdHlwZTogXCJjb250aW51ZV9jb252ZXJzYXRpb25cIixcbiAgICAgICAgdGl0bGU6IFwi57un57ut5a+56K+dXCIsXG4gICAgICAgIGljb246IFwi8J+UhFwiLFxuICAgICAgICBpdGVtczogW1xuICAgICAgICAgIHsgdGV4dDogXCLnu6fnu63lrozlloTpnIDmsYLkv6Hmga9cIiwgaWNvbjogXCLwn5OdXCIgfSxcbiAgICAgICAgICB7IHRleHQ6IFwi5Z+65LqO5b2T5YmN5L+h5oGv57uZ5bu66K6uXCIsIGljb246IFwi8J+SoVwiIH0sXG4gICAgICAgICAgeyB0ZXh0OiBcIuS6huino+aKgOacr+e7huiKglwiLCBpY29uOiBcIvCflKxcIiB9LFxuICAgICAgICAgIHsgdGV4dDogXCLpooTkvLDpobnnm67lkajmnJ9cIiwgaWNvbjogXCLij7HvuI9cIiB9XG4gICAgICAgIF1cbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgdHlwZTogXCJhZHZhbmNlZF9hY3Rpb25zXCIsXG4gICAgICAgIHRpdGxlOiBcIumrmOe6p+WKn+iDvVwiLFxuICAgICAgICBpY29uOiBcIvCfmoBcIixcbiAgICAgICAgaXRlbXM6IFtcbiAgICAgICAgICB7IHRleHQ6IFwi55Sf5oiQ5a6M5pW05oqA5pyv5pa55qGIXCIsIGljb246IFwi8J+Ti1wiIH0sXG4gICAgICAgICAgeyB0ZXh0OiBcIuS8mOWMluaIkOacrOmFjee9rlwiLCBpY29uOiBcIvCfkrBcIiB9LFxuICAgICAgICAgIHsgdGV4dDogXCLpo47pmanor4TkvLDliIbmnpBcIiwgaWNvbjogXCLimqDvuI9cIiB9LFxuICAgICAgICAgIHsgdGV4dDogXCLogZTns7vmioDmnK/kuJPlrrZcIiwgaWNvbjogXCLwn5Go4oCN8J+UrFwiIH1cbiAgICAgICAgXVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8vIOWkhOeQhuW7uuiurueCueWHu1xuICBjb25zdCBoYW5kbGVTdWdnZXN0aW9uQ2xpY2sgPSAoaXRlbTogYW55KSA9PiB7XG4gICAgaWYgKGl0ZW0uYWN0aW9uID09PSBcImZpbGxfcmVxdWlyZW1lbnRcIikge1xuICAgICAgLy8g5aaC5p6c5piv6ZyA5rGC5aGr5YaZ5bu66K6u77yM5pi+56S65Y+z5L6n6Z2i5p2/5bm26IGa54Sm5Yiw5a+55bqU5a2X5q61XG4gICAgICBzZXRSaWdodFBhbmVsQ29sbGFwc2VkKGZhbHNlKVxuICAgICAgLy8g5Y+v5Lul5re75Yqg5rua5Yqo5Yiw5a+55bqU5a2X5q6155qE6YC76L6RXG4gICAgICB0b2FzdC5pbmZvKGDor7flnKjlj7PkvqfpnaLmnb/loavlhpnvvJoke2l0ZW0udGV4dH1gKVxuICAgIH0gZWxzZSB7XG4gICAgICAvLyDmma7pgJrlu7rorq7nm7TmjqXorr7nva7liLDovpPlhaXmoYZcbiAgICAgIHNldElucHV0VmFsdWUoaXRlbS50ZXh0KVxuICAgIH1cbiAgfVxuXG4gIC8vIOWKoOi9veWOhuWPsuWvueivnVxuICBjb25zdCBoYW5kbGVMb2FkQ29udmVyc2F0aW9uID0gKHNlc3Npb246IGFueSkgPT4ge1xuICAgIHNldE1lc3NhZ2VzKHNlc3Npb24ubWVzc2FnZXMpXG4gICAgc2V0Q3VycmVudFNlc3Npb25JZChzZXNzaW9uLmlkKVxuICAgIHNldFNob3dIaXN0b3J5KGZhbHNlKVxuICAgIHRvYXN0LnN1Y2Nlc3MoYOW3suWKoOi9veWvueivnTogJHtzZXNzaW9uLnRpdGxlfWApXG4gIH1cblxuICAvLyDkv53lrZjlvZPliY3lr7nor51cbiAgY29uc3QgaGFuZGxlU2F2ZUN1cnJlbnRDb252ZXJzYXRpb24gPSAoKSA9PiB7XG4gICAgLy8g6L+Z5Liq5Ye95pWw5Lya5ZyoQ29udmVyc2F0aW9uSGlzdG9yeee7hOS7tuS4reWkhOeQhlxuICAgIHNldFNob3dIaXN0b3J5KGZhbHNlKVxuICB9XG5cbiAgLy8g5byA5aeL5paw5a+56K+dXG4gIGNvbnN0IGhhbmRsZU5ld0NvbnZlcnNhdGlvbiA9ICgpID0+IHtcbiAgICBzZXRNZXNzYWdlcyhbXSlcbiAgICBzZXRDdXJyZW50U2Vzc2lvbklkKG51bGwpXG4gICAgc2V0UmVxdWlyZW1lbnRzKG51bGwpXG4gICAgc2V0RXJyb3IoXCJcIilcbiAgICBzZXRTaG93SGlzdG9yeShmYWxzZSlcbiAgICBzZXRSaWdodFBhbmVsQ29sbGFwc2VkKGZhbHNlKSAvLyDmmL7npLrpnIDmsYLmlLbpm4bliqnmiYtcbiAgICBzZXRSZXNldFRyaWdnZXIocHJldiA9PiBwcmV2ICsgMSkgLy8g6Kem5Y+R6ZyA5rGC5pS26ZuG5Yqp5omL6YeN572uXG4gICAgdG9hc3Quc3VjY2VzcyhcIuW3suW8gOWni+aWsOWvueivne+8jOivt+WcqOWPs+S+p+Whq+WGmemhueebrumcgOaxglwiKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaC1mdWxsIGJnLXNsYXRlLTUwXCI+XG4gICAgICB7Lyog5bem5L6n5Li75a+56K+d5Yy65Z+fICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4IGZsZXgtY29sIGgtZnVsbCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgJHtcbiAgICAgICAgcmlnaHRQYW5lbENvbGxhcHNlZCA/ICdmbGV4LTEnIDogJ2ZsZXgtMSdcbiAgICAgIH1gfT5cbiAgICAgICAgey8qIOWvueivneWktOmDqCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBib3JkZXItYiBib3JkZXItc2xhdGUtMjAwIHB4LTQgcHktMyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTNcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwMCB0by1pbmRpZ28tNjAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxCcmFpbiBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LXNsYXRlLTkwMFwiPkNlbGxGb3JnZSBBSTwvaDI+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNsYXRlLTUwMFwiPuWNlee7huiDnua1i+W6j+aZuuiDvemhvumXrjwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIHtyZXF1aXJlbWVudHMgJiYgcmVxdWlyZW1lbnRzLmNvbXBsZXRlbmVzcyA+IDAgJiYgKFxuICAgICAgICAgICAgICA8QmFkZ2UgdmFyaWFudD1cInNlY29uZGFyeVwiIGNsYXNzTmFtZT1cImJnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDBcIj5cbiAgICAgICAgICAgICAgICDpnIDmsYLlrozmiJDluqYge3JlcXVpcmVtZW50cy5jb21wbGV0ZW5lc3N9JVxuICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgIHsvKiDljoblj7Llr7nor53mjInpkq4gKi99XG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dIaXN0b3J5KCFzaG93SGlzdG9yeSl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtc2xhdGUtNjAwIGhvdmVyOnRleHQtc2xhdGUtOTAwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPEhpc3RvcnkgY2xhc3NOYW1lPVwiaC00IHctNCBtci0xXCIgLz5cbiAgICAgICAgICAgICAg5Y6G5Y+yXG4gICAgICAgICAgICA8L0J1dHRvbj5cblxuICAgICAgICAgICAgey8qIOaWsOWvueivneaMiemSriAqL31cbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlTmV3Q29udmVyc2F0aW9ufVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXNsYXRlLTYwMCBob3Zlcjp0ZXh0LXNsYXRlLTkwMFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxSb3RhdGVDY3cgY2xhc3NOYW1lPVwiaC00IHctNCBtci0xXCIgLz5cbiAgICAgICAgICAgICAg5paw5a+56K+dXG4gICAgICAgICAgICA8L0J1dHRvbj5cblxuICAgICAgICAgICAgeyFpc01vYmlsZSAmJiAoXG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0UmlnaHRQYW5lbENvbGxhcHNlZCghcmlnaHRQYW5lbENvbGxhcHNlZCl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1zbGF0ZS02MDAgaG92ZXI6dGV4dC1zbGF0ZS05MDBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge3JpZ2h0UGFuZWxDb2xsYXBzZWQgPyAoXG4gICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICA8Q2hldnJvbkxlZnQgY2xhc3NOYW1lPVwiaC00IHctNCBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICAgICAg5pi+56S65Yqp5omLXG4gICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAg6ZqQ6JeP5Yqp5omLXG4gICAgICAgICAgICAgICAgICAgIDxDaGV2cm9uUmlnaHQgY2xhc3NOYW1lPVwiaC00IHctNCBtbC0xXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIOmUmeivr+aPkOekuiAqL31cbiAgICAgICAge2Vycm9yICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICA8QWxlcnQgdmFyaWFudD1cImRlc3RydWN0aXZlXCI+XG4gICAgICAgICAgICAgIDxBbGVydENpcmNsZSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgPEFsZXJ0RGVzY3JpcHRpb24+e2Vycm9yfTwvQWxlcnREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgIDwvQWxlcnQ+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIOa2iOaBr+WMuuWfn+WuueWZqCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLWgtMCByZWxhdGl2ZVwiPlxuICAgICAgICAgIHsvKiDmtojmga/ljLrln58gKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIG92ZXJmbG93LXktYXV0byBwLTQgc3BhY2UteS02IGJnLXdoaXRlXCI+XG4gICAgICAgICAgICB7bWVzc2FnZXMubWFwKChtZXNzYWdlKSA9PiAoXG4gICAgICAgICAgICAgIDxkaXYga2V5PXttZXNzYWdlLmlkfSBjbGFzc05hbWU9e2BmbGV4ICR7bWVzc2FnZS50eXBlID09PSBcInVzZXJcIiA/IFwianVzdGlmeS1lbmRcIiA6IFwianVzdGlmeS1zdGFydFwifWB9PlxuICAgICAgICAgICAgICAgIHttZXNzYWdlLnR5cGUgPT09IFwiYWlcIiAmJiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNTAwIHRvLWluZGlnby02MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG1yLTMgZmxleC1zaHJpbmstMCBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgIDxTcGFya2xlcyBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgJHtcbiAgICAgICAgICAgICAgICAgIG1lc3NhZ2UudHlwZSA9PT0gXCJ1c2VyXCJcbiAgICAgICAgICAgICAgICAgICAgPyBcIm1heC13LWxnIG1sLTEyXCJcbiAgICAgICAgICAgICAgICAgICAgOiBcIm1heC13LTR4bCBtci04XCIgIC8vIEdpdmUgQUkgbWVzc2FnZXMgbW9yZSBzcGFjZVxuICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgJHtcbiAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlLnR5cGUgPT09IFwidXNlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICA/IFwicHgtNCBweS0zIHJvdW5kZWQtMnhsIHNoYWRvdy1zbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICA6IFwicHgtNiBweS00IHJvdW5kZWQtMnhsIHNoYWRvdy1zbVwiICAvLyBNb3JlIHBhZGRpbmcgZm9yIEFJIG1lc3NhZ2VzXG4gICAgICAgICAgICAgICAgICAgIH0gJHtcbiAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlLnR5cGUgPT09IFwidXNlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICA/IG1lc3NhZ2Uuc3RhdHVzID09PSBcImVycm9yXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcImJnLXJlZC01MDAgdGV4dC13aGl0ZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS02MDAgdG8taW5kaWdvLTYwMCB0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogXCJiZy13aGl0ZSBib3JkZXIgYm9yZGVyLXNsYXRlLTIwMCB0ZXh0LXNsYXRlLTkwMFwiICAvLyBXaGl0ZSBiYWNrZ3JvdW5kIGZvciBBSVxuICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge21lc3NhZ2UudHlwZSA9PT0gXCJhaVwiID8gKFxuICAgICAgICAgICAgICAgICAgICAgIDxGb3JtYXR0ZWRNZXNzYWdlIGNvbnRlbnQ9e21lc3NhZ2UuY29udGVudH0gY2xhc3NOYW1lPVwidGV4dC1zbVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGxlYWRpbmctcmVsYXhlZCB3aGl0ZXNwYWNlLXByZS13cmFwXCI+e21lc3NhZ2UuY29udGVudH08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAgICB7LyogQUnmtojmga/nmoTpop3lpJbkv6Hmga8gKi99XG4gICAgICAgICAgICAgICAgICAgIHttZXNzYWdlLnR5cGUgPT09IFwiYWlcIiAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0zIHB0LTIgYm9yZGVyLXQgYm9yZGVyLXNsYXRlLTIwMCBzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHttZXNzYWdlLmNvbmZpZGVuY2UgIT09IHVuZGVmaW5lZCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8WmFwIGNsYXNzTmFtZT1cImgtMyB3LTMgdGV4dC1ibHVlLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNsYXRlLTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg572u5L+h5bqmOiB7TWF0aC5yb3VuZChtZXNzYWdlLmNvbmZpZGVuY2UgKiAxMDApfSVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHttZXNzYWdlLnNvdXJjZXMgJiYgbWVzc2FnZS5zb3VyY2VzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPE1lc3NhZ2VTcXVhcmUgY2xhc3NOYW1lPVwiaC0zIHctMyB0ZXh0LWdyZWVuLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNsYXRlLTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg5p2l5rqQOiB7bWVzc2FnZS5zb3VyY2VzLnNsaWNlKDAsIDIpLmpvaW4oXCIsIFwiKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHttZXNzYWdlLnNvdXJjZXMubGVuZ3RoID4gMiAmJiBcIi4uLlwifVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdGV4dC14cyBtdC0yIGZsZXggaXRlbXMtY2VudGVyICR7XG4gICAgICAgICAgICAgICAgICAgIG1lc3NhZ2UudHlwZSA9PT0gXCJ1c2VyXCIgPyBcImp1c3RpZnktZW5kIHRleHQtc2xhdGUtNTAwXCIgOiBcImp1c3RpZnktc3RhcnQgdGV4dC1zbGF0ZS01MDBcIlxuICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj57bWVzc2FnZS50aW1lc3RhbXAudG9Mb2NhbGVUaW1lU3RyaW5nKCl9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICB7bWVzc2FnZS50eXBlID09PSBcInVzZXJcIiAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge21lc3NhZ2Uuc3RhdHVzID09PSBcInNlbmRpbmdcIiAmJiBcIuKPs1wifVxuICAgICAgICAgICAgICAgICAgICAgICAge21lc3NhZ2Uuc3RhdHVzID09PSBcInNlbnRcIiAmJiBcIuKck1wifVxuICAgICAgICAgICAgICAgICAgICAgICAge21lc3NhZ2Uuc3RhdHVzID09PSBcInJlYWRcIiAmJiBcIuKck+Kck1wifVxuICAgICAgICAgICAgICAgICAgICAgICAge21lc3NhZ2Uuc3RhdHVzID09PSBcImVycm9yXCIgJiYgXCLinYxcIn1cbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHttZXNzYWdlLnR5cGUgPT09IFwidXNlclwiICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1ncmFkaWVudC10by1yIGZyb20tc2xhdGUtNDAwIHRvLXNsYXRlLTUwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWwtMyBmbGV4LXNocmluay0wIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAge3VzZXI/Lm5hbWU/LmNoYXJBdCgwKSB8fCBcIlVcIn1cbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApKX1cblxuICAgICAgICAgICAge2lzTG9hZGluZyAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LXN0YXJ0XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwMCB0by1pbmRpZ28tNjAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtci0zIGZsZXgtc2hyaW5rLTAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgPFNwYXJrbGVzIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWF4LXctbWRcIj5cbiAgICAgICAgICAgICAgICAgIDxFbmhhbmNlZExvYWRpbmdJbmRpY2F0b3JcbiAgICAgICAgICAgICAgICAgICAgaXNMb2FkaW5nPXtpc0xvYWRpbmd9XG4gICAgICAgICAgICAgICAgICAgIHN0YWdlPXtjdXJyZW50U3RhZ2V9XG4gICAgICAgICAgICAgICAgICAgIHByb2dyZXNzPXtwcm9ncmVzc31cbiAgICAgICAgICAgICAgICAgICAgZXN0aW1hdGVkVGltZT17MzB9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2FuY2VsPXtjYW5jZWxMb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgICBvblJldHJ5PXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgY2FuY2VsTG9hZGluZygpXG4gICAgICAgICAgICAgICAgICAgICAgaGFuZGxlU2VuZE1lc3NhZ2UoKVxuICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICBzaG93UmV0cnlBZnRlcj17NjB9IC8vIDYw56eS5ZCO5omN5pi+56S66YeN6K+V77yM57uZQUnlhYXliIbml7bpl7RcbiAgICAgICAgICAgICAgICAgICAgYWxsb3dDYW5jZWw9e3RydWV9XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8ZGl2IHJlZj17bWVzc2FnZXNFbmRSZWZ9IC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDmmbrog73lu7rorq7ljLrln5/vvIjlkIjlubZBSeW7uuiuruWSjOW/q+mAn+WbnuWkje+8iSAqL31cbiAgICAgICAgeygoKSA9PiB7XG4gICAgICAgICAgY29uc3Qgc3VnZ2VzdGlvbnMgPSBnZXRTbWFydFN1Z2dlc3Rpb25zKClcbiAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2Bib3JkZXItdCBib3JkZXItc2xhdGUtMjAwIHAtNCBmbGV4LXNocmluay0wICR7XG4gICAgICAgICAgICAgIHN1Z2dlc3Rpb25zLnR5cGUgPT09IFwiYWlfc3VnZ2VzdGlvbnNcIlxuICAgICAgICAgICAgICAgID8gXCJiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS01MCB0by1pbmRpZ28tNTBcIlxuICAgICAgICAgICAgICAgIDogc3VnZ2VzdGlvbnMudHlwZSA9PT0gXCJyZXF1aXJlbWVudF9mcmFtZXdvcmtcIlxuICAgICAgICAgICAgICAgID8gXCJiZy1ncmFkaWVudC10by1yIGZyb20tYW1iZXItNTAgdG8tb3JhbmdlLTUwXCJcbiAgICAgICAgICAgICAgICA6IFwiYmctd2hpdGVcIlxuICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIG1iLTNcIj5cbiAgICAgICAgICAgICAgICAgIHtzdWdnZXN0aW9ucy50eXBlID09PSBcImFpX3N1Z2dlc3Rpb25zXCIgPyAoXG4gICAgICAgICAgICAgICAgICAgIDxTcGFya2xlcyBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtYmx1ZS02MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbVwiPntzdWdnZXN0aW9ucy5pY29ufTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2B0ZXh0LXNtIGZvbnQtbWVkaXVtICR7XG4gICAgICAgICAgICAgICAgICAgIHN1Z2dlc3Rpb25zLnR5cGUgPT09IFwiYWlfc3VnZ2VzdGlvbnNcIlxuICAgICAgICAgICAgICAgICAgICAgID8gXCJ0ZXh0LWJsdWUtODAwXCJcbiAgICAgICAgICAgICAgICAgICAgICA6IHN1Z2dlc3Rpb25zLnR5cGUgPT09IFwicmVxdWlyZW1lbnRfZnJhbWV3b3JrXCJcbiAgICAgICAgICAgICAgICAgICAgICA/IFwidGV4dC1hbWJlci04MDBcIlxuICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LXNsYXRlLTYwMFwiXG4gICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgIHtzdWdnZXN0aW9ucy50aXRsZX1cbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHsvKiDmmL7npLrlia/moIfpopjvvIjnlKjkuo7pnIDmsYLmoYbmnrbmj5DnpLrvvIkgKi99XG4gICAgICAgICAgICAgICAge3N1Z2dlc3Rpb25zLnN1YnRpdGxlICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWFtYmVyLTcwMCBtYi0zIHAtMiBiZy1hbWJlci0xMDAgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWFtYmVyLTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICB7c3VnZ2VzdGlvbnMuc3VidGl0bGV9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAge3N1Z2dlc3Rpb25zLml0ZW1zLm1hcCgoaXRlbSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlU3VnZ2VzdGlvbkNsaWNrKGl0ZW0pfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHRleHQteHMgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHN1Z2dlc3Rpb25zLnR5cGUgPT09IFwiYWlfc3VnZ2VzdGlvbnNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiYmctd2hpdGUgaG92ZXI6YmctYmx1ZS01MCBib3JkZXItYmx1ZS0yMDAgdGV4dC1ibHVlLTcwMCBob3Zlcjp0ZXh0LWJsdWUtODAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiBzdWdnZXN0aW9ucy50eXBlID09PSBcInJlcXVpcmVtZW50X2ZyYW1ld29ya1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJiZy13aGl0ZSBob3ZlcjpiZy1hbWJlci01MCBib3JkZXItYW1iZXItMjAwIHRleHQtYW1iZXItNzAwIGhvdmVyOnRleHQtYW1iZXItODAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcImJnLXNsYXRlLTUwIGhvdmVyOmJnLXNsYXRlLTEwMCBib3JkZXItc2xhdGUtMjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1yLTFcIj57aXRlbS5pY29ufTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICB7aXRlbS50ZXh0fVxuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIClcbiAgICAgICAgfSkoKX1cblxuICAgICAgICB7Lyog6L6T5YWl5Yy65Z+fICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci10IGJvcmRlci1zbGF0ZS0yMDAgcC00IGJnLXdoaXRlIGZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBiZy1zbGF0ZS01MCByb3VuZGVkLXhsIHAtM1wiPlxuICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwiZ2hvc3RcIiBzaXplPVwiaWNvblwiIGNsYXNzTmFtZT1cInRleHQtc2xhdGUtNTAwIGhvdmVyOnRleHQtc2xhdGUtNzAwXCI+XG4gICAgICAgICAgICAgIDxQYXBlcmNsaXAgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cImdob3N0XCIgc2l6ZT1cImljb25cIiBjbGFzc05hbWU9XCJ0ZXh0LXNsYXRlLTUwMCBob3Zlcjp0ZXh0LXNsYXRlLTcwMFwiPlxuICAgICAgICAgICAgICA8TWljIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgdmFsdWU9e2lucHV0VmFsdWV9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0SW5wdXRWYWx1ZShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6L6T5YWl5oKo55qE6Zeu6aKY77yM5oiR5p2l5Li65oKo5o+Q5L6b5LiT5Lia5bu66K6uLi4uXCJcbiAgICAgICAgICAgICAgb25LZXlEb3duPXsoZSkgPT4gZS5rZXkgPT09IFwiRW50ZXJcIiAmJiAhZS5zaGlmdEtleSAmJiBoYW5kbGVTZW5kTWVzc2FnZSgpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgYm9yZGVyLTAgYmctd2hpdGUgc2hhZG93LXNtIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNlbmRNZXNzYWdlfVxuICAgICAgICAgICAgICBkaXNhYmxlZD17IWlucHV0VmFsdWUudHJpbSgpIHx8IGlzTG9hZGluZ31cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNjAwIHRvLWluZGlnby02MDAgaG92ZXI6ZnJvbS1ibHVlLTcwMCBob3Zlcjp0by1pbmRpZ28tNzAwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFNlbmQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIOWPs+S+p+mdouadvyAtIOmcgOaxguaUtumbhuaIluWOhuWPsuWvueivnSAqL31cbiAgICAgIHsoIXJpZ2h0UGFuZWxDb2xsYXBzZWQgfHwgc2hvd0hpc3RvcnkpICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9e2Bib3JkZXItbCBib3JkZXItc2xhdGUtMjAwIGJnLXdoaXRlIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBmbGV4IGZsZXgtY29sIGgtZnVsbCAke1xuICAgICAgICAgIGlzTW9iaWxlID8gJ2Fic29sdXRlIHJpZ2h0LTAgdG9wLTAgYm90dG9tLTAgdy04MCBzaGFkb3cteGwgei0xMCcgOiAndy04MCBmbGV4LXNocmluay0wJ1xuICAgICAgICB9YH0+XG4gICAgICAgICAgey8qIOWPs+S+p+mdouadv+WktOmDqCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci1iIGJvcmRlci1zbGF0ZS0yMDAgcC00IGZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICB7c2hvd0hpc3RvcnkgPyAoXG4gICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICA8SGlzdG9yeSBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtaW5kaWdvLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtc2xhdGUtOTAwXCI+5a+56K+d5Y6G5Y+yPC9oMz5cbiAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICA8Q2xpcGJvYXJkTGlzdCBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtaW5kaWdvLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtc2xhdGUtOTAwXCI+6ZyA5rGC5pS26ZuG5Yqp5omLPC9oMz5cbiAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgICB7Lyog5YiH5o2i5oyJ6ZKuICovfVxuICAgICAgICAgICAgICAgIHshc2hvd0hpc3RvcnkgJiYgKFxuICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93SGlzdG9yeSh0cnVlKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1zbGF0ZS01MDAgaG92ZXI6dGV4dC1zbGF0ZS03MDAgaC02IHctNiBwLTBcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8SGlzdG9yeSBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAge3Nob3dIaXN0b3J5ICYmIChcbiAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0hpc3RvcnkoZmFsc2UpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXNsYXRlLTUwMCBob3Zlcjp0ZXh0LXNsYXRlLTcwMCBoLTYgdy02IHAtMFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxDbGlwYm9hcmRMaXN0IGNsYXNzTmFtZT1cImgtMyB3LTNcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBzZXRSaWdodFBhbmVsQ29sbGFwc2VkKHRydWUpXG4gICAgICAgICAgICAgICAgICAgIHNldFNob3dIaXN0b3J5KGZhbHNlKVxuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtc2xhdGUtNTAwIGgtNiB3LTYgcC0wXCJcbiAgICAgICAgICAgICAgICAgIHRpdGxlPVwi6ZqQ6JeP6Z2i5p2/XCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8Q2hldnJvblJpZ2h0IGNsYXNzTmFtZT1cImgtMyB3LTNcIiAvPlxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNsYXRlLTYwMCBtdC0xXCI+XG4gICAgICAgICAgICAgIHtzaG93SGlzdG9yeVxuICAgICAgICAgICAgICAgID8gXCLmn6XnnIvlkoznrqHnkIbmgqjnmoTlr7nor53ljoblj7JcIlxuICAgICAgICAgICAgICAgIDogXCLlv6vpgJ/loavlhpnpobnnm67pnIDmsYLvvIzojrflvpfmm7Tnsr7lh4bnmoRBSeW7uuiurlwiXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiDpnaLmnb/lhoXlrrkgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLWgtMCByZWxhdGl2ZVwiPlxuICAgICAgICAgICAge3Nob3dIaXN0b3J5ID8gKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgICAgICAgPENvbnZlcnNhdGlvbkhpc3RvcnlcbiAgICAgICAgICAgICAgICAgIG9uTG9hZENvbnZlcnNhdGlvbj17aGFuZGxlTG9hZENvbnZlcnNhdGlvbn1cbiAgICAgICAgICAgICAgICAgIGN1cnJlbnRNZXNzYWdlcz17bWVzc2FnZXN9XG4gICAgICAgICAgICAgICAgICBvblNhdmVDdXJyZW50Q29udmVyc2F0aW9uPXtoYW5kbGVTYXZlQ3VycmVudENvbnZlcnNhdGlvbn1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBvdmVyZmxvdy15LWF1dG8gcC00IHNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgIHsvKiDmlofnjK7mkJzntKLlvIDlhbMgKi99XG4gICAgICAgICAgICAgICAgPExpdGVyYXR1cmVTZWFyY2hUb2dnbGVcbiAgICAgICAgICAgICAgICAgIGVuYWJsZWQ9e2VuYWJsZUxpdGVyYXR1cmVTZWFyY2h9XG4gICAgICAgICAgICAgICAgICBvblRvZ2dsZT17c2V0RW5hYmxlTGl0ZXJhdHVyZVNlYXJjaH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1iLTRcIlxuICAgICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgICB7Lyog6ZyA5rGC5pS26ZuG5ZmoICovfVxuICAgICAgICAgICAgICAgIDxQcm9ncmVzc2l2ZVJlcXVpcmVtZW50Q29sbGVjdG9yXG4gICAgICAgICAgICAgICAgICBvblJlcXVpcmVtZW50c0NoYW5nZT17aGFuZGxlUmVxdWlyZW1lbnRzQ2hhbmdlfVxuICAgICAgICAgICAgICAgICAgb25TdWJtaXRSZXF1aXJlbWVudHM9e2hhbmRsZVJlcXVpcmVtZW50c1N1Ym1pdH1cbiAgICAgICAgICAgICAgICAgIGlzQ29tcGFjdD17dHJ1ZX1cbiAgICAgICAgICAgICAgICAgIHJlc2V0VHJpZ2dlcj17cmVzZXRUcmlnZ2VyfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7Lyog56e75Yqo56uv6YGu572pICovfVxuICAgICAge2lzTW9iaWxlICYmICFyaWdodFBhbmVsQ29sbGFwc2VkICYmIChcbiAgICAgICAgPGRpdlxuICAgICAgICAgIGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2sgYmctb3BhY2l0eS01MCB6LTVcIlxuICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFJpZ2h0UGFuZWxDb2xsYXBzZWQodHJ1ZSl9XG4gICAgICAgIC8+XG4gICAgICApfVxuXG4gICAgICB7Lyog56e75Yqo56uv5pi+56S65Yqp5omL5oyJ6ZKuICovfVxuICAgICAge2lzTW9iaWxlICYmIHJpZ2h0UGFuZWxDb2xsYXBzZWQgJiYgKFxuICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgY2xhc3NOYW1lPVwiZml4ZWQgYm90dG9tLTQgcmlnaHQtNCByb3VuZGVkLWZ1bGwgdy0xMiBoLTEyIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTYwMCB0by1pbmRpZ28tNjAwIGhvdmVyOmZyb20tYmx1ZS03MDAgaG92ZXI6dG8taW5kaWdvLTcwMCBzaGFkb3ctbGcgei0xMFwiXG4gICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0UmlnaHRQYW5lbENvbGxhcHNlZChmYWxzZSl9XG4gICAgICAgID5cbiAgICAgICAgICA8Q2xpcGJvYXJkTGlzdCBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cbiAgICAgICAgPC9CdXR0b24+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApXG59Il0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlUmVmIiwidXNlRWZmZWN0IiwiQnV0dG9uIiwiSW5wdXQiLCJCYWRnZSIsIlNlbmQiLCJQYXBlcmNsaXAiLCJNaWMiLCJBbGVydENpcmNsZSIsIk1lc3NhZ2VTcXVhcmUiLCJDbGlwYm9hcmRMaXN0IiwiQ2hldnJvbkxlZnQiLCJDaGV2cm9uUmlnaHQiLCJTcGFya2xlcyIsIkJyYWluIiwiWmFwIiwiSGlzdG9yeSIsIlJvdGF0ZUNjdyIsIkFsZXJ0IiwiQWxlcnREZXNjcmlwdGlvbiIsInVzZUF1dGgiLCJjb252ZXJzYXRpb25BcGkiLCJsaXRlcmF0dXJlQXBpIiwic29sdXRpb25BcGkiLCJlcnJvckhhbmRsZXIiLCJMb2FkaW5nU3RhZ2UiLCJFbmhhbmNlZExvYWRpbmdJbmRpY2F0b3IiLCJ1c2VTdGFnZUxvYWRpbmciLCJ0b2FzdCIsIlByb2dyZXNzaXZlUmVxdWlyZW1lbnRDb2xsZWN0b3IiLCJGb3JtYXR0ZWRNZXNzYWdlIiwiQ29udmVyc2F0aW9uSGlzdG9yeSIsIkxpdGVyYXR1cmVTZWFyY2hUb2dnbGUiLCJDb252ZXJzYXRpb25JbnRlcmZhY2UiLCJtZXNzYWdlcyIsInNldE1lc3NhZ2VzIiwiaW5wdXRWYWx1ZSIsInNldElucHV0VmFsdWUiLCJlcnJvciIsInNldEVycm9yIiwiY29udmVyc2F0aW9uSWQiLCJzZXRDb252ZXJzYXRpb25JZCIsInJlcXVpcmVtZW50cyIsInNldFJlcXVpcmVtZW50cyIsInJpZ2h0UGFuZWxDb2xsYXBzZWQiLCJzZXRSaWdodFBhbmVsQ29sbGFwc2VkIiwiaXNNb2JpbGUiLCJzZXRJc01vYmlsZSIsInNob3dIaXN0b3J5Iiwic2V0U2hvd0hpc3RvcnkiLCJjdXJyZW50U2Vzc2lvbklkIiwic2V0Q3VycmVudFNlc3Npb25JZCIsInJlc2V0VHJpZ2dlciIsInNldFJlc2V0VHJpZ2dlciIsImVuYWJsZUxpdGVyYXR1cmVTZWFyY2giLCJzZXRFbmFibGVMaXRlcmF0dXJlU2VhcmNoIiwibWVzc2FnZXNFbmRSZWYiLCJ1c2VyIiwiaXNMb2FkaW5nIiwiY3VycmVudFN0YWdlIiwicHJvZ3Jlc3MiLCJzdGFydExvYWRpbmciLCJ1cGRhdGVTdGFnZSIsImZpbmlzaExvYWRpbmciLCJjYW5jZWxMb2FkaW5nIiwiY2hlY2tNb2JpbGUiLCJ3aW5kb3ciLCJpbm5lcldpZHRoIiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJzY3JvbGxUb0JvdHRvbSIsImN1cnJlbnQiLCJzY3JvbGxJbnRvVmlldyIsImJlaGF2aW9yIiwid2VsY29tZU1lc3NhZ2UiLCJpZCIsInR5cGUiLCJjb250ZW50IiwibmFtZSIsInRpbWVzdGFtcCIsIkRhdGUiLCJzdGF0dXMiLCJjb25maWRlbmNlIiwic3VnZ2VzdGlvbnMiLCJhdXRvU2F2ZUNvbnZlcnNhdGlvbiIsImxlbmd0aCIsImV4aXN0aW5nU2Vzc2lvbnMiLCJzdG9yYWdlS2V5IiwiSlNPTiIsInBhcnNlIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsImdlbmVyYXRlVGl0bGUiLCJtc2dzIiwiZmlyc3RVc2VyTWVzc2FnZSIsImZpbmQiLCJtIiwidHJpbSIsInN1YnN0cmluZyIsInNlc3Npb25JZCIsIk1hdGgiLCJub3ciLCJyYW5kb20iLCJ0b1N0cmluZyIsInN1YnN0ciIsInNlc3Npb24iLCJ0aXRsZSIsImNyZWF0ZWRBdCIsInMiLCJ1cGRhdGVkQXQiLCJpc1N0YXJyZWQiLCJpc0FyY2hpdmVkIiwidGFncyIsIm1lc3NhZ2VDb3VudCIsImxhc3RNZXNzYWdlIiwidXBkYXRlZFNlc3Npb25zIiwibWFwIiwic2xpY2UiLCJzZXRJdGVtIiwic3RyaW5naWZ5IiwiY29uc29sZSIsImhhbmRsZVNlbmRNZXNzYWdlIiwidXNlck1lc3NhZ2UiLCJuZXdVc2VyTWVzc2FnZSIsInByZXYiLCJjb252ZXJzYXRpb25IaXN0b3J5IiwibXNnIiwicm9sZSIsImVuaGFuY2VkQ29udGV4dCIsInVzZXJfcHJvZmlsZSIsIm9yZ2FuaXphdGlvbiIsImV4cGVydGlzZSIsImV4cGVydGlzZV9hcmVhcyIsInJlc2VhcmNoX2ludGVyZXN0cyIsImNvbnZlcnNhdGlvbl9oaXN0b3J5IiwiZW5hYmxlX2xpdGVyYXR1cmVfc2VhcmNoIiwiYWlBcGlSZXNwb25zZSIsImhhbmRsZVN0YWdlZE9wZXJhdGlvbiIsInN0YWdlIiwiQU5BTFlaSU5HIiwib3BlcmF0aW9uIiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0Iiwic3RlcCIsIlNFQVJDSElOR19MSVRFUkFUVVJFIiwiR0VORVJBVElOR19SRVNQT05TRSIsInNlbmRNZXNzYWdlIiwibWVzc2FnZSIsImNvbnZlcnNhdGlvbl9pZCIsInVuZGVmaW5lZCIsImNvbnZlcnNhdGlvbl90eXBlIiwiaGlzdG9yeSIsImNvbnRleHQiLCJGT1JNQVRUSU5HIiwibWF4UmV0cmllcyIsInJldHJ5RGVsYXkiLCJ1c2VyRnJpZW5kbHlNZXNzYWdlIiwic2hvd1RvYXN0Iiwib25SZXRyeSIsImF0dGVtcHQiLCJpbmNsdWRlcyIsImluZm8iLCJhaVJlc3BvbnNlIiwic291cmNlcyIsIm5ld01lc3NhZ2VzIiwiZXJyIiwiaGFuZGxlUmVxdWlyZW1lbnRzQ2hhbmdlIiwibmV3UmVxdWlyZW1lbnRzIiwiaGFuZGxlUmVxdWlyZW1lbnRzU3VibWl0IiwicmVxdWlyZW1lbnRTdW1tYXJ5IiwiZ2VuZXJhdGVSZXF1aXJlbWVudFN1bW1hcnkiLCJzb2x1dGlvblJlc3BvbnNlIiwibGl0ZXJhdHVyZVJlc3VsdHMiLCJ2YWxpZGF0aW9uIiwidmFsaWRhdGVSZXF1aXJlbWVudHMiLCJsaXRlcmF0dXJlUmVzdWx0IiwiZW5oYW5jZWRMaXRlcmF0dXJlU2VhcmNoIiwicXVlcnkiLCJyZXNlYXJjaEdvYWwiLCJzYW1wbGVUeXBlIiwibGl0ZXJhdHVyZV9yZXN1bHRzIiwic2VhcmNoX3Zpc2libGUiLCJ3YXJuIiwibG9nIiwidXNlcl9tZXNzYWdlIiwiZnJhbWV3b3JrX3RlbXBsYXRlIiwiZnJhbWV3b3JrUmVzcG9uc2UiLCJnZW5lcmF0ZUNvbXByZWhlbnNpdmVGcmFtZXdvcmsiLCJzdWNjZXNzIiwic29sdXRpb25faWQiLCJmcmFtZXdvcmtfaWQiLCJnZW5lcmF0ZWRfYXQiLCJnZW5lcmF0aW9uX3RpbWUiLCJjbGllbnRfcmVxdWlyZW1lbnRzIiwiY29tcHJlaGVuc2l2ZV9mcmFtZXdvcmsiLCJkYXRhIiwicmVjb21tZW5kZWRfc29sdXRpb24iLCJwbGF0Zm9ybSIsInJlYXNvbmluZyIsInNwZWNpZmljYXRpb25zIiwic29sdXRpb25fb3ZlcnZpZXciLCJjb3N0X2FuYWx5c2lzIiwicmlza19hc3Nlc3NtZW50IiwidGltZWxpbmUiLCJpbXBsZW1lbnRhdGlvbl9wbGFuIiwiZGVsaXZlcmFibGVzIiwibmV4dF9zdGVwcyIsImNvbnRhY3RfaW5mbyIsImVtYWlsIiwicGhvbmUiLCJmcmFtZXdvcmtfZmVhdHVyZXMiLCJyZXNlYXJjaF9pbnRlbnRfYW5hbHlzaXMiLCJwcmVjaXNpb25fc2VhcmNoX2xpbmtzIiwibGl0ZXJhdHVyZV9yZWNvbW1lbmRhdGlvbnMiLCJwbGF0Zm9ybV9jb21wYXJpc29uIiwiRXJyb3IiLCJmcmFtZXdvcmtFcnJvciIsInN0YWNrIiwidG9JU09TdHJpbmciLCJmYWxsYmFja19ub3RpY2UiLCJleHBlcnRfY29udGFjdCIsInJlYXNvbiIsInNvbHV0aW9uQ29udGVudCIsImZvcm1hdFNvbHV0aW9uUmVzcG9uc2UiLCJhaV9hbmFseXNpc19mZWF0dXJlcyIsImludGVudF9hbmFseXplZCIsImFuYWx5c2lzX3NvdXJjZXMiLCJjb25jYXQiLCJpbnRlbGxpZ2VudF9mZWF0dXJlcyIsInB1c2giLCJjb25maWRlbmNlX3Njb3JlIiwicmVxcyIsInNlY3Rpb25zIiwic3BlY2llc1R5cGUiLCJleHBlcmltZW50VHlwZSIsInNhbXBsZUNvdW50Iiwic2FtcGxlQ29uZGl0aW9uIiwiY2VsbENvdW50Iiwic2FtcGxlUHJvY2Vzc2luZyIsImNlbGxWaWFiaWxpdHkiLCJidWRnZXQiLCJ1cmdlbmN5TGV2ZWwiLCJzZXF1ZW5jaW5nRGVwdGgiLCJhbmFseXNpc1R5cGUiLCJkYXRhQW5hbHlzaXNOZWVkcyIsIm5lZWRzQ2VsbFNvcnRpbmciLCJzcGVjaWFsUmVxdWlyZW1lbnRzIiwiam9pbiIsImludGVudF9hbmFseXNpcyIsInJlc2VhcmNoX2RvbWFpbiIsInBlcnNvbmFsaXphdGlvbl9sZXZlbCIsImlzUGVyc29uYWxpemVkIiwiaXNJbnRlbGxpZ2VudFJlY29tbWVuZGF0aW9uIiwiaXNBaUFuYWx5emVkIiwiaXNGYWxsYmFjayIsInJvdW5kIiwiYnVkZ2V0X2NhdGVnb3J5IiwidXJnZW5jeV9sZXZlbCIsInRlY2huaWNhbF9wcmVmZXJlbmNlIiwic3VjY2Vzc19wcm9iYWJpbGl0eSIsIm92ZXJhbGxfcmlza19sZXZlbCIsInRlY2huaWNhbF9yaXNrcyIsImZvckVhY2giLCJyaXNrIiwicHJvYmFiaWxpdHkiLCJpbXBhY3QiLCJtaXRpZ2F0aW9uIiwiaWRlbnRpZmllZF9yaXNrcyIsImhvdF9wYXBlcnMiLCJwYXBlciIsImluZGV4IiwicHVibWVkTGluayIsImRvaSIsImVuY29kZVVSSUNvbXBvbmVudCIsInNjaG9sYXJMaW5rIiwiam91cm5hbCIsImltcGFjdF9mYWN0b3IiLCJleHBhbmRlZF9rZXl3b3JkcyIsImtleXdvcmRzIiwic2VtYW50aWNfZXhwYW5zaW9uIiwidHJlbmRpbmdfdGVybXMiLCJrZXl3b3JkIiwibW9sZWN1bGFyX3RhcmdldHMiLCJjb21iaW5lZF9yZXN1bHRzIiwicHVibWVkX2lkIiwicHVibGljYXRpb25feWVhciIsImF1dGhvcnMiLCJhYnN0cmFjdCIsInNvdXJjZSIsInNtYXJ0X2luc2lnaHRzIiwiaW5zaWdodCIsImNvbGxhYm9yYXRpb25fb3Bwb3J0dW5pdGllcyIsImNvbGxhYiIsImluc3RpdHV0aW9uIiwiZXhwZXJ0IiwiY29sbGFib3JhdGlvbl90eXBlIiwic2VhcmNoX3N1Z2dlc3Rpb25zIiwicHJpbWFyeUtleXdvcmRzIiwibGlua3MiLCJzZWFyY2hMaW5rcyIsImxpbmtUZXh0cyIsImxpbmsiLCJlbW9qaSIsInVybCIsInNlY29uZGFyeUtleXdvcmRzIiwicGFwZXJzVG9TaG93IiwiYXV0aG9yTGlzdCIsIm1vcmVBdXRob3JzIiwia2V5X2ZpbmRpbmdzIiwibWV0aG9kb2xvZ3lfc3VtbWFyeSIsInRvdGFsX3BhcGVycyIsImNvbXByZWhlbnNpdmVfc29sdXRpb24iLCJlbmhhbmNlZF9mZWF0dXJlcyIsImZpdmVfY29yZV9mdW5jdGlvbnMiLCJnZW5lcmF0ZVJlcXVpcmVtZW50QmFzZWRTdWdnZXN0aW9uIiwic3VnZ2VzdGlvbk1lc3NhZ2UiLCJhcGlSZXNwb25zZSIsImFpU3VnZ2VzdGlvbiIsImdldE1pc3NpbmdSZXF1aXJlbWVudHMiLCJyZXF1aXJlZEZpZWxkcyIsImtleSIsImxhYmVsIiwicXVlc3Rpb24iLCJmaWx0ZXIiLCJmaWVsZCIsImdldFNtYXJ0U3VnZ2VzdGlvbnMiLCJpY29uIiwiaXRlbXMiLCJzdWdnZXN0aW9uIiwidGV4dCIsIm1pc3NpbmdSZXFzIiwiY29tcGxldGVuZXNzIiwic3VidGl0bGUiLCJyZXEiLCJhY3Rpb24iLCJoYW5kbGVTdWdnZXN0aW9uQ2xpY2siLCJpdGVtIiwiaGFuZGxlTG9hZENvbnZlcnNhdGlvbiIsImhhbmRsZVNhdmVDdXJyZW50Q29udmVyc2F0aW9uIiwiaGFuZGxlTmV3Q29udmVyc2F0aW9uIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDIiLCJwIiwidmFyaWFudCIsInNpemUiLCJvbkNsaWNrIiwic3BhbiIsInRvTG9jYWxlVGltZVN0cmluZyIsImNoYXJBdCIsImVzdGltYXRlZFRpbWUiLCJvbkNhbmNlbCIsInNob3dSZXRyeUFmdGVyIiwiYWxsb3dDYW5jZWwiLCJyZWYiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsInBsYWNlaG9sZGVyIiwib25LZXlEb3duIiwic2hpZnRLZXkiLCJkaXNhYmxlZCIsImgzIiwib25Mb2FkQ29udmVyc2F0aW9uIiwiY3VycmVudE1lc3NhZ2VzIiwib25TYXZlQ3VycmVudENvbnZlcnNhdGlvbiIsImVuYWJsZWQiLCJvblRvZ2dsZSIsIm9uUmVxdWlyZW1lbnRzQ2hhbmdlIiwib25TdWJtaXRSZXF1aXJlbWVudHMiLCJpc0NvbXBhY3QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/conversation-interface.tsx\n"));

/***/ })

});