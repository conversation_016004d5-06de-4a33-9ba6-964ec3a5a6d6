"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/comprehensive-solution-display.tsx":
/*!*******************************************************!*\
  !*** ./components/comprehensive-solution-display.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ComprehensiveSolutionDisplay: () => (/* binding */ ComprehensiveSolutionDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,CheckCircle,Clock,Copy,ExternalLink,Search,Shield,Target,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,CheckCircle,Clock,Copy,ExternalLink,Search,Shield,Target,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,CheckCircle,Clock,Copy,ExternalLink,Search,Shield,Target,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,CheckCircle,Clock,Copy,ExternalLink,Search,Shield,Target,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,CheckCircle,Clock,Copy,ExternalLink,Search,Shield,Target,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,CheckCircle,Clock,Copy,ExternalLink,Search,Shield,Target,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,CheckCircle,Clock,Copy,ExternalLink,Search,Shield,Target,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,CheckCircle,Clock,Copy,ExternalLink,Search,Shield,Target,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,CheckCircle,Clock,Copy,ExternalLink,Search,Shield,Target,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,CheckCircle,Clock,Copy,ExternalLink,Search,Shield,Target,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,CheckCircle,Clock,Copy,ExternalLink,Search,Shield,Target,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* __next_internal_client_entry_do_not_use__ ComprehensiveSolutionDisplay auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ComprehensiveSolutionDisplay(param) {\n    let { solutionData } = param;\n    var _solutionData_personalized_solution, _solutionData_personalized_solution_核心优势, _solutionData_personalized_solution1, _solutionData_personalized_solution2, _solutionData_personalized_solution3, _solutionData_literature_recommendations, _solutionData_literature_recommendations1, _solutionData_literature_recommendations2, _solutionData_search_keywords, _solutionData_search_keywords1, _solutionData_search_keywords2, _solutionData_pain_point_analysis, _solutionData_pain_point_analysis1, _solutionData_pain_point_analysis2, _solutionData_risk_assessment, _solutionData_risk_assessment1, _solutionData_risk_assessment2, _solutionData_综合建议_成功关键因素;\n    _s();\n    const [copiedKeyword, setCopiedKeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const copyToClipboard = (text, id)=>{\n        navigator.clipboard.writeText(text);\n        setCopiedKeyword(id);\n        setTimeout(()=>setCopiedKeyword(null), 2000);\n    };\n    const getRiskLevelColor = (level)=>{\n        switch(level){\n            case '低':\n                return 'text-green-600 bg-green-50 border-green-200';\n            case '中':\n            case '中等':\n                return 'text-yellow-600 bg-yellow-50 border-yellow-200';\n            case '高':\n            case '中高':\n                return 'text-red-600 bg-red-50 border-red-200';\n            default:\n                return 'text-gray-600 bg-gray-50 border-gray-200';\n        }\n    };\n    const getPriorityIcon = (priority)=>{\n        switch(priority){\n            case '高':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 24\n                }, this);\n            case '中':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 24\n                }, this);\n            case '低':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 24\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: \"\\uD83E\\uDDEC 单细胞测序解决方案\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"基于您的需求生成的专业化单细胞测序方案、文献推荐和风险分析\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                defaultValue: \"solution\",\n                className: \"w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                        className: \"grid w-full grid-cols-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"solution\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"技术方案\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"literature\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"文献推荐\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"keywords\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"搜索关键词\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"painpoints\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"领域痛点\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"risks\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"风险评估\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"solution\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"个性化技术方案\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold mb-2\",\n                                                            children: \"推荐技术路线\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                            lineNumber: 108,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: \"mb-2\",\n                                                            children: ((_solutionData_personalized_solution = solutionData.personalized_solution) === null || _solutionData_personalized_solution === void 0 ? void 0 : _solutionData_personalized_solution.技术路线) || '10x Genomics标准流程'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                            lineNumber: 109,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: (_solutionData_personalized_solution1 = solutionData.personalized_solution) === null || _solutionData_personalized_solution1 === void 0 ? void 0 : (_solutionData_personalized_solution_核心优势 = _solutionData_personalized_solution1.核心优势) === null || _solutionData_personalized_solution_核心优势 === void 0 ? void 0 : _solutionData_personalized_solution_核心优势.map((advantage, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-green-500 mt-0.5 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                            lineNumber: 115,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm\",\n                                                                            children: advantage\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                            lineNumber: 116,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                    lineNumber: 114,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                            lineNumber: 112,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold mb-2\",\n                                                            children: \"预期成果\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: Object.entries(((_solutionData_personalized_solution2 = solutionData.personalized_solution) === null || _solutionData_personalized_solution2 === void 0 ? void 0 : _solutionData_personalized_solution2.预期成果) || {}).map((param)=>{\n                                                                let [key, value] = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-blue-50 p-3 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                            className: \"font-medium text-blue-900 mb-1\",\n                                                                            children: key\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                            lineNumber: 127,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-blue-700\",\n                                                                            children: value\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                            lineNumber: 128,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, key, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                    lineNumber: 126,\n                                                                    columnNumber: 23\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this),\n                                        ((_solutionData_personalized_solution3 = solutionData.personalized_solution) === null || _solutionData_personalized_solution3 === void 0 ? void 0 : _solutionData_personalized_solution3.专业化流程) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold mb-3\",\n                                                    children: \"专业化实验流程\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                    children: Object.entries(solutionData.personalized_solution.专业化流程).map((param)=>{\n                                                        let [step, details] = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                            className: \"border-l-4 border-l-blue-500\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                                className: \"p-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"font-semibold text-blue-900 mb-2\",\n                                                                        children: step\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                        lineNumber: 142,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    typeof details === 'object' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-1\",\n                                                                        children: Object.entries(details).map((param)=>{\n                                                                            let [key, value] = param;\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: [\n                                                                                            key,\n                                                                                            \":\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                        lineNumber: 147,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"ml-1 text-gray-600\",\n                                                                                        children: value\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                        lineNumber: 148,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, key, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                lineNumber: 146,\n                                                                                columnNumber: 33\n                                                                            }, this);\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                        lineNumber: 144,\n                                                                        columnNumber: 29\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: details\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                        lineNumber: 153,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                lineNumber: 141,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, step, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 23\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"literature\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"智能文献推荐\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-50 p-4 rounded-lg mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-blue-900 mb-2\",\n                                                    children: \"推荐策略\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: (_solutionData_literature_recommendations = solutionData.literature_recommendations) === null || _solutionData_literature_recommendations === void 0 ? void 0 : _solutionData_literature_recommendations.推荐策略\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this),\n                                        ((_solutionData_literature_recommendations1 = solutionData.literature_recommendations) === null || _solutionData_literature_recommendations1 === void 0 ? void 0 : _solutionData_literature_recommendations1.分类推荐) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: Object.entries(solutionData.literature_recommendations.分类推荐).map((param)=>{\n                                                let [category, papers] = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold mb-3 flex items-center gap-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                variant: \"secondary\",\n                                                                children: category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                lineNumber: 187,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: (Array.isArray(papers) ? papers : [\n                                                                papers\n                                                            ]).map((paper, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                                    className: \"border-l-4 border-l-green-500\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                                        className: \"p-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-start justify-between gap-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex-1\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                            className: \"font-semibold text-gray-900 mb-2\",\n                                                                                            children: paper.title || \"\".concat(category, \"相关文献 \").concat(index + 1)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                            lineNumber: 195,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        paper.reason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-sm text-gray-600 mb-2\",\n                                                                                            children: paper.reason\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                            lineNumber: 199,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        paper.authors && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-500\",\n                                                                                            children: [\n                                                                                                \"作者: \",\n                                                                                                Array.isArray(paper.authors) ? paper.authors.join(', ') : paper.authors\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                            lineNumber: 202,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        paper.journal && paper.year && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-500\",\n                                                                                            children: [\n                                                                                                paper.journal,\n                                                                                                \" (\",\n                                                                                                paper.year,\n                                                                                                \")\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                            lineNumber: 207,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                    lineNumber: 194,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex flex-col items-end gap-2\",\n                                                                                    children: [\n                                                                                        paper.priority && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                                            variant: paper.priority === '高' ? 'destructive' : 'secondary',\n                                                                                            children: paper.priority\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                            lineNumber: 214,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        paper.doi && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                            size: \"sm\",\n                                                                                            variant: \"outline\",\n                                                                                            asChild: true,\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                                                href: \"https://doi.org/\".concat(paper.doi),\n                                                                                                target: \"_blank\",\n                                                                                                rel: \"noopener noreferrer\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                                        className: \"h-3 w-3 mr-1\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                                        lineNumber: 221,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this),\n                                                                                                    \"查看\"\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                                lineNumber: 220,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                            lineNumber: 219,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                    lineNumber: 212,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                            lineNumber: 193,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                        lineNumber: 192,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, index, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                    lineNumber: 191,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, category, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 21\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this),\n                                        ((_solutionData_literature_recommendations2 = solutionData.literature_recommendations) === null || _solutionData_literature_recommendations2 === void 0 ? void 0 : _solutionData_literature_recommendations2.阅读建议) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                                                    className: \"space-y-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"阅读建议:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mt-1\",\n                                                                children: solutionData.literature_recommendations.阅读建议.优先顺序\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 mt-1\",\n                                                                children: solutionData.literature_recommendations.阅读建议.实用提示\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"keywords\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"智能搜索关键词\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-50 p-4 rounded-lg mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-green-900 mb-2\",\n                                                    children: \"搜索策略\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-green-700\",\n                                                    children: (_solutionData_search_keywords = solutionData.search_keywords) === null || _solutionData_search_keywords === void 0 ? void 0 : _solutionData_search_keywords.搜索策略\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                '主要关键词',\n                                                '技术关键词',\n                                                '热门方向',\n                                                '个性化关键词'\n                                            ].map((category)=>{\n                                                var _solutionData_search_keywords_category, _solutionData_search_keywords;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold mb-3\",\n                                                            children: category\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2\",\n                                                            children: (_solutionData_search_keywords = solutionData.search_keywords) === null || _solutionData_search_keywords === void 0 ? void 0 : (_solutionData_search_keywords_category = _solutionData_search_keywords[category]) === null || _solutionData_search_keywords_category === void 0 ? void 0 : _solutionData_search_keywords_category.map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"cursor-pointer hover:bg-blue-50 transition-colors\",\n                                                                    onClick: ()=>copyToClipboard(keyword, \"\".concat(category, \"-\").concat(index)),\n                                                                    children: [\n                                                                        keyword,\n                                                                        copiedKeyword === \"\".concat(category, \"-\").concat(index) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"h-3 w-3 ml-1 text-green-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                            lineNumber: 286,\n                                                                            columnNumber: 29\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-3 w-3 ml-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                            lineNumber: 288,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, category, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 19\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, this),\n                                        ((_solutionData_search_keywords1 = solutionData.search_keywords) === null || _solutionData_search_keywords1 === void 0 ? void 0 : _solutionData_search_keywords1.组合搜索建议) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold mb-3\",\n                                                    children: \"组合搜索建议\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: solutionData.search_keywords.组合搜索建议.map((query, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gray-50 p-3 rounded-lg font-mono text-sm flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: query\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                    lineNumber: 303,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: \"ghost\",\n                                                                    onClick: ()=>copyToClipboard(query, \"combo-\".concat(index)),\n                                                                    children: copiedKeyword === \"combo-\".concat(index) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-3 w-3 text-green-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                        lineNumber: 310,\n                                                                        columnNumber: 29\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                        lineNumber: 312,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                    lineNumber: 304,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this),\n                                        ((_solutionData_search_keywords2 = solutionData.search_keywords) === null || _solutionData_search_keywords2 === void 0 ? void 0 : _solutionData_search_keywords2.数据库建议) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold mb-3\",\n                                                    children: \"推荐数据库\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                                                    children: Object.entries(solutionData.search_keywords.数据库建议).map((param)=>{\n                                                        let [db, description] = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border rounded-lg p-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                    className: \"font-medium\",\n                                                                    children: db\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600 mt-1\",\n                                                                    children: description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, db, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 23\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"painpoints\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"领域痛点分析\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-orange-50 p-4 rounded-lg mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-orange-900 mb-2\",\n                                                    children: \"分析总结\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-orange-700\",\n                                                    children: (_solutionData_pain_point_analysis = solutionData.pain_point_analysis) === null || _solutionData_pain_point_analysis === void 0 ? void 0 : _solutionData_pain_point_analysis.痛点分析总结\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this),\n                                        ((_solutionData_pain_point_analysis1 = solutionData.pain_point_analysis) === null || _solutionData_pain_point_analysis1 === void 0 ? void 0 : _solutionData_pain_point_analysis1.主要痛点) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold\",\n                                                    children: \"主要痛点识别\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 19\n                                                }, this),\n                                                solutionData.pain_point_analysis.主要痛点.map((painPoint, index)=>{\n                                                    var _painPoint_具体表现, _painPoint_解决方案;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                        className: \"border-l-4 \".concat(painPoint.影响程度 === '高' ? 'border-l-red-500' : painPoint.影响程度 === '中高' ? 'border-l-orange-500' : 'border-l-yellow-500'),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                            className: \"p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start justify-between gap-4 mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"font-semibold\",\n                                                                                    children: painPoint.痛点\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                    lineNumber: 366,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                                    className: \"mt-1\",\n                                                                                    variant: \"secondary\",\n                                                                                    children: painPoint.类别\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                    lineNumber: 367,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                            lineNumber: 365,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                            className: getRiskLevelColor(painPoint.影响程度),\n                                                                            children: painPoint.影响程度\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                            lineNumber: 369,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                                                                    className: \"font-medium text-sm mb-1\",\n                                                                                    children: \"具体表现:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                    lineNumber: 376,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                    className: \"text-sm space-y-1\",\n                                                                                    children: (_painPoint_具体表现 = painPoint.具体表现) === null || _painPoint_具体表现 === void 0 ? void 0 : _painPoint_具体表现.map((symptom, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                            className: \"flex items-start gap-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                                    className: \"h-3 w-3 text-red-500 mt-0.5 flex-shrink-0\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                                    lineNumber: 380,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                symptom\n                                                                                            ]\n                                                                                        }, i, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                            lineNumber: 379,\n                                                                                            columnNumber: 33\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                    lineNumber: 377,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                            lineNumber: 375,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                                                                    className: \"font-medium text-sm mb-1\",\n                                                                                    children: \"解决方案:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                    lineNumber: 388,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                    className: \"text-sm space-y-1\",\n                                                                                    children: (_painPoint_解决方案 = painPoint.解决方案) === null || _painPoint_解决方案 === void 0 ? void 0 : _painPoint_解决方案.map((solution, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                            className: \"flex items-start gap-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                                    className: \"h-3 w-3 text-green-500 mt-0.5 flex-shrink-0\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                                    lineNumber: 392,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                solution\n                                                                                            ]\n                                                                                        }, i, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                            lineNumber: 391,\n                                                                                            columnNumber: 33\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                    lineNumber: 389,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                            lineNumber: 387,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                    lineNumber: 374,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 21\n                                                    }, this);\n                                                })\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 17\n                                        }, this),\n                                        ((_solutionData_pain_point_analysis2 = solutionData.pain_point_analysis) === null || _solutionData_pain_point_analysis2 === void 0 ? void 0 : _solutionData_pain_point_analysis2.缓解策略) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"缓解策略建议:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                lineNumber: 410,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mt-2\",\n                                                                children: Object.entries(solutionData.pain_point_analysis.缓解策略).map((param)=>{\n                                                                    let [type, strategies] = param;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                                                                className: \"font-medium mb-1\",\n                                                                                children: [\n                                                                                    type,\n                                                                                    \":\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                lineNumber: 414,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                className: \"text-sm space-y-1\",\n                                                                                children: strategies.map((strategy, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                        className: \"flex items-start gap-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"w-1 h-1 bg-blue-500 rounded-full mt-2 flex-shrink-0\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                                lineNumber: 418,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            strategy\n                                                                                        ]\n                                                                                    }, i, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                        lineNumber: 417,\n                                                                                        columnNumber: 33\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                lineNumber: 415,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, type, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                        lineNumber: 413,\n                                                                        columnNumber: 27\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"risks\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"项目风险评估\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        ((_solutionData_risk_assessment = solutionData.risk_assessment) === null || _solutionData_risk_assessment === void 0 ? void 0 : _solutionData_risk_assessment.风险评估总结) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                    className: \"text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        className: \"p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold \".concat(solutionData.risk_assessment.风险评估总结.整体风险等级 === '低' ? 'text-green-600' : solutionData.risk_assessment.风险评估总结.整体风险等级 === '中等' ? 'text-yellow-600' : 'text-red-600'),\n                                                                children: solutionData.risk_assessment.风险评估总结.整体风险等级\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"整体风险等级\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                    className: \"text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        className: \"p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-blue-600\",\n                                                                children: solutionData.risk_assessment.风险评估总结.成功概率\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                lineNumber: 459,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"成功概率\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                    className: \"text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        className: \"p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-purple-600\",\n                                                                children: [\n                                                                    solutionData.risk_assessment.风险评估总结.风险分数,\n                                                                    \"/3\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"风险分数\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                    className: \"text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        className: \"p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-orange-600\",\n                                                                children: solutionData.risk_assessment.风险评估总结.识别风险数\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"识别风险数\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 17\n                                        }, this),\n                                        ((_solutionData_risk_assessment1 = solutionData.risk_assessment) === null || _solutionData_risk_assessment1 === void 0 ? void 0 : _solutionData_risk_assessment1.详细风险列表) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold\",\n                                                    children: \"详细风险列表\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 19\n                                                }, this),\n                                                solutionData.risk_assessment.详细风险列表.map((risk, index)=>{\n                                                    var _risk_主要风险, _risk_缓解措施;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                        className: \"border-l-4 \".concat(risk.风险等级 === '高' || risk.风险等级 === '中高' ? 'border-l-red-500' : risk.风险等级 === '中' || risk.风险等级 === '中等' ? 'border-l-yellow-500' : 'border-l-green-500'),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                            className: \"p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start justify-between gap-4 mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"font-semibold\",\n                                                                                    children: risk.具体风险\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                    lineNumber: 495,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                                    className: \"mt-1\",\n                                                                                    variant: \"secondary\",\n                                                                                    children: risk.风险类别\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                    lineNumber: 496,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                            lineNumber: 494,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                            className: getRiskLevelColor(risk.风险等级),\n                                                                            children: risk.风险等级\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                            lineNumber: 498,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                    lineNumber: 493,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                                                                    className: \"font-medium text-sm mb-2\",\n                                                                                    children: \"主要风险:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                    lineNumber: 505,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                    className: \"text-sm space-y-1\",\n                                                                                    children: (_risk_主要风险 = risk.主要风险) === null || _risk_主要风险 === void 0 ? void 0 : _risk_主要风险.map((mainRisk, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                            className: \"flex items-start gap-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                                                    className: \"h-3 w-3 text-orange-500 mt-0.5 flex-shrink-0\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                                    lineNumber: 509,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                mainRisk\n                                                                                            ]\n                                                                                        }, i, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                            lineNumber: 508,\n                                                                                            columnNumber: 33\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                    lineNumber: 506,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                            lineNumber: 504,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                                                                    className: \"font-medium text-sm mb-2\",\n                                                                                    children: \"缓解措施:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                    lineNumber: 517,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                    className: \"text-sm space-y-1\",\n                                                                                    children: (_risk_缓解措施 = risk.缓解措施) === null || _risk_缓解措施 === void 0 ? void 0 : _risk_缓解措施.map((mitigation, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                            className: \"flex items-start gap-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                                    className: \"h-3 w-3 text-blue-500 mt-0.5 flex-shrink-0\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                                    lineNumber: 521,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                mitigation\n                                                                                            ]\n                                                                                        }, i, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                            lineNumber: 520,\n                                                                                            columnNumber: 33\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                    lineNumber: 518,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                            lineNumber: 516,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                    lineNumber: 503,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 21\n                                                    }, this);\n                                                })\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 17\n                                        }, this),\n                                        ((_solutionData_risk_assessment2 = solutionData.risk_assessment) === null || _solutionData_risk_assessment2 === void 0 ? void 0 : _solutionData_risk_assessment2.风险管理建议) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"风险管理建议:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                lineNumber: 539,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            Object.entries(solutionData.risk_assessment.风险管理建议).map((param)=>{\n                                                                let [priority, suggestions] = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                                                            className: \"font-medium flex items-center gap-2 mb-1\",\n                                                                            children: [\n                                                                                getPriorityIcon(priority.includes('高') ? '高' : priority.includes('一般') ? '中' : '低'),\n                                                                                priority,\n                                                                                \":\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                            lineNumber: 542,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                            className: \"text-sm space-y-1 ml-6\",\n                                                                            children: suggestions.map((suggestion, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    className: \"flex items-start gap-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"w-1 h-1 bg-blue-500 rounded-full mt-2 flex-shrink-0\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                            lineNumber: 549,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        suggestion\n                                                                                    ]\n                                                                                }, i, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                                    lineNumber: 548,\n                                                                                    columnNumber: 31\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                            lineNumber: 546,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, priority, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                    lineNumber: 541,\n                                                                    columnNumber: 25\n                                                                }, this);\n                                                            })\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this),\n            solutionData.综合建议 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 15\n                                }, this),\n                                \"综合建议与行动计划\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                            lineNumber: 569,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                        lineNumber: 568,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: Object.entries(solutionData.综合建议.执行优先级 || {}).map((param)=>{\n                                    let [priority, actions] = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        className: \"border-l-4 border-l-blue-500\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold mb-3\",\n                                                    children: priority\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2\",\n                                                    children: actions.map((action, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"text-sm flex items-start gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-3 w-3 text-green-500 mt-0.5 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                action\n                                                            ]\n                                                        }, i, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, priority, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                        lineNumber: 577,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                lineNumber: 575,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-blue-900 mb-2\",\n                                        children: \"成功关键因素\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                        lineNumber: 594,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-2\",\n                                        children: (_solutionData_综合建议_成功关键因素 = solutionData.综合建议.成功关键因素) === null || _solutionData_综合建议_成功关键因素 === void 0 ? void 0 : _solutionData_综合建议_成功关键因素.map((factor, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 text-sm text-blue-800\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_CheckCircle_Clock_Copy_ExternalLink_Search_Shield_Target_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-4 w-4 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                        lineNumber: 598,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    factor\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                                lineNumber: 593,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                        lineNumber: 574,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n                lineNumber: 567,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\comprehensive-solution-display.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n_s(ComprehensiveSolutionDisplay, \"U4Z1qjtVqM9EcGSPL6IUwVaCbeM=\");\n_c = ComprehensiveSolutionDisplay;\nvar _c;\n$RefreshReg$(_c, \"ComprehensiveSolutionDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/comprehensive-solution-display.tsx\n"));

/***/ })

});