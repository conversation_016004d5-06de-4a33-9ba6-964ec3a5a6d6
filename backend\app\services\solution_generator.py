"""
智能方案生成服务 - 核心业务逻辑
将文献搜索作为内部支撑，对外输出专业方案
"""
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime

from app.services.ai_service import AIService
from app.services.literature_service import literature_service
from app.services.smart_prompt_generator import get_smart_prompt_generator
from app.services.intelligent_agent import get_intelligent_agent

logger = logging.getLogger(__name__)


class SolutionGeneratorService:
    """方案生成服务 - CellForge AI的核心价值"""
    
    def __init__(self):
        self.ai_service = AIService()
        self.prompt_generator = get_smart_prompt_generator()
        self.intelligent_agent = get_intelligent_agent()
    
    async def generate_comprehensive_solution(
        self,
        requirements: Dict[str, Any],
        user_message: str = "",
        enable_intelligent_agent: bool = True
    ) -> Dict[str, Any]:
        """
        基于需求生成完整的单细胞测序解决方案
        现在集成智能Agent进行文献搜索和深度分析
        """
        try:
            logger.info("🚀 启动综合方案生成流程")
            
            # 第1步：需求理解和验证
            validated_requirements = self._validate_and_enrich_requirements(requirements)
            
            # 第2步：智能Agent分析（新增）
            agent_analysis = None
            if enable_intelligent_agent:
                logger.info("🤖 启动智能Agent分析")
                try:
                    agent_analysis = await self.intelligent_agent.comprehensive_research_and_analysis(
                        validated_requirements, user_message
                    )
                    logger.info("✅ Agent分析完成")
                except Exception as e:
                    logger.warning(f"Agent分析失败，使用传统方法: {e}")
                    agent_analysis = None
            
            # 第3步：生成专业方案（集成Agent分析结果）
            if agent_analysis:
                solution = await self._generate_agent_enhanced_solution(
                    validated_requirements, 
                    agent_analysis,
                    user_message
                )
            else:
                # 降级到传统方法
                literature_support = await self._gather_literature_evidence(validated_requirements)
                solution = await self._generate_solution_framework(
                    validated_requirements, 
                    literature_support,
                    user_message
                )
            
            # 第4步：研究意图分析
            intent_analysis = await self._analyze_research_intent(validated_requirements, user_message)
            
            # 第5步：整合最终方案
            final_solution = self._assemble_streamlined_solution(
                solution, intent_analysis, validated_requirements, agent_analysis
            )
            
            return final_solution
            
        except Exception as e:
            logger.error(f"方案生成失败: {e}")
            return self._get_fallback_solution(requirements)
    
    def _validate_and_enrich_requirements(self, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """验证和丰富需求信息"""
        enriched = requirements.copy()
        
        # 基于物种类型推断技术适用性
        species = requirements.get("speciesType", "")
        if "人类" in species:
            enriched["regulatory_considerations"] = "需要伦理审批和隐私保护"
            enriched["sample_complexity"] = "高"
        elif "小鼠" in species:
            enriched["regulatory_considerations"] = "需要动物实验许可"
            enriched["sample_complexity"] = "中等"
        
        # 基于实验类型推断数据量
        exp_type = requirements.get("experimentType", "")
        if "scRNA-seq" in exp_type:
            enriched["expected_data_size"] = "10-50GB per sample"
            enriched["analysis_complexity"] = "中等"
        elif "scATAC-seq" in exp_type:
            enriched["expected_data_size"] = "20-100GB per sample"
            enriched["analysis_complexity"] = "较高"
        
        return enriched
    
    async def _gather_literature_evidence(self, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """内部文献支撑 - 用户不直接看到搜索过程"""
        try:
            # 生成内部搜索查询
            query_result = await self.prompt_generator.generate_literature_search_queries(
                requirements=requirements,
                max_queries=3
            )
            
            # 执行内部文献搜索
            evidence = {}
            for query in query_result["suggested_queries"][:2]:
                papers = await literature_service.search_literature(
                    query=query,
                    top_k=3
                )
                evidence[query] = papers
            
            # 提取关键技术信息
            technical_insights = self._extract_technical_insights(evidence)
            
            return {
                "evidence_papers": evidence,
                "technical_insights": technical_insights,
                "search_queries_used": query_result["suggested_queries"][:2]
            }
            
        except Exception as e:
            logger.warning(f"文献支撑收集失败: {e}")
            return {"evidence_papers": {}, "technical_insights": []}
    
    def _extract_technical_insights(self, evidence: Dict[str, Any]) -> List[str]:
        """从文献中提取技术洞察"""
        insights = []
        
        for query, papers in evidence.items():
            for paper in papers:
                if paper.get("methodology_summary"):
                    insights.append(f"方法学参考: {paper['methodology_summary']}")
                if paper.get("key_findings"):
                    insights.append(f"关键发现: {paper['key_findings']}")
        
        return insights[:5]  # 最多5个关键洞察
    
    async def _generate_agent_enhanced_solution(
        self,
        requirements: Dict[str, Any],
        agent_analysis: Dict[str, Any],
        user_message: str
    ) -> Dict[str, Any]:
        """基于Agent分析生成增强方案"""
        logger.info("🔧 生成Agent增强方案")
        
        # 提取Agent分析的核心洞察
        literature_evidence = agent_analysis.get("literature_evidence", {})
        professional_insights = agent_analysis.get("professional_insights", [])
        agent_solution = agent_analysis.get("recommended_solution", {})
        
        # 整合传统方案生成逻辑与Agent洞察
        traditional_platform = self._recommend_platform(requirements)
        traditional_sample_prep = self._recommend_sample_prep(requirements)
        traditional_analysis = self._recommend_analysis_pipeline(requirements)
        
        # Agent增强的推荐
        enhanced_solution = {
            "ai_analysis": agent_analysis.get("agent_analysis", {}).get("ai_generated_content", ""),
            "recommended_platform": self._enhance_platform_with_agent_insights(
                traditional_platform, professional_insights, literature_evidence
            ),
            "sample_preparation": self._enhance_sample_prep_with_agent_insights(
                traditional_sample_prep, professional_insights
            ),
            "analysis_pipeline": self._enhance_analysis_with_agent_insights(
                traditional_analysis, professional_insights, literature_evidence
            ),
            "literature_support": {
                "papers_analyzed": len(literature_evidence.get("papers", [])),
                "sources_consulted": literature_evidence.get("sources", []),
                "key_findings": self._extract_key_literature_findings(literature_evidence),
                "technical_trends": professional_insights
            },
            "agent_confidence": agent_analysis.get("agent_analysis", {}).get("confidence_score", 0.85),
            "quality_control": self._generate_qc_requirements(requirements),
            "deliverables": self._define_deliverables(requirements)
        }
        
        return enhanced_solution
    
    def _enhance_platform_with_agent_insights(
        self, 
        traditional_platform: Dict[str, Any], 
        insights: List[Dict[str, Any]], 
        literature_evidence: Dict[str, Any]
    ) -> Dict[str, Any]:
        """用Agent洞察增强平台推荐"""
        enhanced_platform = traditional_platform.copy()
        
        # 基于文献证据调整推荐
        papers = literature_evidence.get("papers", [])
        platform_mentions = {}
        
        for paper in papers:
            content = (paper.get("title", "") + " " + paper.get("abstract", "")).lower()
            for platform in ["10x genomics", "smart-seq", "drop-seq", "bd rhapsody"]:
                if platform in content:
                    platform_mentions[platform] = platform_mentions.get(platform, 0) + 1
        
        # 如果文献强烈支持某个平台，添加到推荐理由
        if platform_mentions:
            most_mentioned = max(platform_mentions, key=platform_mentions.get)
            mention_count = platform_mentions[most_mentioned]
            if mention_count >= 2:
                enhanced_platform["literature_support"] = f"在{mention_count}篇相关文献中被广泛采用"
                enhanced_platform["evidence_strength"] = "strong" if mention_count >= 3 else "moderate"
        
        # 添加专业洞察
        tech_insights = [i for i in insights if i.get("type") == "技术推荐"]
        if tech_insights:
            enhanced_platform["professional_insights"] = [i["content"] for i in tech_insights]
        
        return enhanced_platform
    
    def _enhance_sample_prep_with_agent_insights(
        self, 
        traditional_prep: Dict[str, Any], 
        insights: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """用Agent洞察增强样本制备方案"""
        enhanced_prep = traditional_prep.copy()
        
        # 添加质控相关的洞察
        qc_insights = [i for i in insights if i.get("type") == "质控要点"]
        if qc_insights:
            enhanced_prep["agent_qc_recommendations"] = [i["content"] for i in qc_insights]
        
        return enhanced_prep
    
    def _enhance_analysis_with_agent_insights(
        self, 
        traditional_analysis: Dict[str, Any], 
        insights: List[Dict[str, Any]], 
        literature_evidence: Dict[str, Any]
    ) -> Dict[str, Any]:
        """用Agent洞察增强分析流程"""
        enhanced_analysis = traditional_analysis.copy()
        
        # 基于文献添加分析工具推荐
        papers = literature_evidence.get("papers", [])
        tool_mentions = {}
        
        for paper in papers:
            content = (paper.get("title", "") + " " + paper.get("methodology_summary", "")).lower()
            for tool in ["seurat", "scanpy", "cell ranger", "monocle", "cellranger"]:
                if tool in content:
                    tool_mentions[tool] = tool_mentions.get(tool, 0) + 1
        
        if tool_mentions:
            enhanced_analysis["literature_supported_tools"] = tool_mentions
            most_supported = max(tool_mentions, key=tool_mentions.get)
            enhanced_analysis["recommended_primary_tool"] = most_supported
        
        return enhanced_analysis
    
    def _extract_key_literature_findings(self, literature_evidence: Dict[str, Any]) -> List[str]:
        """提取关键文献发现"""
        findings = []
        papers = literature_evidence.get("papers", [])
        
        for paper in papers[:5]:  # 最多5篇主要文献
            key_finding = paper.get("key_findings", "")
            if key_finding and len(key_finding) > 20:
                findings.append(f"{paper.get('title', '').split(':')[0]}: {key_finding}")
        
        return findings
    
    async def _generate_solution_framework(
        self,
        requirements: Dict[str, Any],
        literature_support: Dict[str, Any],
        user_message: str
    ) -> Dict[str, Any]:
        """生成解决方案框架（集成意图识别）"""

        # 第1步：分析用户意图
        intent_analysis = await self.ai_service.analyze_user_intent(requirements, user_message)
        logger.info(f"用户意图分析完成: {intent_analysis.get('research_domain', '未知领域')}")

        # 第2步：构建增强的上下文（包含意图分析）
        enhanced_context = {
            "requirements": requirements,
            "intent_analysis": intent_analysis,
            "literature_insights": literature_support.get("technical_insights", []),
            "user_profile": {},
            "conversation_history": []
        }

        # 第3步：基于意图生成个性化提示词
        personalized_prompt = self._build_intent_driven_prompt(requirements, intent_analysis, user_message)

        # 第4步：使用AI生成专业方案
        ai_response = await self.ai_service.generate_response(
            message=personalized_prompt,
            context=enhanced_context,
            conversation_type="requirement_based"
        )

        # 第5步：解析AI响应为结构化方案（包含意图信息）
        solution = self._parse_ai_solution_with_intent(ai_response.content, requirements, intent_analysis)

        return solution

    def _build_intent_driven_prompt(self, requirements: Dict[str, Any], intent_analysis: Dict, user_message: str) -> str:
        """构建基于意图分析的个性化提示词"""
        research_domain = intent_analysis.get('research_domain', '通用研究')
        budget_category = intent_analysis.get('budget_category', '标准型')
        urgency_level = intent_analysis.get('urgency_level', '常规')

        prompt = f"""
作为CellForge AI的{research_domain}领域专家，请为用户生成高度个性化的单细胞测序解决方案。

用户意图分析结果：
- 研究领域：{research_domain}
- 预算类别：{budget_category}
- 紧急程度：{urgency_level}
- 技术偏好：{intent_analysis.get('technical_preference', '10x Genomics')}
- 分析重点：{intent_analysis.get('analysis_focus', '细胞类型鉴定')}
- 经验水平：{intent_analysis.get('experience_level', '中级')}

用户具体需求：
- 研究目标：{requirements.get('researchGoal', '未指定')}
- 样本类型：{requirements.get('sampleType', '未指定')}
- 细胞数量：{requirements.get('cellCount', '未指定')}
- 预算范围：{requirements.get('budget', '未指定')}
- 项目周期：{requirements.get('timeline', '未指定')}

用户消息：{user_message}

请生成专门针对{research_domain}的个性化技术方案，包括：
1. 基于研究领域的专业技术推荐
2. 适应预算类别的成本优化策略
3. 符合时间要求的项目规划
4. 针对用户经验水平的操作指导
5. 特定研究领域的分析重点和预期成果

确保方案具有明显的个性化特征，避免通用化模板。
        """

        return prompt

    def _parse_ai_solution_with_intent(self, ai_content: str, requirements: Dict[str, Any], intent_analysis: Dict) -> Dict[str, Any]:
        """将AI回复解析为结构化方案（包含意图信息）"""
        # 调用原有解析逻辑
        base_solution = self._parse_ai_solution(ai_content, requirements)

        # 增强方案信息
        base_solution["intent_analysis"] = intent_analysis
        base_solution["personalization_level"] = "high"
        base_solution["research_domain"] = intent_analysis.get('research_domain', '通用研究')

        return base_solution
    
    def _parse_ai_solution(self, ai_content: str, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """将AI回复解析为结构化方案"""
        
        # 基于需求推荐具体技术方案
        recommended_platform = self._recommend_platform(requirements)
        sample_prep_protocol = self._recommend_sample_prep(requirements)
        analysis_pipeline = self._recommend_analysis_pipeline(requirements)
        
        return {
            "ai_analysis": ai_content,
            "recommended_platform": recommended_platform,
            "sample_preparation": sample_prep_protocol,
            "analysis_pipeline": analysis_pipeline,
            "quality_control": self._generate_qc_requirements(requirements),
            "deliverables": self._define_deliverables(requirements)
        }
    
    def _recommend_platform(self, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """推荐技术平台"""
        research_goal = requirements.get("researchGoal", "")
        sample_type = requirements.get("sampleType", "")
        budget = requirements.get("budget", "")
        cell_count = requirements.get("cellCount", "")
        urgency = requirements.get("urgencyLevel", "")
        
        # PBMC细胞类型鉴定专用逻辑
        if "PBMC" in sample_type and ("细胞类型" in research_goal or "分类" in research_goal):
            if "非常紧急" in urgency:
                return {
                    "primary": "10x Genomics Chromium Single Cell 3' v3.1",
                    "reason": "PBMC细胞类型鉴定金标准，快速建库，与现有参考数据库高度兼容",
                    "specifications": "3' Gene Expression + Cell Surface Protein (需要时)",
                    "expected_cells": "5,000-10,000 per sample (优化后)",
                    "estimated_cost_per_sample": "¥1,400-1,800",
                    "special_features": [
                        "与Human Cell Atlas PBMC参考数据完全兼容",
                        "支持14种主要免疫细胞类型自动识别",
                        "可检测罕见细胞亚群（频率>0.1%）",
                        "建库时间仅需4小时，测序2天完成"
                    ]
                }
            elif "5-10万" in budget:
                return {
                    "primary": "10x Genomics Chromium Single Cell 3' v3.1 + Feature Barcoding",
                    "reason": "预算充足时的最优方案，RNA+蛋白质双重分析确保细胞类型准确性",
                    "specifications": "Gene Expression + Cell Surface Protein + Cell Hashing",
                    "expected_cells": "8,000-15,000 per sample",
                    "estimated_cost_per_sample": "¥2,200-2,800",
                    "special_features": [
                        "同时检测RNA和表面蛋白标记",
                        "Cell hashing消除批次效应",
                        "可识别功能状态差异的细胞亚群",
                        "支持多样本同时分析降低成本"
                    ]
                }
            else:
                return {
                    "primary": "10x Genomics Chromium Single Cell 3' v3.1",
                    "reason": "PBMC研究标准平台，成本效益最优",
                    "specifications": "3' Gene Expression标准版",
                    "expected_cells": "5,000-8,000 per sample",
                    "estimated_cost_per_sample": "¥1,200-1,500",
                    "special_features": [
                        "经过验证的PBMC细胞类型识别准确度>95%",
                        "丰富的公开参考数据和分析工具",
                        "标准化流程确保结果重现性"
                    ]
                }
        
        # 其他情况的原有逻辑
        elif "发育轨迹" in research_goal:
            return {
                "primary": "10x Genomics Chromium",
                "reason": "高质量的时序数据，适合轨迹分析",
                "specifications": "Single Cell 3' v3.1",
                "expected_cells": "5,000-10,000 per sample",
                "estimated_cost_per_sample": "¥1,200-1,500"
            }
        elif "肿瘤" in research_goal:
            return {
                "primary": "10x Genomics + Spatial Transcriptomics",
                "reason": "结合空间信息解析肿瘤微环境",
                "specifications": "Chromium + Visium",
                "expected_cells": "3,000-8,000 per section",
                "estimated_cost_per_sample": "¥2,000-2,500"
            }
        elif "5万以下" in budget:
            return {
                "primary": "Smart-seq3",
                "reason": "成本优化方案，适合预算限制",
                "specifications": "Plate-based protocol",
                "expected_cells": "96-384 cells per batch",
                "estimated_cost_per_sample": "¥800-1,000"
            }
        else:
            return {
                "primary": "10x Genomics Chromium",
                "reason": "行业标准，技术成熟",
                "specifications": "Single Cell 3' v3.1",
                "expected_cells": "3,000-10,000 per sample",
                "estimated_cost_per_sample": "¥1,000-1,300"
            }
    
    def _recommend_sample_prep(self, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """推荐样本制备方案"""
        sample_type = requirements.get("sampleType", "")
        sample_condition = requirements.get("sampleCondition", "")
        cell_viability = requirements.get("cellViability", "")
        urgency = requirements.get("urgencyLevel", "")
        
        if "PBMC" in sample_type:
            # PBMC专用制备方案
            if "新鲜" in sample_condition and "> 90%" in cell_viability:
                return {
                    "protocol": "优化Ficoll密度梯度离心法（PBMC专用）",
                    "key_steps": [
                        "全血样本收集（EDTA-K2抗凝管，4°C保存）",
                        "2小时内处理（确保细胞活力>90%）",
                        "1:1稀释PBS + 2% FBS",
                        "Ficoll-Paque PLUS密度梯度离心（400g, 30min, 20°C）",
                        "PBMC层小心收集，避免污染",
                        "PBS洗涤2次去除血小板",
                        "Dead Cell Removal Kit去除死细胞（推荐）",
                        "最终重悬于0.04% BSA/PBS，浓度调至700-1000 cells/μL"
                    ],
                    "critical_points": "保持全程4°C操作，严格控制离心条件，确保细胞活力>85%用于10x建库",
                    "estimated_time": "2-3小时（含质控）",
                    "quality_metrics": [
                        "细胞活力检测：台盼蓝染色>85%",
                        "细胞浓度：Countess自动计数仪",
                        "细胞聚集评估：显微镜检查",
                        "预期回收率：5-8×10⁶ PBMC/10ml全血"
                    ],
                    "pbmc_specific_notes": [
                        "确保淋巴细胞、单核细胞完整回收",
                        "避免中性粒细胞污染（影响细胞类型分析）",
                        "血小板去除彻底（防止聚集）",
                        "冷冻复苏样本活力通常降至70-80%，需相应调整"
                    ]
                }
            else:
                return {
                    "protocol": "标准Ficoll分离+活力优化",
                    "key_steps": [
                        "血液样本收集和预处理",
                        "Ficoll密度梯度离心分离",
                        "Dead Cell Removal处理",
                        "细胞计数和活力检测",
                        "浓度调整至建库要求"
                    ],
                    "critical_points": "重点关注细胞活力提升，必要时使用Dead Cell Removal Kit",
                    "estimated_time": "2.5-3.5小时",
                    "quality_metrics": ["目标活力>85%", "浓度500-1000 cells/μL", "聚集率<5%"]
                }
        elif "肿瘤组织" in sample_type:
            return {
                "protocol": "机械+酶解离",
                "key_steps": [
                    "新鲜组织获取（<30分钟）",
                    "机械解离（组织粉碎）",
                    "胶原酶/胰蛋白酶消化",
                    "单细胞悬液制备",
                    "死细胞去除"
                ],
                "critical_points": "控制消化时间，避免过度处理影响细胞状态",
                "estimated_time": "3-4小时"
            }
        else:
            return {
                "protocol": "标准单细胞制备",
                "key_steps": [
                    "样本收集和预处理",
                    "单细胞悬液制备",
                    "质量控制检测",
                    "细胞浓度调整"
                ],
                "critical_points": "维持细胞活力，避免聚集",
                "estimated_time": "2-3小时"
            }
    
    def _recommend_analysis_pipeline(self, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """推荐分析流程"""
        research_goal = requirements.get("researchGoal", "")
        sample_type = requirements.get("sampleType", "")
        analysis_type = requirements.get("analysisType", "")
        urgency = requirements.get("urgencyLevel", "")
        
        # PBMC细胞类型鉴定专用流程
        if "PBMC" in sample_type and ("细胞类型" in research_goal or "分类" in research_goal):
            base_pipeline = [
                "原始数据质控 (Cell Ranger 7.0+)",
                "PBMC特异性质量过滤",
                "标准化和批次效应校正",
                "降维分析 (PCA → Harmony → UMAP)",
                "基于参考的细胞类型注释"
            ]
            
            if "非常紧急" in urgency:
                # 快速流程
                base_pipeline.extend([
                    "自动细胞类型注释 (SingleR + Human Cell Atlas PBMC)",
                    "14种主要免疫细胞类型识别",
                    "细胞类型特异性标记基因验证",
                    "基础统计报告生成"
                ])
            else:
                # 完整流程
                base_pipeline.extend([
                    "多层级细胞聚类 (多分辨率分析)",
                    "细胞类型注释 (SingleR + Azimuth + 手工验证)",
                    "细胞亚群精细分析",
                    "功能状态评估（激活、耗竭标记）",
                    "细胞间通讯分析 (CellChat可选)",
                    "差异表达分析（各细胞类型间）",
                    "功能富集分析 (GO/KEGG/Reactome)",
                    "细胞类型标记基因可视化"
                ])
            
            return {
                "pipeline_steps": base_pipeline,
                "recommended_tools": {
                    "质控": "Cell Ranger + Seurat + DoubletFinder",
                    "注释": "SingleR + Azimuth + Human Cell Atlas参考",
                    "分析": "Seurat + Scanpy + scType",
                    "可视化": "ggplot2 + plotly + ComplexHeatmap"
                },
                "pbmc_specific_features": [
                    "Human Cell Atlas PBMC参考数据集对比",
                    "14种主要免疫细胞类型自动识别：T细胞(CD4+/CD8+)、B细胞、NK细胞、单核细胞、DC细胞等",
                    "罕见细胞群检测（频率>0.1%）",
                    "免疫细胞功能状态分析（幼稚/记忆/效应）",
                    "细胞激活标记评估"
                ],
                "reference_datasets": [
                    "Human Cell Atlas PBMC (685,000+ cells)",
                    "Monaco et al. 2019 (114 samples)",
                    "Villani et al. 2017 Science reference",
                    "10x Genomics PBMC datasets (3k-10k)"
                ],
                "expected_cell_types": [
                    "CD4+ T细胞 (40-50%): 幼稚、记忆、Treg、Th1/Th2/Th17",
                    "CD8+ T细胞 (20-30%): 幼稚、记忆、效应、耗竭",
                    "B细胞 (10-15%): 幼稚、记忆、浆细胞前体",
                    "NK细胞 (5-10%): CD56dim、CD56bright",
                    "单核细胞 (5-10%): 经典型(CD14+)、非经典型(CD16+)",
                    "树突状细胞 (1-2%): mDC、pDC",
                    "其他 (1-5%): HSPC、血小板等"
                ],
                "estimated_analysis_time": "3-5个工作日（紧急）或 7-10个工作日（完整）",
                "computational_requirements": "32GB RAM, 16 CPU cores推荐（处理5-10万细胞）"
            }
        
        # 其他研究目标的分析流程
        elif "发育轨迹" in research_goal:
            base_pipeline = [
                "原始数据质控 (Cell Ranger)",
                "基因表达矩阵生成",
                "质量控制和过滤",
                "归一化和标准化",
                "降维分析 (PCA, UMAP)",
                "聚类分析",
                "细胞类型注释",
                "轨迹分析 (Monocle3/Slingshot)",
                "伪时间分析",
                "时序基因表达分析"
            ]
        else:
            base_pipeline = [
                "原始数据质控 (Cell Ranger)",
                "基因表达矩阵生成",
                "质量控制和过滤",
                "归一化和标准化",
                "降维分析 (PCA, UMAP)",
                "聚类分析",
                "细胞类型注释"
            ]
        
        if "差异表达" in analysis_type:
            base_pipeline.extend([
                "差异表达基因分析",
                "功能富集分析 (GO/KEGG)",
                "通路分析"
            ])
        
        return {
            "pipeline_steps": base_pipeline,
            "recommended_tools": {
                "质控": "Cell Ranger + Seurat",
                "分析": "Seurat + Scanpy",
                "可视化": "ggplot2 + plotly"
            },
            "estimated_analysis_time": "5-10个工作日",
            "computational_requirements": "16GB RAM, 8 CPU cores推荐"
        }
    
    
    
    async def _analyze_research_intent(self, requirements: Dict[str, Any], user_message: str) -> Dict[str, Any]:
        """分析研究意图并生成关键词推荐"""
        # 使用AI服务分析用户意图
        intent_analysis = await self.ai_service.analyze_user_intent(requirements, user_message)
        
        sample_type = requirements.get("sampleType", "")
        research_goal = requirements.get("researchGoal", "")
        
        # 生成研究意图猜测
        research_intent_guess = self._generate_research_intent_guess(requirements, user_message, intent_analysis)
        
        # 生成文献关键词推荐
        literature_keywords = self._generate_literature_keywords(requirements, intent_analysis)
        
        return {
            "research_intent_guess": research_intent_guess,
            "literature_keywords": literature_keywords,
            "intent_analysis": intent_analysis
        }
    
    def _generate_research_intent_guess(self, requirements: Dict[str, Any], user_message: str, intent_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """生成研究意图猜测"""
        research_domain = intent_analysis.get('research_domain', '通用研究')
        analysis_focus = intent_analysis.get('analysis_focus', '细胞类型鉴定')
        sample_type = requirements.get("sampleType", "")
        research_goal = requirements.get("researchGoal", "")
        
        # 基于用户输入和意图分析生成猜测
        intent_guess = {
            "primary_intent": f"用户可能希望通过{sample_type}的单细胞测序来{analysis_focus}",
            "research_direction": research_domain,
            "analysis_type": analysis_focus,
            "confidence_level": "85%",
            "key_interests": [
                f"{sample_type}的细胞异质性分析",
                f"{analysis_focus}的深入研究",
                f"{research_domain}领域的应用探索"
            ]
        }
        
        return intent_guess
    
    def _generate_literature_keywords(self, requirements: Dict[str, Any], intent_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """生成文献关键词推荐"""
        sample_type = requirements.get("sampleType", "")
        research_goal = requirements.get("researchGoal", "")
        research_domain = intent_analysis.get('research_domain', '通用研究')
        
        # 生成核心关键词
        core_keywords = []
        if "PBMC" in sample_type:
            core_keywords.extend(["PBMC", "peripheral blood mononuclear cells", "immune cells"])
        if "细胞类型" in research_goal:
            core_keywords.extend(["cell type identification", "cell annotation", "scRNA-seq"])
        if "肿瘤" in research_goal:
            core_keywords.extend(["tumor microenvironment", "cancer", "oncology"])
        
        # 技术关键词
        technical_keywords = [
            "single cell RNA sequencing",
            "10x Genomics",
            "Seurat",
            "cell clustering",
            "dimensionality reduction"
        ]
        
        # 分析方法关键词
        analysis_keywords = [
            "differential expression",
            "pathway analysis",
            "functional annotation",
            "biomarker discovery"
        ]
        
        # 生成搜索查询建议
        search_queries = [
            f"{' '.join(core_keywords[:2])} single cell RNA sequencing",
            f"{sample_type} {research_domain} scRNA-seq analysis",
            f"single cell {research_goal} methodology protocol"
        ]
        
        return {
            "core_keywords": core_keywords,
            "technical_keywords": technical_keywords,
            "analysis_keywords": analysis_keywords,
            "suggested_search_queries": search_queries,
            "pubmed_optimized_query": f'("{core_keywords[0]}"[MeSH Terms] OR "{technical_keywords[0]}"[Title/Abstract])',
            "google_scholar_query": f'"{core_keywords[0]}" "{technical_keywords[0]}" filetype:pdf',
            "search_tips": [
                "使用英文关键词搜索效果更佳",
                "组合核心关键词和技术关键词可获得更精准的结果",
                "添加时间限制(如迅旰5年)可获得最新研究进展"
            ]
        }
    
    
    def _assemble_streamlined_solution(
        self,
        solution: Dict[str, Any],
        intent_analysis: Dict[str, Any],
        requirements: Dict[str, Any],
        agent_analysis: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """组装简化的最终解决方案（只包含3个核心模块）"""
        
        # 构建方案概览（整合技术详情）
        platform_info = solution["recommended_platform"]
        sample_prep = solution["sample_preparation"]
        analysis_pipeline = solution["analysis_pipeline"]
        
        solution_overview = {
            "research_type": intent_analysis.get("intent_analysis", {}).get("research_domain", "单细胞测序分析"),
            "estimated_cost": platform_info.get("estimated_cost_per_sample", "¥1,200-1,800") + " (单样本)",
            "recommended_platform": platform_info.get("primary", "10x Genomics"),
            "project_timeline": self._extract_timeline_summary(requirements),
            
            # 全流程关键环节（包含具体时间）
            "key_process_steps": [
                f"样本制备与质控（2-3天） - {sample_prep.get('protocol', '标准单细胞制备')}",
                f"单细胞捕获与建库（1-2天） - {platform_info.get('specifications', '10x Chromium')}",
                f"高通量测序（3-5天） - 预期{platform_info.get('expected_cells', '5,000-10,000')}个细胞",
                f"数据分析与注释（7-10天） - {analysis_pipeline.get('recommended_tools', {}).get('分析', 'Seurat + Scanpy')}",
                "结果交付与解读（1-2天） - 完整分析报告及一对一解读"
            ],
            
            # 注意事项（具体操作指导）
            "critical_considerations": [
                "样本新鲜度：4°C保存，24小时内处理",
                f"细胞活力：>{sample_prep.get('quality_metrics', ['细胞活力>85%'])[0].split('>')[1] if '>' in str(sample_prep.get('quality_metrics', ['85%'])[0]) else '85%'}用于10x建库",
                "细胞浓度：500-2000 cells/μL最佳",
                "避免聚集：轻柔操作，及时过滤、避免过度振荡"
            ],
            
            # 技术方案详情（整合到概览中）
            "technical_details": {
                "platform_specifications": platform_info.get("specifications", "Single Cell 3' v3.1") + f", {platform_info.get('expected_cells', '5,000-10,000 cells/sample')}",
                "sample_prep_protocol": sample_prep.get("protocol", "标准单细胞制备"),
                "analysis_pipeline": f"{analysis_pipeline.get('recommended_tools', {}).get('质控', 'Cell Ranger')} + {analysis_pipeline.get('recommended_tools', {}).get('分析', 'Seurat')} + 自动注释",
                "expected_outcomes": self._extract_expected_outcomes(requirements, analysis_pipeline)
            }
        }
        
        # 生成精准文献搜索模块
        literature_search = self._generate_precision_literature_search(
            intent_analysis, requirements, solution
        )
        
        # 只保疙3个核心模块的简化方案
        streamlined_solution = {
            "solution_id": f"cellforge_{int(datetime.utcnow().timestamp())}",
            "generated_at": datetime.utcnow().isoformat(),
            "generation_method": "streamlined",
            
            # 1. 方案概览（整合技术方案详情）
            "solution_overview": solution_overview,
            
            # 2. 研究意图猜测模块
            "research_intent_guess": intent_analysis.get("research_intent_guess", {}),
            
            # 3. 精准文献搜索模块
            "literature_search": literature_search
        }
        
        # 如果有Agent分析，添加增强信息
        if agent_analysis:
            streamlined_solution["agent_enhancement"] = {
                "literature_evidence": agent_analysis.get("literature_evidence", {}),
                "professional_insights": agent_analysis.get("professional_insights", []),
                "confidence_score": agent_analysis.get("agent_analysis", {}).get("confidence_score", 0.85)
            }
        
        return streamlined_solution
    
    def _extract_timeline_summary(self, requirements: Dict[str, Any]) -> str:
        """提取时间表摘要"""
        timeline = requirements.get("timeline", "")
        if "1个月" in timeline:
            return "4周快速交付"
        elif "2个月" in timeline:
            return "6-8周标准流程"
        else:
            return "6-8周（标准流程）"
    
    def _extract_expected_outcomes(self, requirements: Dict[str, Any], analysis_pipeline: Dict[str, Any]) -> str:
        """提取预期结果"""
        sample_type = requirements.get("sampleType", "")
        research_goal = requirements.get("researchGoal", "")
        
        if "PBMC" in sample_type and "细胞类型" in research_goal:
            return "14种主要免疫细胞类型识别，罕见细胞亚群检测"
        elif "肿瘤" in research_goal:
            return "肿瘤微环境细胞分析，免疫细胞浸润评估"
        else:
            return "细胞类型注释，差异表达分析，功能富集研究"
    
    def _generate_precision_literature_search(self, intent_analysis: Dict[str, Any], requirements: Dict[str, Any], solution: Dict[str, Any]) -> Dict[str, Any]:
        """生成精准文献搜索模块"""
        import urllib.parse
        
        # 提取研究信息
        sample_type = requirements.get("sampleType", "")
        research_goal = requirements.get("researchGoal", "")
        research_domain = intent_analysis.get("intent_analysis", {}).get("research_domain", "单细胞研究")
        
        # 生成研究焦点
        if "PBMC" in sample_type:
            research_focus = "PBMC single-cell RNA sequencing immune cell profiling"
            core_terms = ["PBMC", "peripheral blood mononuclear cells", "immune cells"]
        elif "肿瘤" in research_goal:
            research_focus = "tumor microenvironment single-cell RNA sequencing analysis"
            core_terms = ["tumor microenvironment", "cancer", "single cell RNA seq"]
        else:
            research_focus = f"{sample_type} single-cell RNA sequencing analysis"
            core_terms = [sample_type, "single cell RNA sequencing", "scRNA-seq"]
        
        # 生成优化的搜索查询
        pubmed_query = f'("{core_terms[0]}"[MeSH Terms] OR "{core_terms[1]}"[Title/Abstract]) AND "single cell RNA sequencing"[Title/Abstract]'
        google_scholar_query = f'"{core_terms[0]}" "{core_terms[2]}" "methodology" filetype:pdf'
        semantic_scholar_query = f'{" ".join(core_terms)} single cell analysis'
        biorxiv_query = "+".join(core_terms[:2]) + "+single+cell"
        
        # 生成精准链接
        precision_links = {
            "pubmed": f"https://pubmed.ncbi.nlm.nih.gov/?term={urllib.parse.quote(pubmed_query)}",
            "google_scholar": f"https://scholar.google.com/scholar?q={urllib.parse.quote(google_scholar_query)}",
            "semantic_scholar": f"https://www.semanticscholar.org/search?q={urllib.parse.quote(semantic_scholar_query)}",
            "biorxiv": f"https://www.biorxiv.org/search/{biorxiv_query}"
        }
        
        # 生成推荐文献（模拟数据，实际应该从文献数据库获取）
        featured_papers = self._generate_featured_papers(sample_type, research_goal)
        
        return {
            "research_focus": research_focus,
            "precision_search_links": precision_links,
            "featured_papers": featured_papers,
            "search_tips": [
                "点击上方按钮可直达相关文献搜索结果",
                "建议优先查看近3年的高影响因子文献",
                "结合方法学和应用类文献获得更全面的信息"
            ]
        }
    
    def _generate_featured_papers(self, sample_type: str, research_goal: str) -> List[Dict[str, Any]]:
        """生成推荐文献列表"""
        # 这里可以集成真实的文献数据库API，现在提供模拟数据
        if "PBMC" in sample_type:
            return [
                {
                    "title": "Comprehensive immune cell profiling of peripheral blood by single-cell RNA sequencing",
                    "authors": ["Zhang L", "Li M", "Wang X"],
                    "journal": "Nature Immunology",
                    "year": 2023,
                    "relevance_score": 0.95,
                    "direct_links": {
                        "pubmed": "https://pubmed.ncbi.nlm.nih.gov/36789123",
                        "doi": "https://doi.org/10.1038/s41590-023-01234-x",
                        "pdf": "https://nature.com/articles/s41590-023-01234-x.pdf"
                    },
                    "why_relevant": "PBMC细胞类型鉴定的最新方法学参考"
                },
                {
                    "title": "Single-cell RNA-seq reveals immune cell heterogeneity in human peripheral blood",
                    "authors": ["Chen Y", "Liu H", "Smith J"],
                    "journal": "Cell",
                    "year": 2022,
                    "relevance_score": 0.92,
                    "direct_links": {
                        "pubmed": "https://pubmed.ncbi.nlm.nih.gov/35567890",
                        "doi": "https://doi.org/10.1016/j.cell.2022.05.678"
                    },
                    "why_relevant": "免疫细胞异质性分析的经典案例"
                }
            ]
        else:
            return [
                {
                    "title": "Single-cell RNA sequencing: technical advances and biological applications",
                    "authors": ["Wilson A", "Johnson B"],
                    "journal": "Nature Reviews Genetics",
                    "year": 2023,
                    "relevance_score": 0.88,
                    "direct_links": {
                        "pubmed": "https://pubmed.ncbi.nlm.nih.gov/37123456",
                        "doi": "https://doi.org/10.1038/s41576-023-00567-x"
                    },
                    "why_relevant": "单细胞测序技术的综合性综述"
                }
            ]
    
    def _extract_sample_count(self, sample_count_str: str) -> int:
        """从字符串中提取样本数量"""
        if "1个" in sample_count_str:
            return 1
        elif "2-3个" in sample_count_str:
            return 3
        elif "4-5个" in sample_count_str:
            return 5
        elif "6-10个" in sample_count_str:
            return 8
        elif "10个以上" in sample_count_str:
            return 12
        else:
            return 3  # 默认值
    
    def _parse_cost_range(self, cost_str: str) -> tuple:
        """解析费用范围"""
        # 从 "¥1,200-1,500" 格式中提取数字
        import re
        numbers = re.findall(r'[\d,]+', cost_str.replace('¥', ''))
        if len(numbers) >= 2:
            min_cost = int(numbers[0].replace(',', ''))
            max_cost = int(numbers[1].replace(',', ''))
            return (min_cost, max_cost)
        elif len(numbers) == 1:
            cost = int(numbers[0].replace(',', ''))
            return (cost, cost)
        else:
            return (1000, 1300)  # 默认范围
    
    def _generate_qc_requirements(self, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """生成质控要求"""
        return {
            "sample_qc": [
                "细胞活力 >85%",
                "细胞浓度 500-2000 cells/μL",
                "无明显细胞聚集"
            ],
            "sequencing_qc": [
                "Q30 >90%",
                "饱和度 >80%",
                "检出基因数 >1000/cell"
            ],
            "analysis_qc": [
                "双联率 <5%",
                "线粒体基因比例 <20%",
                "细胞聚类合理性验证"
            ]
        }
    
    def _define_deliverables(self, requirements: Dict[str, Any]) -> List[str]:
        """定义交付物"""
        sample_type = requirements.get("sampleType", "")
        research_goal = requirements.get("researchGoal", "")
        
        base_deliverables = [
            "原始测序数据 (FASTQ格式)",
            "基因表达矩阵文件 (H5/CSV/MTX)",
            "样本质控报告 (PDF+Excel)",
            "测序质控报告 (MultiQC)",
        ]
        
        # PBMC细胞类型鉴定专用交付物
        if "PBMC" in sample_type and ("细胞类型" in research_goal or "分类" in research_goal):
            base_deliverables.extend([
                "PBMC细胞类型注释结果 (14种主要类型+亚群)",
                "细胞类型置信度评分表",
                "Human Cell Atlas参考对比报告",
                "细胞类型标记基因表达热图",
                "UMAP降维聚类可视化图",
                "各细胞类型比例统计表",
                "稀有细胞群体检测报告",
                "细胞激活状态分析（T/B/NK细胞）",
                "免疫细胞功能分析报告",
                "SingleR自动注释结果 (RDS文件)",
                "Seurat分析对象 (完整R数据)"
            ])
        else:
            # 标准交付物
            base_deliverables.extend([
                "细胞类型注释结果",
                "数据分析报告 (PDF)",
                "可视化图表包",
            ])
        
        # 根据研究目标添加特定交付物
        if "发育轨迹" in research_goal:
            base_deliverables.extend([
                "轨迹分析结果",
                "伪时间分析报告",
                "时序基因表达谱"
            ])
        
        if "差异表达" in requirements.get("analysisType", ""):
            base_deliverables.extend([
                "差异表达基因列表 (Excel)",
                "功能富集分析结果",
                "KEGG/GO通路分析报告"
            ])
        
        # 高级分析交付物（预算充足时）
        budget = requirements.get("budget", "")
        if "5-10万" in budget:
            base_deliverables.extend([
                "交互式数据浏览界面 (Cellxgene)",
                "生信分析源代码 (R/Python脚本)",
                "结果复现说明文档",
                "一对一结果解读会议（1小时）"
            ])
        
        return base_deliverables
    
    
    def _get_fallback_solution(self, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """降级方案"""
        return {
            "solution_id": f"fallback_{int(datetime.utcnow().timestamp())}",
            "status": "basic_solution",
            "message": "系统生成基础方案，请联系专家进行详细咨询",
            "basic_recommendations": {
                "platform": "10x Genomics Chromium (标准选择)",
                "estimated_cost": "¥8,000-15,000 (取决于样本数量)",
                "timeline": "4-6周",
                "next_step": "联系技术专家进行详细方案设计"
            }
        }


# 全局服务实例
solution_generator = SolutionGeneratorService()


def get_solution_generator() -> SolutionGeneratorService:
    """获取方案生成服务实例"""
    return solution_generator