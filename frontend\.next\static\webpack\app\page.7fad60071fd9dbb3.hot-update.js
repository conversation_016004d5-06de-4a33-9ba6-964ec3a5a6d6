"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/formatted-message.tsx":
/*!******************************************!*\
  !*** ./components/formatted-message.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FormattedMessage: () => (/* binding */ FormattedMessage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Copy,ExternalLink,Info,Lightbulb!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Copy,ExternalLink,Info,Lightbulb!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Copy,ExternalLink,Info,Lightbulb!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Copy,ExternalLink,Info,Lightbulb!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Copy,ExternalLink,Info,Lightbulb!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Copy,ExternalLink,Info,Lightbulb!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _interactive_literature_citation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./interactive-literature-citation */ \"(app-pages-browser)/./components/interactive-literature-citation.tsx\");\n/* harmony import */ var _comprehensive_solution_display__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./comprehensive-solution-display */ \"(app-pages-browser)/./components/comprehensive-solution-display.tsx\");\n/* harmony import */ var _comprehensive_solution_framework__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./comprehensive-solution-framework */ \"(app-pages-browser)/./components/comprehensive-solution-framework.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction FormattedMessage(param) {\n    let { content, className = '' } = param;\n    _s();\n    // 渲染Mermaid图表的简化版本\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FormattedMessage.useEffect\": ()=>{\n            const renderMermaidDiagrams = {\n                \"FormattedMessage.useEffect.renderMermaidDiagrams\": ()=>{\n                    const diagrams = document.querySelectorAll('.mermaid-diagram[data-mermaid]:not(.mermaid-processed)');\n                    diagrams.forEach({\n                        \"FormattedMessage.useEffect.renderMermaidDiagrams\": (diagram)=>{\n                            const element = diagram;\n                            const mermaidCode = element.getAttribute('data-mermaid');\n                            if (!mermaidCode) return;\n                            // 标记为已处理\n                            element.classList.add('mermaid-processed');\n                            // 创建一个更好的展示界面\n                            element.innerHTML = '\\n          <div class=\"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200\">\\n            <div class=\"text-center mb-4\">\\n              <div class=\"inline-flex items-center gap-2 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium\">\\n                <svg class=\"h-4 w-4\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\\n                  <path d=\"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\"/>\\n                </svg>\\n                Mermaid 图表\\n              </div>\\n            </div>\\n            <div class=\"bg-white rounded border border-slate-200 p-4\">\\n              <div class=\"text-center text-slate-600 mb-3\">\\n                <div class=\"text-lg mb-2\">\\uD83D\\uDCCA</div>\\n                <div class=\"text-sm font-medium\">图表内容预览</div>\\n              </div>\\n              <pre class=\"text-xs text-slate-700 bg-slate-50 p-3 rounded border overflow-x-auto whitespace-pre-wrap\">'.concat(mermaidCode, '</pre>\\n              <div class=\"mt-3 text-center\">\\n                <div class=\"text-xs text-slate-500\">\\n                  \\uD83D\\uDCA1 提示：此图表包含 ').concat(mermaidCode.split('\\\\n').length, \" 行定义\\n                </div>\\n              </div>\\n            </div>\\n          </div>\\n        \");\n                        }\n                    }[\"FormattedMessage.useEffect.renderMermaidDiagrams\"]);\n                }\n            }[\"FormattedMessage.useEffect.renderMermaidDiagrams\"];\n            // 延迟处理，确保DOM已更新\n            const timer = setTimeout(renderMermaidDiagrams, 100);\n            return ({\n                \"FormattedMessage.useEffect\": ()=>clearTimeout(timer)\n            })[\"FormattedMessage.useEffect\"];\n        }\n    }[\"FormattedMessage.useEffect\"], [\n        content\n    ]);\n    // 处理内联格式（粗体、斜体、代码、链接等）\n    const formatInlineText = (text)=>{\n        // 如果没有特殊格式，直接返回\n        if (!text.includes('**') && !text.includes('*') && !text.includes('`') && !text.includes('~~') && !text.includes('[')) {\n            return [\n                text\n            ];\n        }\n        const parts = [];\n        let key = 0;\n        // 改进的正则表达式，更精确地处理markdown格式\n        // 优先匹配较长的格式（如 *** 和 **），避免误匹配，并支持链接\n        const formatRegex = RegExp('(\\\\[([^\\\\]]+?)\\\\]\\\\(([^)]+?)(?:\\\\s+\"([^\"]*)\")?\\\\)|\\\\*\\\\*\\\\*([^*\\\\n]+?)\\\\*\\\\*\\\\*|\\\\*\\\\*([^*\\\\n]+?)\\\\*\\\\*|~~([^~\\\\n]+?)~~|(?<!\\\\*)\\\\*([^*\\\\n]+?)\\\\*(?!\\\\*)|`([^`\\\\n]+?)`)', \"g\");\n        let lastIndex = 0;\n        let match;\n        // 重置正则表达式的lastIndex\n        formatRegex.lastIndex = 0;\n        while((match = formatRegex.exec(text)) !== null){\n            // 添加格式前的普通文本\n            if (match.index > lastIndex) {\n                const plainText = text.slice(lastIndex, match.index);\n                if (plainText) {\n                    parts.push(plainText);\n                }\n            }\n            // 根据匹配的格式添加相应的元素\n            if (match[2] && match[3]) {\n                // 链接 [text](url) 或 [text](url \"title\")\n                const linkText = match[2];\n                const linkUrl = match[3];\n                const linkTitle = match[4] || '';\n                parts.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: linkUrl,\n                    title: linkTitle,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    className: \"text-blue-600 hover:text-blue-800 underline hover:no-underline transition-colors duration-200 font-medium inline-flex items-center gap-1\",\n                    children: [\n                        linkText,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-3 w-3 opacity-60\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, key++, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 11\n                }, this));\n            } else if (match[5]) {\n                // 粗体斜体 ***text***\n                parts.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                    className: \"font-bold italic text-slate-900\",\n                    children: match[5]\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 11\n                }, this));\n            } else if (match[6]) {\n                // 粗体 **text**\n                parts.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                    className: \"font-semibold text-slate-900\",\n                    children: match[6]\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 11\n                }, this));\n            } else if (match[7]) {\n                // 删除线 ~~text~~\n                parts.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"line-through text-slate-600\",\n                    children: match[7]\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 11\n                }, this));\n            } else if (match[8]) {\n                // 斜体 *text* (使用负向前瞻和后瞻避免与**冲突)\n                parts.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                    className: \"italic text-slate-800\",\n                    children: match[8]\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 11\n                }, this));\n            } else if (match[9]) {\n                // 代码 `text`\n                parts.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                    className: \"bg-slate-100 text-slate-800 px-1 py-0.5 rounded text-sm font-mono\",\n                    children: match[9]\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, this));\n            }\n            lastIndex = match.index + match[0].length;\n        }\n        // 添加剩余的普通文本\n        if (lastIndex < text.length) {\n            const remainingPlainText = text.slice(lastIndex);\n            if (remainingPlainText) {\n                parts.push(remainingPlainText);\n            }\n        }\n        return parts.length > 0 ? parts : [\n            text\n        ];\n    };\n    // 复制文本到剪贴板\n    const copyToClipboard = (text)=>{\n        navigator.clipboard.writeText(text).then(()=>{\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('已复制到剪贴板');\n        }).catch(()=>{\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('复制失败');\n        });\n    };\n    const formatContent = (text)=>{\n        const lines = text.split('\\n');\n        const elements = [];\n        let key = 0;\n        let inCodeBlock = false;\n        let codeBlockContent = [];\n        let codeBlockLanguage = '';\n        for(let i = 0; i < lines.length; i++){\n            const line = lines[i];\n            const trimmedLine = line.trim();\n            // 处理代码块\n            if (trimmedLine.startsWith('```')) {\n                if (!inCodeBlock) {\n                    // 开始代码块\n                    inCodeBlock = true;\n                    codeBlockLanguage = trimmedLine.slice(3).trim();\n                    codeBlockContent = [];\n                } else {\n                    // 结束代码块\n                    inCodeBlock = false;\n                    const codeContent = codeBlockContent.join('\\n');\n                    // 检查是否是综合解决方案或综合方案框架的JSON数据\n                    if (codeBlockLanguage === 'json') {\n                        try {\n                            const jsonData = JSON.parse(codeContent);\n                            if (jsonData.type === 'comprehensive_framework' && jsonData.data) {\n                                // 渲染综合方案框架组件（按设计文档要求）\n                                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_comprehensive_solution_framework__WEBPACK_IMPORTED_MODULE_6__.ComprehensiveSolutionFramework, {\n                                        frameworkData: jsonData.data,\n                                        onSearchClick: (platform, url)=>{\n                                            window.open(url, '_blank');\n                                        },\n                                        onOptimize: (feedback)=>{\n                                            console.log('优化反馈:', feedback);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 21\n                                    }, this)\n                                }, key++, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 19\n                                }, this));\n                            } else if (jsonData.type === 'comprehensive_solution' && jsonData.data) {\n                                // 渲染综合解决方案组件（降级方案）\n                                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_comprehensive_solution_display__WEBPACK_IMPORTED_MODULE_5__.ComprehensiveSolutionDisplay, {\n                                        solutionData: jsonData.data\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 21\n                                    }, this)\n                                }, key++, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 19\n                                }, this));\n                            } else {\n                                // 普通JSON代码块\n                                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-4 relative group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-slate-900 rounded-lg overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between px-4 py-2 bg-slate-800 border-b border-slate-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-slate-300 font-medium\",\n                                                        children: \"JSON\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>copyToClipboard(codeContent),\n                                                        className: \"h-6 px-2 text-slate-400 hover:text-white hover:bg-slate-700\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"p-4 text-sm text-slate-100 overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    children: codeContent\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 21\n                                    }, this)\n                                }, key++, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 19\n                                }, this));\n                            }\n                        } catch (jsonError) {\n                            // JSON解析失败，作为普通代码块处理\n                            elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"my-4 relative group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-900 rounded-lg overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between px-4 py-2 bg-slate-800 border-b border-slate-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-slate-300 font-medium\",\n                                                    children: \"JSON\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>copyToClipboard(codeContent),\n                                                    className: \"h-6 px-2 text-slate-400 hover:text-white hover:bg-slate-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                            className: \"p-4 text-sm text-slate-100 overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                children: codeContent\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 19\n                                }, this)\n                            }, key++, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 17\n                            }, this));\n                        }\n                    } else if (codeBlockLanguage === 'mermaid' || codeContent.trim().match(/^(graph|pie|gantt|sequenceDiagram|classDiagram|stateDiagram|journey|gitgraph|flowchart)/)) {\n                        elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white border border-slate-200 rounded-lg overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between px-4 py-2 bg-slate-50 border-b border-slate-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-slate-600 font-medium flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"h-4 w-4\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Mermaid 图表\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>copyToClipboard(codeContent),\n                                                className: \"h-6 px-2 text-slate-400 hover:text-slate-600\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mermaid-diagram bg-white rounded border border-slate-100 p-4 text-center min-h-[200px] flex flex-col items-center justify-center\",\n                                            \"data-mermaid\": codeContent,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-slate-500 text-sm mb-2\",\n                                                    children: \"\\uD83C\\uDFA8 正在渲染图表...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-slate-400 bg-slate-50 p-2 rounded text-left overflow-x-auto max-w-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                        className: \"whitespace-pre-wrap\",\n                                                        children: codeContent\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 17\n                            }, this)\n                        }, key++, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 15\n                        }, this));\n                    } else {\n                        // 普通代码块\n                        elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-4 relative group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-slate-900 rounded-lg overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between px-4 py-2 bg-slate-800 border-b border-slate-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-slate-300 font-medium\",\n                                                children: codeBlockLanguage || 'Code'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>copyToClipboard(codeContent),\n                                                className: \"h-6 px-2 text-slate-400 hover:text-white hover:bg-slate-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"p-4 text-sm text-slate-100 overflow-x-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            children: codeContent\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 17\n                            }, this)\n                        }, key++, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 15\n                        }, this));\n                    }\n                    codeBlockContent = [];\n                    codeBlockLanguage = '';\n                }\n                continue;\n            }\n            // 如果在代码块内，收集内容\n            if (inCodeBlock) {\n                codeBlockContent.push(line);\n                continue;\n            }\n            // Skip empty lines but add spacing\n            if (!trimmedLine) {\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-2\"\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 23\n                }, this));\n                continue;\n            }\n            // Markdown标题支持 (### ## #)\n            if (trimmedLine.match(/^#{1,6}\\s+(.+)/)) {\n                const match = trimmedLine.match(/^(#{1,6})\\s+(.+)/);\n                if (match) {\n                    const [, hashes, title] = match;\n                    const level = hashes.length;\n                    let className = \"\";\n                    let Component = \"h1\";\n                    switch(level){\n                        case 1:\n                            Component = \"h1\";\n                            className = \"text-2xl font-bold text-slate-900 mt-6 mb-4 pb-2 border-b border-slate-200\";\n                            break;\n                        case 2:\n                            Component = \"h2\";\n                            className = \"text-xl font-semibold text-slate-800 mt-5 mb-3 pb-1 border-b border-slate-100\";\n                            break;\n                        case 3:\n                            Component = \"h3\";\n                            className = \"text-lg font-semibold text-slate-800 mt-4 mb-2\";\n                            break;\n                        case 4:\n                            Component = \"h4\";\n                            className = \"text-base font-medium text-slate-700 mt-3 mb-2\";\n                            break;\n                        case 5:\n                            Component = \"h5\";\n                            className = \"text-sm font-medium text-slate-700 mt-2 mb-1\";\n                            break;\n                        case 6:\n                            Component = \"h6\";\n                            className = \"text-sm font-medium text-slate-600 mt-2 mb-1\";\n                            break;\n                    }\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                            className: className,\n                            children: formatInlineText(title)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 15\n                        }, this)\n                    }, key++, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 13\n                    }, this));\n                }\n                continue;\n            }\n            // Main title (🎯 at start)\n            if (trimmedLine.match(/^🎯\\s+(.+)/)) {\n                const title = trimmedLine.replace(/^🎯\\s+/, '');\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-xl font-bold text-blue-900 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"\\uD83C\\uDFAF\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: formatInlineText(title)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 13\n                    }, this)\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 399,\n                    columnNumber: 11\n                }, this));\n                continue;\n            }\n            // Section headers with emojis (📊, 🔬, 💰, ⏰, ⚠️, 🧬, 🏆, 🤝, etc.)\n            if (trimmedLine.match(/^[📊🔬💰⏰⚠️🎯📋📅⚡🛡️📞🔍💡🚀🧬🏆🤝📚📈]\\s+(.+)/)) {\n                const match = trimmedLine.match(/^([📊🔬💰⏰⚠️🎯📋📅⚡🛡️📞🔍💡🚀🧬🏆🤝📚📈])\\s+(.+)/);\n                if (match) {\n                    const [, emoji, title] = match;\n                    const isIntelligentFeature = [\n                        '🧬',\n                        '🏆',\n                        '🤝',\n                        '📚',\n                        '📈'\n                    ].includes(emoji);\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 mb-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold flex items-center gap-2 border-b pb-2 \".concat(isIntelligentFeature ? 'text-blue-800 border-blue-200 bg-blue-50 px-3 py-2 rounded-t-lg' : 'text-slate-800 border-slate-200'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl\",\n                                    children: emoji\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: formatInlineText(title)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 17\n                                }, this),\n                                isIntelligentFeature && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs bg-blue-600 text-white px-2 py-1 rounded-full ml-auto\",\n                                    children: \"AI智能推荐\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                            lineNumber: 417,\n                            columnNumber: 15\n                        }, this)\n                    }, key++, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 13\n                    }, this));\n                }\n                continue;\n            }\n            // Subsection headers (🌟, 💡, 🎯, etc.)\n            if (trimmedLine.match(/^[🌟💡🎯📅⚡]\\s+(.+):/)) {\n                const match = trimmedLine.match(/^([🌟💡🎯📅⚡])\\s+(.+):?/);\n                if (match) {\n                    const [, emoji, title] = match;\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-base font-medium text-slate-700 flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: emoji\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                    lineNumber: 444,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: formatInlineText(title)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                            lineNumber: 443,\n                            columnNumber: 15\n                        }, this)\n                    }, key++, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 13\n                    }, this));\n                }\n                continue;\n            }\n            // Markdown分隔线支持 (--- *** ___)\n            if (trimmedLine.match(/^(-{3,}|\\*{3,}|_{3,})$/)) {\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                        className: \"border-slate-300 border-t-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 13\n                    }, this)\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 456,\n                    columnNumber: 11\n                }, this));\n                continue;\n            }\n            // Separator lines (━━━)\n            if (trimmedLine.match(/^━+$/)) {\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                        className: \"border-slate-300 border-t-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 13\n                    }, this)\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 466,\n                    columnNumber: 11\n                }, this));\n                continue;\n            }\n            // Markdown无序列表支持 (- * +)\n            if (trimmedLine.match(/^[\\s]*[-\\*\\+]\\s+(.+)/)) {\n                const match = trimmedLine.match(/^(\\s*)([-\\*\\+])\\s+(.+)/);\n                if (match) {\n                    const [, indent, bullet, text] = match;\n                    const indentLevel = Math.floor(indent.length / 2) // 每2个空格为一级缩进\n                    ;\n                    const marginLeft = indentLevel * 20 + 16 // 基础16px + 每级20px\n                    ;\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-1 flex items-start gap-2\",\n                        style: {\n                            marginLeft: \"\".concat(marginLeft, \"px\")\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mt-0.5 text-slate-600 font-bold\",\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 483,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-slate-700 leading-relaxed\",\n                                children: formatInlineText(text)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, key++, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 482,\n                        columnNumber: 13\n                    }, this));\n                }\n                continue;\n            }\n            // Bullet points (• or ✅)\n            if (trimmedLine.match(/^[•✅]\\s+(.+)/)) {\n                const match = trimmedLine.match(/^([•✅])\\s+(.+)/);\n                if (match) {\n                    const [, bullet, text] = match;\n                    const isCheckmark = bullet === '✅';\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-4 mb-1 flex items-start gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mt-0.5 \".concat(isCheckmark ? 'text-green-600' : 'text-slate-600'),\n                                children: bullet\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 501,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-slate-700 leading-relaxed\",\n                                children: formatInlineText(text)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 504,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, key++, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 500,\n                        columnNumber: 13\n                    }, this));\n                }\n                continue;\n            }\n            // Numbered lists (1., 2., etc.)\n            if (trimmedLine.match(/^\\d+\\.\\s+(.+)/)) {\n                const match = trimmedLine.match(/^(\\d+)\\.\\s+(.+)/);\n                if (match) {\n                    const [, number, text] = match;\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-4 mb-2 flex items-start gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-blue-100 text-blue-800 text-sm font-medium px-2 py-0.5 rounded-full min-w-[24px] text-center\",\n                                children: number\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-slate-700 leading-relaxed\",\n                                children: formatInlineText(text)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, key++, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 517,\n                        columnNumber: 13\n                    }, this));\n                }\n                continue;\n            }\n            // Key-value pairs with colon (研究目标：xxx)\n            if (trimmedLine.includes('：') && !trimmedLine.match(/^[📊🔬💰⏰⚠️🎯📋📅⚡🛡️📞🔍💡🚀]/)) {\n                const parts = trimmedLine.split('：');\n                if (parts.length === 2) {\n                    const keyText = parts[0].trim();\n                    const value = parts[1].trim();\n                    // Special styling for different types of information\n                    const isImportant = keyText.includes('预算') || keyText.includes('费用') || keyText.includes('成本');\n                    const isTime = keyText.includes('时间') || keyText.includes('周期') || keyText.includes('阶段');\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2 flex items-start\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium min-w-[120px] \".concat(isImportant ? 'text-green-700' : isTime ? 'text-blue-700' : 'text-slate-800'),\n                                children: [\n                                    keyText,\n                                    \"：\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-slate-700 \".concat(isImportant ? 'font-medium text-green-800' : ''),\n                                children: formatInlineText(value)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 548,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, key++, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 540,\n                        columnNumber: 13\n                    }, this));\n                    continue;\n                }\n            }\n            // Cost/fee information (¥ symbol)\n            if (trimmedLine.includes('¥')) {\n                // Extract cost ranges and highlight them\n                const costPattern = /¥([\\d,.-]+)/g;\n                const parts = trimmedLine.split(costPattern);\n                const formattedParts = parts.map((part, index)=>{\n                    if (part.match(/^[\\d,.-]+$/)) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-semibold text-green-700 bg-green-50 px-1 rounded\",\n                            children: [\n                                \"\\xa5\",\n                                part\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                            lineNumber: 567,\n                            columnNumber: 15\n                        }, this);\n                    }\n                    return part;\n                });\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-1 text-slate-700\",\n                    children: formattedParts\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 576,\n                    columnNumber: 11\n                }, this));\n                continue;\n            }\n            // 表格支持 (| column1 | column2 |)\n            if (trimmedLine.includes('|') && trimmedLine.split('|').length >= 3) {\n                // 收集表格行\n                const tableRows = [\n                    trimmedLine\n                ];\n                let j = i + 1;\n                // 收集连续的表格行\n                while(j < lines.length){\n                    const nextLine = lines[j].trim();\n                    if (nextLine.includes('|') && nextLine.split('|').length >= 3) {\n                        tableRows.push(nextLine);\n                        j++;\n                    } else if (nextLine === '') {\n                        j++;\n                        break;\n                    } else {\n                        break;\n                    }\n                }\n                if (tableRows.length > 0) {\n                    const isHeaderSeparator = (row)=>/^[\\|\\s\\-:]+$/.test(row);\n                    let headerRow = null;\n                    let separatorIndex = -1;\n                    let dataRows = [];\n                    // 查找表头和分隔符\n                    for(let k = 0; k < tableRows.length; k++){\n                        if (isHeaderSeparator(tableRows[k])) {\n                            if (k > 0) {\n                                headerRow = tableRows[k - 1];\n                                separatorIndex = k;\n                            }\n                            break;\n                        }\n                    }\n                    // 如果找到分隔符，分离数据行\n                    if (separatorIndex >= 0) {\n                        dataRows = tableRows.slice(separatorIndex + 1);\n                    } else {\n                        // 没有分隔符，第一行作为表头，其余作为数据\n                        headerRow = tableRows[0];\n                        dataRows = tableRows.slice(1);\n                    }\n                    const parseTableRow = (row)=>{\n                        return row.split('|').map((cell)=>cell.trim()).filter((cell)=>cell !== '');\n                    };\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full border border-slate-200 rounded-lg overflow-hidden\",\n                            children: [\n                                headerRow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-slate-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: parseTableRow(headerRow).map((header, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-4 py-2 text-left text-sm font-medium text-slate-700 border-b border-slate-200\",\n                                                children: formatInlineText(header)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 25\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                    lineNumber: 639,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: dataRows.map((row, rowIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: rowIndex % 2 === 0 ? 'bg-white' : 'bg-slate-25',\n                                            children: parseTableRow(row).map((cell, cellIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-4 py-2 text-sm text-slate-700 border-b border-slate-100\",\n                                                    children: formatInlineText(cell)\n                                                }, cellIndex, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, rowIndex, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                            lineNumber: 651,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                    lineNumber: 649,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                            lineNumber: 637,\n                            columnNumber: 15\n                        }, this)\n                    }, key++, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 636,\n                        columnNumber: 13\n                    }, this));\n                    i = j - 1 // 跳过已处理的行\n                    ;\n                    continue;\n                }\n            }\n            // 引用块支持 (> text)\n            if (trimmedLine.startsWith('> ')) {\n                const quoteText = trimmedLine.slice(2);\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-3 pl-4 border-l-4 border-blue-300 bg-blue-50 py-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-slate-700 italic\",\n                        children: formatInlineText(quoteText)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 674,\n                        columnNumber: 13\n                    }, this)\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 673,\n                    columnNumber: 11\n                }, this));\n                continue;\n            }\n            // 警告和提示框增强\n            if (trimmedLine.match(/^[⚠️💡ℹ️✅]\\s+(.+)/)) {\n                const match = trimmedLine.match(/^([⚠️💡ℹ️✅])\\s+(.+)/);\n                if (match) {\n                    const [, emoji, text] = match;\n                    let bgColor, borderColor, textColor, icon;\n                    switch(emoji){\n                        case '⚠️':\n                            bgColor = 'bg-amber-50';\n                            borderColor = 'border-amber-200';\n                            textColor = 'text-amber-800';\n                            icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 692,\n                                columnNumber: 22\n                            }, this);\n                            break;\n                        case '💡':\n                            bgColor = 'bg-blue-50';\n                            borderColor = 'border-blue-200';\n                            textColor = 'text-blue-800';\n                            icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 698,\n                                columnNumber: 22\n                            }, this);\n                            break;\n                        case 'ℹ️':\n                            bgColor = 'bg-slate-50';\n                            borderColor = 'border-slate-200';\n                            textColor = 'text-slate-800';\n                            icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 704,\n                                columnNumber: 22\n                            }, this);\n                            break;\n                        case '✅':\n                            bgColor = 'bg-green-50';\n                            borderColor = 'border-green-200';\n                            textColor = 'text-green-800';\n                            icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Copy_ExternalLink_Info_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                lineNumber: 710,\n                                columnNumber: 22\n                            }, this);\n                            break;\n                        default:\n                            bgColor = 'bg-slate-50';\n                            borderColor = 'border-slate-200';\n                            textColor = 'text-slate-800';\n                            icon = null;\n                    }\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 rounded-lg mb-3 \".concat(bgColor, \" \").concat(borderColor, \" border\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium flex items-center gap-2 \".concat(textColor),\n                            children: [\n                                icon,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: formatInlineText(text)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                                    lineNumber: 723,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                            lineNumber: 721,\n                            columnNumber: 15\n                        }, this)\n                    }, key++, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 720,\n                        columnNumber: 13\n                    }, this));\n                }\n                continue;\n            }\n            // Signature line (CellForge AI - ...)\n            if (trimmedLine.includes('CellForge AI') && trimmedLine.includes('🧬')) {\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 pt-4 border-t border-slate-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center text-sm text-slate-500 italic\",\n                        children: trimmedLine\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 735,\n                        columnNumber: 13\n                    }, this)\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 734,\n                    columnNumber: 11\n                }, this));\n                continue;\n            }\n            // 检测文献引用格式 **[数字] 标题**\n            const literatureMatch = trimmedLine.match(/^\\*\\*\\[(\\d+)\\]\\s+(.+?)\\*\\*$/);\n            if (literatureMatch) {\n                const [, number, title] = literatureMatch;\n                // 收集后续的文献信息行\n                const literatureInfo = [\n                    trimmedLine\n                ];\n                let j = i + 1;\n                // 收集文献的详细信息（作者、期刊、DOI等）\n                while(j < lines.length){\n                    const nextLine = lines[j].trim();\n                    if (nextLine === '' || nextLine.startsWith('**[')) {\n                        break;\n                    }\n                    if (nextLine.startsWith('*') || nextLine.startsWith('📖') || nextLine.startsWith('🏆') || nextLine.startsWith('💡') || nextLine.startsWith('🔑') || nextLine.startsWith('🔗')) {\n                        literatureInfo.push(nextLine);\n                        j++;\n                    } else {\n                        break;\n                    }\n                }\n                // 解析文献信息\n                const literature = parseLiteratureInfo(literatureInfo);\n                if (literature) {\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_interactive_literature_citation__WEBPACK_IMPORTED_MODULE_4__.InteractiveLiteratureCitation, {\n                            literature: literature,\n                            relevanceExplanation: literature.relevanceExplanation,\n                            supportPoints: literature.supportPoints,\n                            showQualityIndicators: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                            lineNumber: 776,\n                            columnNumber: 15\n                        }, this)\n                    }, key++, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 775,\n                        columnNumber: 13\n                    }, this));\n                } else {\n                    // 如果解析失败，回退到普通文本显示\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-2 text-slate-700 leading-relaxed\",\n                        children: formatInlineText(trimmedLine)\n                    }, key++, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                        lineNumber: 787,\n                        columnNumber: 13\n                    }, this));\n                }\n                i = j - 1 // 跳过已处理的行\n                ;\n                continue;\n            }\n            // Regular paragraph text\n            if (trimmedLine.length > 0) {\n                // Check if it's a continuation of a list or section\n                const isIndented = line.startsWith('  ') || line.startsWith('\\t');\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-2 text-slate-700 leading-relaxed \".concat(isIndented ? 'ml-4 text-slate-600' : ''),\n                    children: formatInlineText(trimmedLine)\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n                    lineNumber: 803,\n                    columnNumber: 11\n                }, this));\n            }\n        }\n        return elements;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"formatted-message \".concat(className),\n        children: formatContent(content)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\formatted-message.tsx\",\n        lineNumber: 816,\n        columnNumber: 5\n    }, this);\n}\n_s(FormattedMessage, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = FormattedMessage;\n// 文献信息解析函数\nfunction parseLiteratureInfo(literatureInfo) {\n    try {\n        const titleLine = literatureInfo[0];\n        const titleMatch = titleLine.match(/^\\*\\*\\[(\\d+)\\]\\s+(.+?)\\*\\*$/);\n        if (!titleMatch) return null;\n        const [, number, title] = titleMatch;\n        // 初始化文献对象\n        const literature = {\n            id: parseInt(number),\n            title: title,\n            authors: [],\n            journal: '',\n            publication_year: new Date().getFullYear(),\n            category: 'unknown',\n            technology_tags: [],\n            application_tags: [],\n            citation_count: 0,\n            relevance_score: 0,\n            key_findings: '',\n            methodology_summary: '',\n            business_value: '',\n            abstract: '',\n            relevanceExplanation: '',\n            supportPoints: []\n        };\n        // 解析其他信息行\n        for(let i = 1; i < literatureInfo.length; i++){\n            const line = literatureInfo[i].trim();\n            // 作者和期刊信息 *作者等*\n            if (line.startsWith('*') && line.endsWith('*') && !line.includes('**')) {\n                const authorJournalMatch = line.match(/^\\*(.+?)\\*$/);\n                if (authorJournalMatch) {\n                    const authorJournalText = authorJournalMatch[1];\n                    const parts = authorJournalText.split('等');\n                    if (parts.length > 0) {\n                        literature.authors = parts[0].split(',').map((author)=>author.trim());\n                    }\n                }\n            } else if (line.startsWith('📖')) {\n                const journalMatch = line.match(/📖\\s+(.+?)\\s+\\((\\d{4})\\)/);\n                if (journalMatch) {\n                    literature.journal = journalMatch[1];\n                    literature.publication_year = parseInt(journalMatch[2]);\n                }\n            } else if (line.startsWith('🏆')) {\n                const ifMatch = line.match(/🏆\\s+影响因子:\\s*([\\d.]+)/);\n                if (ifMatch) {\n                    literature.impact_factor = parseFloat(ifMatch[1]);\n                }\n            } else if (line.startsWith('💡')) {\n                const relevanceMatch = line.match(/💡\\s+\\*\\*相关性\\*\\*:\\s*(.+)/);\n                if (relevanceMatch) {\n                    literature.relevanceExplanation = relevanceMatch[1];\n                }\n            } else if (line.startsWith('🔑')) {\n                const supportMatch = line.match(/🔑\\s+\\*\\*支持要点\\*\\*:\\s*(.+)/);\n                if (supportMatch) {\n                    literature.supportPoints.push(supportMatch[1]);\n                }\n            } else if (line.startsWith('🔗')) {\n                const doiMatch = line.match(/🔗\\s+DOI:\\s*(.+)/);\n                if (doiMatch) {\n                    literature.doi = doiMatch[1];\n                }\n            }\n        }\n        // 设置默认值\n        if (!literature.key_findings) {\n            literature.key_findings = \"\".concat(title, \"的重要研究发现\");\n        }\n        if (!literature.methodology_summary) {\n            literature.methodology_summary = '详细的方法学信息请查看原文';\n        }\n        if (!literature.business_value) {\n            literature.business_value = '为单细胞测序研究提供重要的理论和技术支持';\n        }\n        // 设置技术标签（基于标题推断）\n        const titleLower = title.toLowerCase();\n        if (titleLower.includes('single-cell') || titleLower.includes('scrna')) {\n            literature.technology_tags.push('scRNA-seq');\n        }\n        if (titleLower.includes('atac')) {\n            literature.technology_tags.push('scATAC-seq');\n        }\n        if (titleLower.includes('seurat')) {\n            literature.technology_tags.push('seurat');\n        }\n        if (titleLower.includes('10x')) {\n            literature.technology_tags.push('10x_genomics');\n        }\n        // 设置应用标签\n        if (titleLower.includes('immune') || titleLower.includes('dendritic')) {\n            literature.application_tags.push('immunology');\n        }\n        if (titleLower.includes('development')) {\n            literature.application_tags.push('development');\n        }\n        // 设置分类\n        if (titleLower.includes('integration') || titleLower.includes('method')) {\n            literature.category = 'methodology';\n        } else if (titleLower.includes('reveals') || titleLower.includes('analysis')) {\n            literature.category = 'application';\n        } else {\n            literature.category = 'technology';\n        }\n        // 估算引用数和相关性评分\n        if (literature.impact_factor) {\n            literature.citation_count = Math.round(literature.impact_factor * 100 + Math.random() * 1000);\n            literature.relevance_score = Math.min(0.95, literature.impact_factor / 50 + 0.5);\n        } else {\n            literature.citation_count = Math.round(Math.random() * 500 + 100);\n            literature.relevance_score = 0.7 + Math.random() * 0.2;\n        }\n        return literature;\n    } catch (error) {\n        console.error('解析文献信息失败:', error);\n        return null;\n    }\n}\nvar _c;\n$RefreshReg$(_c, \"FormattedMessage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvZm9ybWF0dGVkLW1lc3NhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXdDO0FBQ3NEO0FBQy9DO0FBQ2pCO0FBQ21EO0FBQ0Y7QUFDSTtBQVE1RSxTQUFTYSxpQkFBaUIsS0FBa0Q7UUFBbEQsRUFBRUMsT0FBTyxFQUFFQyxZQUFZLEVBQUUsRUFBeUIsR0FBbEQ7O0lBQy9CLG1CQUFtQjtJQUNuQmQsZ0RBQVNBO3NDQUFDO1lBQ1IsTUFBTWU7b0VBQXdCO29CQUM1QixNQUFNQyxXQUFXQyxTQUFTQyxnQkFBZ0IsQ0FBQztvQkFFM0NGLFNBQVNHLE9BQU87NEVBQUMsQ0FBQ0M7NEJBQ2hCLE1BQU1DLFVBQVVEOzRCQUNoQixNQUFNRSxjQUFjRCxRQUFRRSxZQUFZLENBQUM7NEJBRXpDLElBQUksQ0FBQ0QsYUFBYTs0QkFFbEIsU0FBUzs0QkFDVEQsUUFBUUcsU0FBUyxDQUFDQyxHQUFHLENBQUM7NEJBRXRCLGNBQWM7NEJBQ2RKLFFBQVFLLFNBQVMsR0FBRyw4NkJBa0JJSixPQUh1RkEsYUFBWSx3SkFHbkUsT0FBaENBLFlBQVlLLEtBQUssQ0FBQyxPQUFPQyxNQUFNLEVBQUM7d0JBTTFEOztnQkFDRjs7WUFFQSxnQkFBZ0I7WUFDaEIsTUFBTUMsUUFBUUMsV0FBV2YsdUJBQXVCO1lBQ2hEOzhDQUFPLElBQU1nQixhQUFhRjs7UUFDNUI7cUNBQUc7UUFBQ2hCO0tBQVE7SUFFWix1QkFBdUI7SUFDdkIsTUFBTW1CLG1CQUFtQixDQUFDQztRQUN4QixnQkFBZ0I7UUFDaEIsSUFBSSxDQUFDQSxLQUFLQyxRQUFRLENBQUMsU0FBUyxDQUFDRCxLQUFLQyxRQUFRLENBQUMsUUFBUSxDQUFDRCxLQUFLQyxRQUFRLENBQUMsUUFBUSxDQUFDRCxLQUFLQyxRQUFRLENBQUMsU0FBUyxDQUFDRCxLQUFLQyxRQUFRLENBQUMsTUFBTTtZQUNySCxPQUFPO2dCQUFDRDthQUFLO1FBQ2Y7UUFFQSxNQUFNRSxRQUEyQixFQUFFO1FBQ25DLElBQUlDLE1BQU07UUFFViw0QkFBNEI7UUFDNUIsb0NBQW9DO1FBQ3BDLE1BQU1DLGNBQWM7UUFDcEIsSUFBSUMsWUFBWTtRQUNoQixJQUFJQztRQUVKLG9CQUFvQjtRQUNwQkYsWUFBWUMsU0FBUyxHQUFHO1FBRXhCLE1BQU8sQ0FBQ0MsUUFBUUYsWUFBWUcsSUFBSSxDQUFDUCxLQUFJLE1BQU8sS0FBTTtZQUNoRCxhQUFhO1lBQ2IsSUFBSU0sTUFBTUUsS0FBSyxHQUFHSCxXQUFXO2dCQUMzQixNQUFNSSxZQUFZVCxLQUFLVSxLQUFLLENBQUNMLFdBQVdDLE1BQU1FLEtBQUs7Z0JBQ25ELElBQUlDLFdBQVc7b0JBQ2JQLE1BQU1TLElBQUksQ0FBQ0Y7Z0JBQ2I7WUFDRjtZQUVBLGlCQUFpQjtZQUNqQixJQUFJSCxLQUFLLENBQUMsRUFBRSxJQUFJQSxLQUFLLENBQUMsRUFBRSxFQUFFO2dCQUN4Qix1Q0FBdUM7Z0JBQ3ZDLE1BQU1NLFdBQVdOLEtBQUssQ0FBQyxFQUFFO2dCQUN6QixNQUFNTyxVQUFVUCxLQUFLLENBQUMsRUFBRTtnQkFDeEIsTUFBTVEsWUFBWVIsS0FBSyxDQUFDLEVBQUUsSUFBSTtnQkFFOUJKLE1BQU1TLElBQUksZUFDUiw4REFBQ0k7b0JBRUNDLE1BQU1IO29CQUNOSSxPQUFPSDtvQkFDUEksUUFBTztvQkFDUEMsS0FBSTtvQkFDSnRDLFdBQVU7O3dCQUVUK0I7c0NBQ0QsOERBQUMzQyxzSUFBWUE7NEJBQUNZLFdBQVU7Ozs7Ozs7bUJBUm5Cc0I7Ozs7O1lBV1gsT0FBTyxJQUFJRyxLQUFLLENBQUMsRUFBRSxFQUFFO2dCQUNuQixrQkFBa0I7Z0JBQ2xCSixNQUFNUyxJQUFJLGVBQ1IsOERBQUNTO29CQUFtQnZDLFdBQVU7OEJBQzNCeUIsS0FBSyxDQUFDLEVBQUU7bUJBREVIOzs7OztZQUlqQixPQUFPLElBQUlHLEtBQUssQ0FBQyxFQUFFLEVBQUU7Z0JBQ25CLGNBQWM7Z0JBQ2RKLE1BQU1TLElBQUksZUFDUiw4REFBQ1M7b0JBQW1CdkMsV0FBVTs4QkFDM0J5QixLQUFLLENBQUMsRUFBRTttQkFERUg7Ozs7O1lBSWpCLE9BQU8sSUFBSUcsS0FBSyxDQUFDLEVBQUUsRUFBRTtnQkFDbkIsZUFBZTtnQkFDZkosTUFBTVMsSUFBSSxlQUNSLDhEQUFDVTtvQkFBaUJ4QyxXQUFVOzhCQUN6QnlCLEtBQUssQ0FBQyxFQUFFO21CQURBSDs7Ozs7WUFJZixPQUFPLElBQUlHLEtBQUssQ0FBQyxFQUFFLEVBQUU7Z0JBQ25CLCtCQUErQjtnQkFDL0JKLE1BQU1TLElBQUksZUFDUiw4REFBQ1c7b0JBQWV6QyxXQUFVOzhCQUN2QnlCLEtBQUssQ0FBQyxFQUFFO21CQURGSDs7Ozs7WUFJYixPQUFPLElBQUlHLEtBQUssQ0FBQyxFQUFFLEVBQUU7Z0JBQ25CLFlBQVk7Z0JBQ1pKLE1BQU1TLElBQUksZUFDUiw4REFBQ1k7b0JBQWlCMUMsV0FBVTs4QkFDekJ5QixLQUFLLENBQUMsRUFBRTttQkFEQUg7Ozs7O1lBSWY7WUFFQUUsWUFBWUMsTUFBTUUsS0FBSyxHQUFHRixLQUFLLENBQUMsRUFBRSxDQUFDWCxNQUFNO1FBQzNDO1FBRUEsWUFBWTtRQUNaLElBQUlVLFlBQVlMLEtBQUtMLE1BQU0sRUFBRTtZQUMzQixNQUFNNkIscUJBQXFCeEIsS0FBS1UsS0FBSyxDQUFDTDtZQUN0QyxJQUFJbUIsb0JBQW9CO2dCQUN0QnRCLE1BQU1TLElBQUksQ0FBQ2E7WUFDYjtRQUNGO1FBRUEsT0FBT3RCLE1BQU1QLE1BQU0sR0FBRyxJQUFJTyxRQUFRO1lBQUNGO1NBQUs7SUFDMUM7SUFFQSxXQUFXO0lBQ1gsTUFBTXlCLGtCQUFrQixDQUFDekI7UUFDdkIwQixVQUFVQyxTQUFTLENBQUNDLFNBQVMsQ0FBQzVCLE1BQU02QixJQUFJLENBQUM7WUFDdkN0RCx5Q0FBS0EsQ0FBQ3VELE9BQU8sQ0FBQztRQUNoQixHQUFHQyxLQUFLLENBQUM7WUFDUHhELHlDQUFLQSxDQUFDeUQsS0FBSyxDQUFDO1FBQ2Q7SUFDRjtJQUVBLE1BQU1DLGdCQUFnQixDQUFDakM7UUFDckIsTUFBTWtDLFFBQVFsQyxLQUFLTixLQUFLLENBQUM7UUFDekIsTUFBTXlDLFdBQThCLEVBQUU7UUFDdEMsSUFBSWhDLE1BQU07UUFDVixJQUFJaUMsY0FBYztRQUNsQixJQUFJQyxtQkFBNkIsRUFBRTtRQUNuQyxJQUFJQyxvQkFBb0I7UUFFeEIsSUFBSyxJQUFJQyxJQUFJLEdBQUdBLElBQUlMLE1BQU12QyxNQUFNLEVBQUU0QyxJQUFLO1lBQ3JDLE1BQU1DLE9BQU9OLEtBQUssQ0FBQ0ssRUFBRTtZQUNyQixNQUFNRSxjQUFjRCxLQUFLRSxJQUFJO1lBRTdCLFFBQVE7WUFDUixJQUFJRCxZQUFZRSxVQUFVLENBQUMsUUFBUTtnQkFDakMsSUFBSSxDQUFDUCxhQUFhO29CQUNoQixRQUFRO29CQUNSQSxjQUFjO29CQUNkRSxvQkFBb0JHLFlBQVkvQixLQUFLLENBQUMsR0FBR2dDLElBQUk7b0JBQzdDTCxtQkFBbUIsRUFBRTtnQkFDdkIsT0FBTztvQkFDTCxRQUFRO29CQUNSRCxjQUFjO29CQUNkLE1BQU1RLGNBQWNQLGlCQUFpQlEsSUFBSSxDQUFDO29CQUUxQyw0QkFBNEI7b0JBQzVCLElBQUlQLHNCQUFzQixRQUFRO3dCQUNoQyxJQUFJOzRCQUNGLE1BQU1RLFdBQVdDLEtBQUtDLEtBQUssQ0FBQ0o7NEJBQzVCLElBQUlFLFNBQVNHLElBQUksS0FBSyw2QkFBNkJILFNBQVNJLElBQUksRUFBRTtnQ0FDaEUsc0JBQXNCO2dDQUN0QmYsU0FBU3hCLElBQUksZUFDWCw4REFBQ3dDO29DQUFnQnRFLFdBQVU7OENBQ3pCLDRFQUFDSCw2RkFBOEJBO3dDQUM3QjBFLGVBQWVOLFNBQVNJLElBQUk7d0NBQzVCRyxlQUFlLENBQUNDLFVBQWtCQzs0Q0FDaENDLE9BQU9DLElBQUksQ0FBQ0YsS0FBSzt3Q0FDbkI7d0NBQ0FHLFlBQVksQ0FBQ0M7NENBQ1hDLFFBQVFDLEdBQUcsQ0FBQyxTQUFTRjt3Q0FDdkI7Ozs7OzttQ0FSTXhEOzs7Ozs0QkFZZCxPQUFPLElBQUkyQyxTQUFTRyxJQUFJLEtBQUssNEJBQTRCSCxTQUFTSSxJQUFJLEVBQUU7Z0NBQ3RFLG1CQUFtQjtnQ0FDbkJmLFNBQVN4QixJQUFJLGVBQ1gsOERBQUN3QztvQ0FBZ0J0RSxXQUFVOzhDQUN6Qiw0RUFBQ0oseUZBQTRCQTt3Q0FBQ3FGLGNBQWNoQixTQUFTSSxJQUFJOzs7Ozs7bUNBRGpEL0M7Ozs7OzRCQUlkLE9BQU87Z0NBQ0wsWUFBWTtnQ0FDWmdDLFNBQVN4QixJQUFJLGVBQ1gsOERBQUN3QztvQ0FBZ0J0RSxXQUFVOzhDQUN6Qiw0RUFBQ3NFO3dDQUFJdEUsV0FBVTs7MERBQ2IsOERBQUNzRTtnREFBSXRFLFdBQVU7O2tFQUNiLDhEQUFDd0M7d0RBQUt4QyxXQUFVO2tFQUFxQzs7Ozs7O2tFQUNyRCw4REFBQ1AseURBQU1BO3dEQUNMeUYsU0FBUTt3REFDUkMsTUFBSzt3REFDTEMsU0FBUyxJQUFNeEMsZ0JBQWdCbUI7d0RBQy9CL0QsV0FBVTtrRUFFViw0RUFBQ2Isc0lBQUlBOzREQUFDYSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OzswREFHcEIsOERBQUNxRjtnREFBSXJGLFdBQVU7MERBQ2IsNEVBQUMwQzs4REFBTXFCOzs7Ozs7Ozs7Ozs7Ozs7OzttQ0FkSHpDOzs7Ozs0QkFtQmQ7d0JBQ0YsRUFBRSxPQUFPZ0UsV0FBVzs0QkFDbEIscUJBQXFCOzRCQUNyQmhDLFNBQVN4QixJQUFJLGVBQ1gsOERBQUN3QztnQ0FBZ0J0RSxXQUFVOzBDQUN6Qiw0RUFBQ3NFO29DQUFJdEUsV0FBVTs7c0RBQ2IsOERBQUNzRTs0Q0FBSXRFLFdBQVU7OzhEQUNiLDhEQUFDd0M7b0RBQUt4QyxXQUFVOzhEQUFxQzs7Ozs7OzhEQUNyRCw4REFBQ1AseURBQU1BO29EQUNMeUYsU0FBUTtvREFDUkMsTUFBSztvREFDTEMsU0FBUyxJQUFNeEMsZ0JBQWdCbUI7b0RBQy9CL0QsV0FBVTs4REFFViw0RUFBQ2Isc0lBQUlBO3dEQUFDYSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztzREFHcEIsOERBQUNxRjs0Q0FBSXJGLFdBQVU7c0RBQ2IsNEVBQUMwQzswREFBTXFCOzs7Ozs7Ozs7Ozs7Ozs7OzsrQkFkSHpDOzs7Ozt3QkFtQmQ7b0JBQ0YsT0FFSyxJQUFJbUMsc0JBQXNCLGFBQWFNLFlBQVlGLElBQUksR0FBR3BDLEtBQUssQ0FBQyw0RkFBNEY7d0JBQy9KNkIsU0FBU3hCLElBQUksZUFDWCw4REFBQ3dDOzRCQUFnQnRFLFdBQVU7c0NBQ3pCLDRFQUFDc0U7Z0NBQUl0RSxXQUFVOztrREFDYiw4REFBQ3NFO3dDQUFJdEUsV0FBVTs7MERBQ2IsOERBQUN3QztnREFBS3hDLFdBQVU7O2tFQUNkLDhEQUFDdUY7d0RBQUl2RixXQUFVO3dEQUFVd0YsU0FBUTt3REFBWUMsTUFBSztrRUFDaEQsNEVBQUNDOzREQUFLQyxHQUFFOzs7Ozs7Ozs7OztvREFDSjs7Ozs7OzswREFHUiw4REFBQ2xHLHlEQUFNQTtnREFDTHlGLFNBQVE7Z0RBQ1JDLE1BQUs7Z0RBQ0xDLFNBQVMsSUFBTXhDLGdCQUFnQm1CO2dEQUMvQi9ELFdBQVU7MERBRVYsNEVBQUNiLHNJQUFJQTtvREFBQ2EsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBR3BCLDhEQUFDc0U7d0NBQUl0RSxXQUFVO2tEQUNiLDRFQUFDc0U7NENBQ0N0RSxXQUFVOzRDQUNWNEYsZ0JBQWM3Qjs7OERBRWQsOERBQUNPO29EQUFJdEUsV0FBVTs4REFBOEI7Ozs7Ozs4REFDN0MsOERBQUNzRTtvREFBSXRFLFdBQVU7OERBQ2IsNEVBQUNxRjt3REFBSXJGLFdBQVU7a0VBQXVCK0Q7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MkJBekJ0Q3pDOzs7OztvQkFnQ2QsT0FBTzt3QkFDTCxRQUFRO3dCQUNSZ0MsU0FBU3hCLElBQUksZUFDWCw4REFBQ3dDOzRCQUFnQnRFLFdBQVU7c0NBQ3pCLDRFQUFDc0U7Z0NBQUl0RSxXQUFVOztrREFDYiw4REFBQ3NFO3dDQUFJdEUsV0FBVTs7MERBQ2IsOERBQUN3QztnREFBS3hDLFdBQVU7MERBQ2J5RCxxQkFBcUI7Ozs7OzswREFFeEIsOERBQUNoRSx5REFBTUE7Z0RBQ0x5RixTQUFRO2dEQUNSQyxNQUFLO2dEQUNMQyxTQUFTLElBQU14QyxnQkFBZ0JtQjtnREFDL0IvRCxXQUFVOzBEQUVWLDRFQUFDYixzSUFBSUE7b0RBQUNhLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUdwQiw4REFBQ3FGO3dDQUFJckYsV0FBVTtrREFDYiw0RUFBQzBDO3NEQUFNcUI7Ozs7Ozs7Ozs7Ozs7Ozs7OzJCQWhCSHpDOzs7OztvQkFxQmQ7b0JBRUFrQyxtQkFBbUIsRUFBRTtvQkFDckJDLG9CQUFvQjtnQkFDdEI7Z0JBQ0E7WUFDRjtZQUVBLGVBQWU7WUFDZixJQUFJRixhQUFhO2dCQUNmQyxpQkFBaUIxQixJQUFJLENBQUM2QjtnQkFDdEI7WUFDRjtZQUVBLG1DQUFtQztZQUNuQyxJQUFJLENBQUNDLGFBQWE7Z0JBQ2hCTixTQUFTeEIsSUFBSSxlQUFDLDhEQUFDd0M7b0JBQWdCdEUsV0FBVTttQkFBakJzQjs7Ozs7Z0JBQ3hCO1lBQ0Y7WUFFQSwwQkFBMEI7WUFDMUIsSUFBSXNDLFlBQVluQyxLQUFLLENBQUMsbUJBQW1CO2dCQUN2QyxNQUFNQSxRQUFRbUMsWUFBWW5DLEtBQUssQ0FBQztnQkFDaEMsSUFBSUEsT0FBTztvQkFDVCxNQUFNLEdBQUdvRSxRQUFRekQsTUFBTSxHQUFHWDtvQkFDMUIsTUFBTXFFLFFBQVFELE9BQU8vRSxNQUFNO29CQUUzQixJQUFJZCxZQUFZO29CQUNoQixJQUFJK0YsWUFBWTtvQkFFaEIsT0FBUUQ7d0JBQ04sS0FBSzs0QkFDSEMsWUFBWTs0QkFDWi9GLFlBQVk7NEJBQ1o7d0JBQ0YsS0FBSzs0QkFDSCtGLFlBQVk7NEJBQ1ovRixZQUFZOzRCQUNaO3dCQUNGLEtBQUs7NEJBQ0grRixZQUFZOzRCQUNaL0YsWUFBWTs0QkFDWjt3QkFDRixLQUFLOzRCQUNIK0YsWUFBWTs0QkFDWi9GLFlBQVk7NEJBQ1o7d0JBQ0YsS0FBSzs0QkFDSCtGLFlBQVk7NEJBQ1ovRixZQUFZOzRCQUNaO3dCQUNGLEtBQUs7NEJBQ0grRixZQUFZOzRCQUNaL0YsWUFBWTs0QkFDWjtvQkFDSjtvQkFFQXNELFNBQVN4QixJQUFJLGVBQ1gsOERBQUN3QztrQ0FDQyw0RUFBQ3lCOzRCQUFVL0YsV0FBV0E7c0NBQ25Ca0IsaUJBQWlCa0I7Ozs7Ozt1QkFGWmQ7Ozs7O2dCQU1kO2dCQUNBO1lBQ0Y7WUFFQSwyQkFBMkI7WUFDM0IsSUFBSXNDLFlBQVluQyxLQUFLLENBQUMsZUFBZTtnQkFDbkMsTUFBTVcsUUFBUXdCLFlBQVlvQyxPQUFPLENBQUMsVUFBVTtnQkFDNUMxQyxTQUFTeEIsSUFBSSxlQUNYLDhEQUFDd0M7b0JBQWdCdEUsV0FBVTs4QkFDekIsNEVBQUNpRzt3QkFBR2pHLFdBQVU7OzBDQUNaLDhEQUFDd0M7MENBQUs7Ozs7OzswQ0FDTiw4REFBQ0E7MENBQU10QixpQkFBaUJrQjs7Ozs7Ozs7Ozs7O21CQUhsQmQ7Ozs7O2dCQU9aO1lBQ0Y7WUFFQSxvRUFBb0U7WUFDcEUsSUFBSXNDLFlBQVluQyxLQUFLLENBQUMsb0RBQW9EO2dCQUN4RSxNQUFNQSxRQUFRbUMsWUFBWW5DLEtBQUssQ0FBQztnQkFDaEMsSUFBSUEsT0FBTztvQkFDVCxNQUFNLEdBQUd5RSxPQUFPOUQsTUFBTSxHQUFHWDtvQkFDekIsTUFBTTBFLHVCQUF1Qjt3QkFBQzt3QkFBTTt3QkFBTTt3QkFBTTt3QkFBTTtxQkFBSyxDQUFDL0UsUUFBUSxDQUFDOEU7b0JBQ3JFNUMsU0FBU3hCLElBQUksZUFDWCw4REFBQ3dDO3dCQUFnQnRFLFdBQVU7a0NBQ3pCLDRFQUFDb0c7NEJBQUdwRyxXQUFXLCtEQUlkLE9BSENtRyx1QkFDSSxvRUFDQTs7OENBRUosOERBQUMzRDtvQ0FBS3hDLFdBQVU7OENBQVdrRzs7Ozs7OzhDQUMzQiw4REFBQzFEOzhDQUFNdEIsaUJBQWlCa0I7Ozs7OztnQ0FDdkIrRCxzQ0FDQyw4REFBQzNEO29DQUFLeEMsV0FBVTs4Q0FBZ0U7Ozs7Ozs7Ozs7Ozt1QkFUNUVzQjs7Ozs7Z0JBZ0JkO2dCQUNBO1lBQ0Y7WUFFQSx3Q0FBd0M7WUFDeEMsSUFBSXNDLFlBQVluQyxLQUFLLENBQUMseUJBQXlCO2dCQUM3QyxNQUFNQSxRQUFRbUMsWUFBWW5DLEtBQUssQ0FBQztnQkFDaEMsSUFBSUEsT0FBTztvQkFDVCxNQUFNLEdBQUd5RSxPQUFPOUQsTUFBTSxHQUFHWDtvQkFDekI2QixTQUFTeEIsSUFBSSxlQUNYLDhEQUFDd0M7d0JBQWdCdEUsV0FBVTtrQ0FDekIsNEVBQUNxRzs0QkFBR3JHLFdBQVU7OzhDQUNaLDhEQUFDd0M7OENBQU0wRDs7Ozs7OzhDQUNQLDhEQUFDMUQ7OENBQU10QixpQkFBaUJrQjs7Ozs7Ozs7Ozs7O3VCQUhsQmQ7Ozs7O2dCQU9kO2dCQUNBO1lBQ0Y7WUFFQSw4QkFBOEI7WUFDOUIsSUFBSXNDLFlBQVluQyxLQUFLLENBQUMsMkJBQTJCO2dCQUMvQzZCLFNBQVN4QixJQUFJLGVBQ1gsOERBQUN3QztvQkFBZ0J0RSxXQUFVOzhCQUN6Qiw0RUFBQ3NHO3dCQUFHdEcsV0FBVTs7Ozs7O21CQUROc0I7Ozs7O2dCQUlaO1lBQ0Y7WUFFQSx3QkFBd0I7WUFDeEIsSUFBSXNDLFlBQVluQyxLQUFLLENBQUMsU0FBUztnQkFDN0I2QixTQUFTeEIsSUFBSSxlQUNYLDhEQUFDd0M7b0JBQWdCdEUsV0FBVTs4QkFDekIsNEVBQUNzRzt3QkFBR3RHLFdBQVU7Ozs7OzttQkFETnNCOzs7OztnQkFJWjtZQUNGO1lBRUEseUJBQXlCO1lBQ3pCLElBQUlzQyxZQUFZbkMsS0FBSyxDQUFDLHlCQUF5QjtnQkFDN0MsTUFBTUEsUUFBUW1DLFlBQVluQyxLQUFLLENBQUM7Z0JBQ2hDLElBQUlBLE9BQU87b0JBQ1QsTUFBTSxHQUFHOEUsUUFBUUMsUUFBUXJGLEtBQUssR0FBR007b0JBQ2pDLE1BQU1nRixjQUFjQyxLQUFLQyxLQUFLLENBQUNKLE9BQU96RixNQUFNLEdBQUcsR0FBRyxhQUFhOztvQkFDL0QsTUFBTThGLGFBQWFILGNBQWMsS0FBSyxHQUFHLGtCQUFrQjs7b0JBRTNEbkQsU0FBU3hCLElBQUksZUFDWCw4REFBQ3dDO3dCQUFnQnRFLFdBQVU7d0JBQThCNkcsT0FBTzs0QkFBRUQsWUFBWSxHQUFjLE9BQVhBLFlBQVc7d0JBQUk7OzBDQUM5Riw4REFBQ3BFO2dDQUFLeEMsV0FBVTswQ0FBa0M7Ozs7OzswQ0FHbEQsOERBQUN3QztnQ0FBS3hDLFdBQVU7MENBQWtDa0IsaUJBQWlCQzs7Ozs7Ozt1QkFKM0RHOzs7OztnQkFPZDtnQkFDQTtZQUNGO1lBRUEseUJBQXlCO1lBQ3pCLElBQUlzQyxZQUFZbkMsS0FBSyxDQUFDLGlCQUFpQjtnQkFDckMsTUFBTUEsUUFBUW1DLFlBQVluQyxLQUFLLENBQUM7Z0JBQ2hDLElBQUlBLE9BQU87b0JBQ1QsTUFBTSxHQUFHK0UsUUFBUXJGLEtBQUssR0FBR007b0JBQ3pCLE1BQU1xRixjQUFjTixXQUFXO29CQUMvQmxELFNBQVN4QixJQUFJLGVBQ1gsOERBQUN3Qzt3QkFBZ0J0RSxXQUFVOzswQ0FDekIsOERBQUN3QztnQ0FBS3hDLFdBQVcsVUFBNEQsT0FBbEQ4RyxjQUFjLG1CQUFtQjswQ0FDekROOzs7Ozs7MENBRUgsOERBQUNoRTtnQ0FBS3hDLFdBQVU7MENBQWtDa0IsaUJBQWlCQzs7Ozs7Ozt1QkFKM0RHOzs7OztnQkFPZDtnQkFDQTtZQUNGO1lBRUEsZ0NBQWdDO1lBQ2hDLElBQUlzQyxZQUFZbkMsS0FBSyxDQUFDLGtCQUFrQjtnQkFDdEMsTUFBTUEsUUFBUW1DLFlBQVluQyxLQUFLLENBQUM7Z0JBQ2hDLElBQUlBLE9BQU87b0JBQ1QsTUFBTSxHQUFHc0YsUUFBUTVGLEtBQUssR0FBR007b0JBQ3pCNkIsU0FBU3hCLElBQUksZUFDWCw4REFBQ3dDO3dCQUFnQnRFLFdBQVU7OzBDQUN6Qiw4REFBQ3dDO2dDQUFLeEMsV0FBVTswQ0FDYitHOzs7Ozs7MENBRUgsOERBQUN2RTtnQ0FBS3hDLFdBQVU7MENBQWtDa0IsaUJBQWlCQzs7Ozs7Ozt1QkFKM0RHOzs7OztnQkFPZDtnQkFDQTtZQUNGO1lBRUEsd0NBQXdDO1lBQ3hDLElBQUlzQyxZQUFZeEMsUUFBUSxDQUFDLFFBQVEsQ0FBQ3dDLFlBQVluQyxLQUFLLENBQUMsbUNBQW1DO2dCQUNyRixNQUFNSixRQUFRdUMsWUFBWS9DLEtBQUssQ0FBQztnQkFDaEMsSUFBSVEsTUFBTVAsTUFBTSxLQUFLLEdBQUc7b0JBQ3RCLE1BQU1rRyxVQUFVM0YsS0FBSyxDQUFDLEVBQUUsQ0FBQ3dDLElBQUk7b0JBQzdCLE1BQU1vRCxRQUFRNUYsS0FBSyxDQUFDLEVBQUUsQ0FBQ3dDLElBQUk7b0JBRTNCLHFEQUFxRDtvQkFDckQsTUFBTXFELGNBQWNGLFFBQVE1RixRQUFRLENBQUMsU0FBUzRGLFFBQVE1RixRQUFRLENBQUMsU0FBUzRGLFFBQVE1RixRQUFRLENBQUM7b0JBQ3pGLE1BQU0rRixTQUFTSCxRQUFRNUYsUUFBUSxDQUFDLFNBQVM0RixRQUFRNUYsUUFBUSxDQUFDLFNBQVM0RixRQUFRNUYsUUFBUSxDQUFDO29CQUVwRmtDLFNBQVN4QixJQUFJLGVBQ1gsOERBQUN3Qzt3QkFBZ0J0RSxXQUFVOzswQ0FDekIsOERBQUN3QztnQ0FBS3hDLFdBQVcsNkJBSWhCLE9BSENrSCxjQUFjLG1CQUNkQyxTQUFTLGtCQUNUOztvQ0FFQ0g7b0NBQVE7Ozs7Ozs7MENBRVgsOERBQUN4RTtnQ0FBS3hDLFdBQVcsa0JBRWhCLE9BRENrSCxjQUFjLCtCQUErQjswQ0FFNUNoRyxpQkFBaUIrRjs7Ozs7Ozt1QkFYWjNGOzs7OztvQkFlWjtnQkFDRjtZQUNGO1lBRUEsa0NBQWtDO1lBQ2xDLElBQUlzQyxZQUFZeEMsUUFBUSxDQUFDLE1BQU07Z0JBQzdCLHlDQUF5QztnQkFDekMsTUFBTWdHLGNBQWM7Z0JBQ3BCLE1BQU0vRixRQUFRdUMsWUFBWS9DLEtBQUssQ0FBQ3VHO2dCQUNoQyxNQUFNQyxpQkFBaUJoRyxNQUFNaUcsR0FBRyxDQUFDLENBQUNDLE1BQU01RjtvQkFDdEMsSUFBSTRGLEtBQUs5RixLQUFLLENBQUMsZUFBZTt3QkFDNUIscUJBQ0UsOERBQUNlOzRCQUFpQnhDLFdBQVU7O2dDQUF3RDtnQ0FDaEZ1SDs7MkJBRE81Rjs7Ozs7b0JBSWY7b0JBQ0EsT0FBTzRGO2dCQUNUO2dCQUVBakUsU0FBU3hCLElBQUksZUFDWCw4REFBQ3dDO29CQUFnQnRFLFdBQVU7OEJBQ3hCcUg7bUJBRE8vRjs7Ozs7Z0JBSVo7WUFDRjtZQUVBLCtCQUErQjtZQUMvQixJQUFJc0MsWUFBWXhDLFFBQVEsQ0FBQyxRQUFRd0MsWUFBWS9DLEtBQUssQ0FBQyxLQUFLQyxNQUFNLElBQUksR0FBRztnQkFDbkUsUUFBUTtnQkFDUixNQUFNMEcsWUFBc0I7b0JBQUM1RDtpQkFBWTtnQkFDekMsSUFBSTZELElBQUkvRCxJQUFJO2dCQUVaLFdBQVc7Z0JBQ1gsTUFBTytELElBQUlwRSxNQUFNdkMsTUFBTSxDQUFFO29CQUN2QixNQUFNNEcsV0FBV3JFLEtBQUssQ0FBQ29FLEVBQUUsQ0FBQzVELElBQUk7b0JBQzlCLElBQUk2RCxTQUFTdEcsUUFBUSxDQUFDLFFBQVFzRyxTQUFTN0csS0FBSyxDQUFDLEtBQUtDLE1BQU0sSUFBSSxHQUFHO3dCQUM3RDBHLFVBQVUxRixJQUFJLENBQUM0Rjt3QkFDZkQ7b0JBQ0YsT0FBTyxJQUFJQyxhQUFhLElBQUk7d0JBQzFCRDt3QkFDQTtvQkFDRixPQUFPO3dCQUNMO29CQUNGO2dCQUNGO2dCQUVBLElBQUlELFVBQVUxRyxNQUFNLEdBQUcsR0FBRztvQkFDeEIsTUFBTTZHLG9CQUFvQixDQUFDQyxNQUFnQixlQUFlQyxJQUFJLENBQUNEO29CQUMvRCxJQUFJRSxZQUEyQjtvQkFDL0IsSUFBSUMsaUJBQWlCLENBQUM7b0JBQ3RCLElBQUlDLFdBQXFCLEVBQUU7b0JBRTNCLFdBQVc7b0JBQ1gsSUFBSyxJQUFJQyxJQUFJLEdBQUdBLElBQUlULFVBQVUxRyxNQUFNLEVBQUVtSCxJQUFLO3dCQUN6QyxJQUFJTixrQkFBa0JILFNBQVMsQ0FBQ1MsRUFBRSxHQUFHOzRCQUNuQyxJQUFJQSxJQUFJLEdBQUc7Z0NBQ1RILFlBQVlOLFNBQVMsQ0FBQ1MsSUFBSSxFQUFFO2dDQUM1QkYsaUJBQWlCRTs0QkFDbkI7NEJBQ0E7d0JBQ0Y7b0JBQ0Y7b0JBRUEsZ0JBQWdCO29CQUNoQixJQUFJRixrQkFBa0IsR0FBRzt3QkFDdkJDLFdBQVdSLFVBQVUzRixLQUFLLENBQUNrRyxpQkFBaUI7b0JBQzlDLE9BQU87d0JBQ0wsdUJBQXVCO3dCQUN2QkQsWUFBWU4sU0FBUyxDQUFDLEVBQUU7d0JBQ3hCUSxXQUFXUixVQUFVM0YsS0FBSyxDQUFDO29CQUM3QjtvQkFFQSxNQUFNcUcsZ0JBQWdCLENBQUNOO3dCQUNyQixPQUFPQSxJQUFJL0csS0FBSyxDQUFDLEtBQ2R5RyxHQUFHLENBQUNhLENBQUFBLE9BQVFBLEtBQUt0RSxJQUFJLElBQ3JCdUUsTUFBTSxDQUFDRCxDQUFBQSxPQUFRQSxTQUFTO29CQUM3QjtvQkFFQTdFLFNBQVN4QixJQUFJLGVBQ1gsOERBQUN3Qzt3QkFBZ0J0RSxXQUFVO2tDQUN6Qiw0RUFBQ3FJOzRCQUFNckksV0FBVTs7Z0NBQ2Q4SCwyQkFDQyw4REFBQ1E7b0NBQU10SSxXQUFVOzhDQUNmLDRFQUFDdUk7a0RBQ0VMLGNBQWNKLFdBQVdSLEdBQUcsQ0FBQyxDQUFDa0IsUUFBUTdHLHNCQUNyQyw4REFBQzhHO2dEQUFlekksV0FBVTswREFDdkJrQixpQkFBaUJzSDsrQ0FEWDdHOzs7Ozs7Ozs7Ozs7Ozs7OENBT2pCLDhEQUFDK0c7OENBQ0VWLFNBQVNWLEdBQUcsQ0FBQyxDQUFDTSxLQUFLZSx5QkFDbEIsOERBQUNKOzRDQUFrQnZJLFdBQVcySSxXQUFXLE1BQU0sSUFBSSxhQUFhO3NEQUM3RFQsY0FBY04sS0FBS04sR0FBRyxDQUFDLENBQUNhLE1BQU1TLDBCQUM3Qiw4REFBQ0M7b0RBQW1CN0ksV0FBVTs4REFDM0JrQixpQkFBaUJpSDttREFEWFM7Ozs7OzJDQUZKRDs7Ozs7Ozs7Ozs7Ozs7Ozt1QkFmUHJIOzs7OztvQkE0QlpvQyxJQUFJK0QsSUFBSSxFQUFFLFVBQVU7O29CQUNwQjtnQkFDRjtZQUNGO1lBRUEsaUJBQWlCO1lBQ2pCLElBQUk3RCxZQUFZRSxVQUFVLENBQUMsT0FBTztnQkFDaEMsTUFBTWdGLFlBQVlsRixZQUFZL0IsS0FBSyxDQUFDO2dCQUNwQ3lCLFNBQVN4QixJQUFJLGVBQ1gsOERBQUN3QztvQkFBZ0J0RSxXQUFVOzhCQUN6Qiw0RUFBQytJO3dCQUFFL0ksV0FBVTtrQ0FBeUJrQixpQkFBaUI0SDs7Ozs7O21CQUQvQ3hIOzs7OztnQkFJWjtZQUNGO1lBRUEsV0FBVztZQUNYLElBQUlzQyxZQUFZbkMsS0FBSyxDQUFDLHNCQUFzQjtnQkFDMUMsTUFBTUEsUUFBUW1DLFlBQVluQyxLQUFLLENBQUM7Z0JBQ2hDLElBQUlBLE9BQU87b0JBQ1QsTUFBTSxHQUFHeUUsT0FBTy9FLEtBQUssR0FBR007b0JBQ3hCLElBQUl1SCxTQUFTQyxhQUFhQyxXQUFXQztvQkFFckMsT0FBUWpEO3dCQUNOLEtBQUs7NEJBQ0g4QyxVQUFVOzRCQUNWQyxjQUFjOzRCQUNkQyxZQUFZOzRCQUNaQyxxQkFBTyw4REFBQzdKLHNJQUFhQTtnQ0FBQ1UsV0FBVTs7Ozs7OzRCQUNoQzt3QkFDRixLQUFLOzRCQUNIZ0osVUFBVTs0QkFDVkMsY0FBYzs0QkFDZEMsWUFBWTs0QkFDWkMscUJBQU8sOERBQUMzSix1SUFBU0E7Z0NBQUNRLFdBQVU7Ozs7Ozs0QkFDNUI7d0JBQ0YsS0FBSzs0QkFDSGdKLFVBQVU7NEJBQ1ZDLGNBQWM7NEJBQ2RDLFlBQVk7NEJBQ1pDLHFCQUFPLDhEQUFDNUosdUlBQUlBO2dDQUFDUyxXQUFVOzs7Ozs7NEJBQ3ZCO3dCQUNGLEtBQUs7NEJBQ0hnSixVQUFVOzRCQUNWQyxjQUFjOzRCQUNkQyxZQUFZOzRCQUNaQyxxQkFBTyw4REFBQzlKLHVJQUFXQTtnQ0FBQ1csV0FBVTs7Ozs7OzRCQUM5Qjt3QkFDRjs0QkFDRWdKLFVBQVU7NEJBQ1ZDLGNBQWM7NEJBQ2RDLFlBQVk7NEJBQ1pDLE9BQU87b0JBQ1g7b0JBRUE3RixTQUFTeEIsSUFBSSxlQUNYLDhEQUFDd0M7d0JBQWdCdEUsV0FBVyx1QkFBa0NpSixPQUFYRCxTQUFRLEtBQWUsT0FBWkMsYUFBWTtrQ0FDeEUsNEVBQUMzRTs0QkFBSXRFLFdBQVcsdUNBQWlELE9BQVZrSjs7Z0NBQ3BEQzs4Q0FDRCw4REFBQzNHOzhDQUFNdEIsaUJBQWlCQzs7Ozs7Ozs7Ozs7O3VCQUhsQkc7Ozs7O2dCQU9kO2dCQUNBO1lBQ0Y7WUFFQSxzQ0FBc0M7WUFDdEMsSUFBSXNDLFlBQVl4QyxRQUFRLENBQUMsbUJBQW1Cd0MsWUFBWXhDLFFBQVEsQ0FBQyxPQUFPO2dCQUN0RWtDLFNBQVN4QixJQUFJLGVBQ1gsOERBQUN3QztvQkFBZ0J0RSxXQUFVOzhCQUN6Qiw0RUFBQytJO3dCQUFFL0ksV0FBVTtrQ0FDVjREOzs7Ozs7bUJBRkt0Qzs7Ozs7Z0JBTVo7WUFDRjtZQUVBLHVCQUF1QjtZQUN2QixNQUFNOEgsa0JBQWtCeEYsWUFBWW5DLEtBQUssQ0FBQztZQUMxQyxJQUFJMkgsaUJBQWlCO2dCQUNuQixNQUFNLEdBQUdyQyxRQUFRM0UsTUFBTSxHQUFHZ0g7Z0JBRTFCLGFBQWE7Z0JBQ2IsTUFBTUMsaUJBQTJCO29CQUFDekY7aUJBQVk7Z0JBQzlDLElBQUk2RCxJQUFJL0QsSUFBSTtnQkFFWix3QkFBd0I7Z0JBQ3hCLE1BQU8rRCxJQUFJcEUsTUFBTXZDLE1BQU0sQ0FBRTtvQkFDdkIsTUFBTTRHLFdBQVdyRSxLQUFLLENBQUNvRSxFQUFFLENBQUM1RCxJQUFJO29CQUM5QixJQUFJNkQsYUFBYSxNQUFNQSxTQUFTNUQsVUFBVSxDQUFDLFFBQVE7d0JBQ2pEO29CQUNGO29CQUNBLElBQUk0RCxTQUFTNUQsVUFBVSxDQUFDLFFBQ3BCNEQsU0FBUzVELFVBQVUsQ0FBQyxTQUNwQjRELFNBQVM1RCxVQUFVLENBQUMsU0FDcEI0RCxTQUFTNUQsVUFBVSxDQUFDLFNBQ3BCNEQsU0FBUzVELFVBQVUsQ0FBQyxTQUNwQjRELFNBQVM1RCxVQUFVLENBQUMsT0FBTzt3QkFDN0J1RixlQUFldkgsSUFBSSxDQUFDNEY7d0JBQ3BCRDtvQkFDRixPQUFPO3dCQUNMO29CQUNGO2dCQUNGO2dCQUVBLFNBQVM7Z0JBQ1QsTUFBTTZCLGFBQWFDLG9CQUFvQkY7Z0JBQ3ZDLElBQUlDLFlBQVk7b0JBQ2RoRyxTQUFTeEIsSUFBSSxlQUNYLDhEQUFDd0M7d0JBQWdCdEUsV0FBVTtrQ0FDekIsNEVBQUNMLDJGQUE2QkE7NEJBQzVCMkosWUFBWUE7NEJBQ1pFLHNCQUFzQkYsV0FBV0Usb0JBQW9COzRCQUNyREMsZUFBZUgsV0FBV0csYUFBYTs0QkFDdkNDLHVCQUF1Qjs7Ozs7O3VCQUxqQnBJOzs7OztnQkFTZCxPQUFPO29CQUNMLG1CQUFtQjtvQkFDbkJnQyxTQUFTeEIsSUFBSSxlQUNYLDhEQUFDaUg7d0JBQWMvSSxXQUFVO2tDQUN0QmtCLGlCQUFpQjBDO3VCQURadEM7Ozs7O2dCQUlaO2dCQUVBb0MsSUFBSStELElBQUksRUFBRSxVQUFVOztnQkFDcEI7WUFDRjtZQUVBLHlCQUF5QjtZQUN6QixJQUFJN0QsWUFBWTlDLE1BQU0sR0FBRyxHQUFHO2dCQUMxQixvREFBb0Q7Z0JBQ3BELE1BQU02SSxhQUFhaEcsS0FBS0csVUFBVSxDQUFDLFNBQVNILEtBQUtHLFVBQVUsQ0FBQztnQkFFNURSLFNBQVN4QixJQUFJLGVBQ1gsOERBQUNpSDtvQkFBYy9JLFdBQVcsdUNBRXpCLE9BREMySixhQUFhLHdCQUF3Qjs4QkFFcEN6SSxpQkFBaUIwQzttQkFIWnRDOzs7OztZQU1aO1FBQ0Y7UUFFQSxPQUFPZ0M7SUFDVDtJQUVBLHFCQUNFLDhEQUFDZ0I7UUFBSXRFLFdBQVcscUJBQStCLE9BQVZBO2tCQUNsQ29ELGNBQWNyRDs7Ozs7O0FBR3JCO0dBcnlCZ0JEO0tBQUFBO0FBdXlCaEIsV0FBVztBQUNYLFNBQVN5SixvQkFBb0JGLGNBQXdCO0lBQ25ELElBQUk7UUFDRixNQUFNTyxZQUFZUCxjQUFjLENBQUMsRUFBRTtRQUNuQyxNQUFNUSxhQUFhRCxVQUFVbkksS0FBSyxDQUFDO1FBQ25DLElBQUksQ0FBQ29JLFlBQVksT0FBTztRQUV4QixNQUFNLEdBQUc5QyxRQUFRM0UsTUFBTSxHQUFHeUg7UUFFMUIsVUFBVTtRQUNWLE1BQU1QLGFBQWtCO1lBQ3RCUSxJQUFJQyxTQUFTaEQ7WUFDYjNFLE9BQU9BO1lBQ1A0SCxTQUFTLEVBQUU7WUFDWEMsU0FBUztZQUNUQyxrQkFBa0IsSUFBSUMsT0FBT0MsV0FBVztZQUN4Q0MsVUFBVTtZQUNWQyxpQkFBaUIsRUFBRTtZQUNuQkMsa0JBQWtCLEVBQUU7WUFDcEJDLGdCQUFnQjtZQUNoQkMsaUJBQWlCO1lBQ2pCQyxjQUFjO1lBQ2RDLHFCQUFxQjtZQUNyQkMsZ0JBQWdCO1lBQ2hCQyxVQUFVO1lBQ1ZyQixzQkFBc0I7WUFDdEJDLGVBQWUsRUFBRTtRQUNuQjtRQUVBLFVBQVU7UUFDVixJQUFLLElBQUkvRixJQUFJLEdBQUdBLElBQUkyRixlQUFldkksTUFBTSxFQUFFNEMsSUFBSztZQUM5QyxNQUFNQyxPQUFPMEYsY0FBYyxDQUFDM0YsRUFBRSxDQUFDRyxJQUFJO1lBRW5DLGdCQUFnQjtZQUNoQixJQUFJRixLQUFLRyxVQUFVLENBQUMsUUFBUUgsS0FBS21ILFFBQVEsQ0FBQyxRQUFRLENBQUNuSCxLQUFLdkMsUUFBUSxDQUFDLE9BQU87Z0JBQ3RFLE1BQU0ySixxQkFBcUJwSCxLQUFLbEMsS0FBSyxDQUFDO2dCQUN0QyxJQUFJc0osb0JBQW9CO29CQUN0QixNQUFNQyxvQkFBb0JELGtCQUFrQixDQUFDLEVBQUU7b0JBQy9DLE1BQU0xSixRQUFRMkosa0JBQWtCbkssS0FBSyxDQUFDO29CQUN0QyxJQUFJUSxNQUFNUCxNQUFNLEdBQUcsR0FBRzt3QkFDcEJ3SSxXQUFXVSxPQUFPLEdBQUczSSxLQUFLLENBQUMsRUFBRSxDQUFDUixLQUFLLENBQUMsS0FBS3lHLEdBQUcsQ0FBQyxDQUFDMkQsU0FBbUJBLE9BQU9wSCxJQUFJO29CQUM5RTtnQkFDRjtZQUNGLE9BR0ssSUFBSUYsS0FBS0csVUFBVSxDQUFDLE9BQU87Z0JBQzlCLE1BQU1vSCxlQUFldkgsS0FBS2xDLEtBQUssQ0FBQztnQkFDaEMsSUFBSXlKLGNBQWM7b0JBQ2hCNUIsV0FBV1csT0FBTyxHQUFHaUIsWUFBWSxDQUFDLEVBQUU7b0JBQ3BDNUIsV0FBV1ksZ0JBQWdCLEdBQUdILFNBQVNtQixZQUFZLENBQUMsRUFBRTtnQkFDeEQ7WUFDRixPQUdLLElBQUl2SCxLQUFLRyxVQUFVLENBQUMsT0FBTztnQkFDOUIsTUFBTXFILFVBQVV4SCxLQUFLbEMsS0FBSyxDQUFDO2dCQUMzQixJQUFJMEosU0FBUztvQkFDWDdCLFdBQVc4QixhQUFhLEdBQUdDLFdBQVdGLE9BQU8sQ0FBQyxFQUFFO2dCQUNsRDtZQUNGLE9BR0ssSUFBSXhILEtBQUtHLFVBQVUsQ0FBQyxPQUFPO2dCQUM5QixNQUFNd0gsaUJBQWlCM0gsS0FBS2xDLEtBQUssQ0FBQztnQkFDbEMsSUFBSTZKLGdCQUFnQjtvQkFDbEJoQyxXQUFXRSxvQkFBb0IsR0FBRzhCLGNBQWMsQ0FBQyxFQUFFO2dCQUNyRDtZQUNGLE9BR0ssSUFBSTNILEtBQUtHLFVBQVUsQ0FBQyxPQUFPO2dCQUM5QixNQUFNeUgsZUFBZTVILEtBQUtsQyxLQUFLLENBQUM7Z0JBQ2hDLElBQUk4SixjQUFjO29CQUNoQmpDLFdBQVdHLGFBQWEsQ0FBQzNILElBQUksQ0FBQ3lKLFlBQVksQ0FBQyxFQUFFO2dCQUMvQztZQUNGLE9BR0ssSUFBSTVILEtBQUtHLFVBQVUsQ0FBQyxPQUFPO2dCQUM5QixNQUFNMEgsV0FBVzdILEtBQUtsQyxLQUFLLENBQUM7Z0JBQzVCLElBQUkrSixVQUFVO29CQUNabEMsV0FBV21DLEdBQUcsR0FBR0QsUUFBUSxDQUFDLEVBQUU7Z0JBQzlCO1lBQ0Y7UUFDRjtRQUVBLFFBQVE7UUFDUixJQUFJLENBQUNsQyxXQUFXb0IsWUFBWSxFQUFFO1lBQzVCcEIsV0FBV29CLFlBQVksR0FBRyxHQUFTLE9BQU50SSxPQUFNO1FBQ3JDO1FBRUEsSUFBSSxDQUFDa0gsV0FBV3FCLG1CQUFtQixFQUFFO1lBQ25DckIsV0FBV3FCLG1CQUFtQixHQUFHO1FBQ25DO1FBRUEsSUFBSSxDQUFDckIsV0FBV3NCLGNBQWMsRUFBRTtZQUM5QnRCLFdBQVdzQixjQUFjLEdBQUc7UUFDOUI7UUFFQSxpQkFBaUI7UUFDakIsTUFBTWMsYUFBYXRKLE1BQU11SixXQUFXO1FBQ3BDLElBQUlELFdBQVd0SyxRQUFRLENBQUMsa0JBQWtCc0ssV0FBV3RLLFFBQVEsQ0FBQyxVQUFVO1lBQ3RFa0ksV0FBV2dCLGVBQWUsQ0FBQ3hJLElBQUksQ0FBQztRQUNsQztRQUNBLElBQUk0SixXQUFXdEssUUFBUSxDQUFDLFNBQVM7WUFDL0JrSSxXQUFXZ0IsZUFBZSxDQUFDeEksSUFBSSxDQUFDO1FBQ2xDO1FBQ0EsSUFBSTRKLFdBQVd0SyxRQUFRLENBQUMsV0FBVztZQUNqQ2tJLFdBQVdnQixlQUFlLENBQUN4SSxJQUFJLENBQUM7UUFDbEM7UUFDQSxJQUFJNEosV0FBV3RLLFFBQVEsQ0FBQyxRQUFRO1lBQzlCa0ksV0FBV2dCLGVBQWUsQ0FBQ3hJLElBQUksQ0FBQztRQUNsQztRQUVBLFNBQVM7UUFDVCxJQUFJNEosV0FBV3RLLFFBQVEsQ0FBQyxhQUFhc0ssV0FBV3RLLFFBQVEsQ0FBQyxjQUFjO1lBQ3JFa0ksV0FBV2lCLGdCQUFnQixDQUFDekksSUFBSSxDQUFDO1FBQ25DO1FBQ0EsSUFBSTRKLFdBQVd0SyxRQUFRLENBQUMsZ0JBQWdCO1lBQ3RDa0ksV0FBV2lCLGdCQUFnQixDQUFDekksSUFBSSxDQUFDO1FBQ25DO1FBRUEsT0FBTztRQUNQLElBQUk0SixXQUFXdEssUUFBUSxDQUFDLGtCQUFrQnNLLFdBQVd0SyxRQUFRLENBQUMsV0FBVztZQUN2RWtJLFdBQVdlLFFBQVEsR0FBRztRQUN4QixPQUFPLElBQUlxQixXQUFXdEssUUFBUSxDQUFDLGNBQWNzSyxXQUFXdEssUUFBUSxDQUFDLGFBQWE7WUFDNUVrSSxXQUFXZSxRQUFRLEdBQUc7UUFDeEIsT0FBTztZQUNMZixXQUFXZSxRQUFRLEdBQUc7UUFDeEI7UUFFQSxjQUFjO1FBQ2QsSUFBSWYsV0FBVzhCLGFBQWEsRUFBRTtZQUM1QjlCLFdBQVdrQixjQUFjLEdBQUc5RCxLQUFLa0YsS0FBSyxDQUFDdEMsV0FBVzhCLGFBQWEsR0FBRyxNQUFNMUUsS0FBS21GLE1BQU0sS0FBSztZQUN4RnZDLFdBQVdtQixlQUFlLEdBQUcvRCxLQUFLb0YsR0FBRyxDQUFDLE1BQU14QyxXQUFXOEIsYUFBYSxHQUFHLEtBQUs7UUFDOUUsT0FBTztZQUNMOUIsV0FBV2tCLGNBQWMsR0FBRzlELEtBQUtrRixLQUFLLENBQUNsRixLQUFLbUYsTUFBTSxLQUFLLE1BQU07WUFDN0R2QyxXQUFXbUIsZUFBZSxHQUFHLE1BQU0vRCxLQUFLbUYsTUFBTSxLQUFLO1FBQ3JEO1FBRUEsT0FBT3ZDO0lBQ1QsRUFBRSxPQUFPbkcsT0FBTztRQUNkNEIsUUFBUTVCLEtBQUssQ0FBQyxhQUFhQTtRQUMzQixPQUFPO0lBQ1Q7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxjY2FpXFxEZXNrdG9wXFxEZXZcXENlbGxGb3JnZSBBSVxcZnJvbnRlbmRcXGNvbXBvbmVudHNcXGZvcm1hdHRlZC1tZXNzYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBDb3B5LCBFeHRlcm5hbExpbmssIENoZWNrQ2lyY2xlLCBBbGVydFRyaWFuZ2xlLCBJbmZvLCBMaWdodGJ1bGIgfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJ1xuaW1wb3J0IHsgdG9hc3QgfSBmcm9tICdzb25uZXInXG5pbXBvcnQgeyBJbnRlcmFjdGl2ZUxpdGVyYXR1cmVDaXRhdGlvbiB9IGZyb20gJy4vaW50ZXJhY3RpdmUtbGl0ZXJhdHVyZS1jaXRhdGlvbidcbmltcG9ydCB7IENvbXByZWhlbnNpdmVTb2x1dGlvbkRpc3BsYXkgfSBmcm9tICcuL2NvbXByZWhlbnNpdmUtc29sdXRpb24tZGlzcGxheSdcbmltcG9ydCB7IENvbXByZWhlbnNpdmVTb2x1dGlvbkZyYW1ld29yayB9IGZyb20gJy4vY29tcHJlaGVuc2l2ZS1zb2x1dGlvbi1mcmFtZXdvcmsnXG5pbXBvcnQgU3RyZWFtbGluZWRTb2x1dGlvbkRpc3BsYXkgZnJvbSAnLi9zdHJlYW1saW5lZC1zb2x1dGlvbi1kaXNwbGF5J1xuXG5pbnRlcmZhY2UgRm9ybWF0dGVkTWVzc2FnZVByb3BzIHtcbiAgY29udGVudDogc3RyaW5nXG4gIGNsYXNzTmFtZT86IHN0cmluZ1xufVxuXG5leHBvcnQgZnVuY3Rpb24gRm9ybWF0dGVkTWVzc2FnZSh7IGNvbnRlbnQsIGNsYXNzTmFtZSA9ICcnIH06IEZvcm1hdHRlZE1lc3NhZ2VQcm9wcykge1xuICAvLyDmuLLmn5NNZXJtYWlk5Zu+6KGo55qE566A5YyW54mI5pysXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgcmVuZGVyTWVybWFpZERpYWdyYW1zID0gKCkgPT4ge1xuICAgICAgY29uc3QgZGlhZ3JhbXMgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCcubWVybWFpZC1kaWFncmFtW2RhdGEtbWVybWFpZF06bm90KC5tZXJtYWlkLXByb2Nlc3NlZCknKVxuXG4gICAgICBkaWFncmFtcy5mb3JFYWNoKChkaWFncmFtKSA9PiB7XG4gICAgICAgIGNvbnN0IGVsZW1lbnQgPSBkaWFncmFtIGFzIEhUTUxFbGVtZW50XG4gICAgICAgIGNvbnN0IG1lcm1haWRDb2RlID0gZWxlbWVudC5nZXRBdHRyaWJ1dGUoJ2RhdGEtbWVybWFpZCcpXG5cbiAgICAgICAgaWYgKCFtZXJtYWlkQ29kZSkgcmV0dXJuXG5cbiAgICAgICAgLy8g5qCH6K6w5Li65bey5aSE55CGXG4gICAgICAgIGVsZW1lbnQuY2xhc3NMaXN0LmFkZCgnbWVybWFpZC1wcm9jZXNzZWQnKVxuXG4gICAgICAgIC8vIOWIm+W7uuS4gOS4quabtOWlveeahOWxleekuueVjOmdolxuICAgICAgICBlbGVtZW50LmlubmVySFRNTCA9IGBcbiAgICAgICAgICA8ZGl2IGNsYXNzPVwiYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ibHVlLTUwIHRvLWluZGlnby01MCByb3VuZGVkLWxnIHAtNiBib3JkZXIgYm9yZGVyLWJsdWUtMjAwXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzPVwidGV4dC1jZW50ZXIgbWItNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHB4LTMgcHktMSBiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwIHJvdW5kZWQtZnVsbCB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgPHN2ZyBjbGFzcz1cImgtNCB3LTRcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgZmlsbD1cImN1cnJlbnRDb2xvclwiPlxuICAgICAgICAgICAgICAgICAgPHBhdGggZD1cIk0xMiAyTDIgN2wxMCA1IDEwLTUtMTAtNXpNMiAxN2wxMCA1IDEwLTVNMiAxMmwxMCA1IDEwLTVcIi8+XG4gICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgTWVybWFpZCDlm77ooahcbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3M9XCJiZy13aGl0ZSByb3VuZGVkIGJvcmRlciBib3JkZXItc2xhdGUtMjAwIHAtNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzPVwidGV4dC1jZW50ZXIgdGV4dC1zbGF0ZS02MDAgbWItM1wiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9XCJ0ZXh0LWxnIG1iLTJcIj7wn5OKPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzcz1cInRleHQtc20gZm9udC1tZWRpdW1cIj7lm77ooajlhoXlrrnpooTop4g8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxwcmUgY2xhc3M9XCJ0ZXh0LXhzIHRleHQtc2xhdGUtNzAwIGJnLXNsYXRlLTUwIHAtMyByb3VuZGVkIGJvcmRlciBvdmVyZmxvdy14LWF1dG8gd2hpdGVzcGFjZS1wcmUtd3JhcFwiPiR7bWVybWFpZENvZGV9PC9wcmU+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3M9XCJtdC0zIHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzcz1cInRleHQteHMgdGV4dC1zbGF0ZS01MDBcIj5cbiAgICAgICAgICAgICAgICAgIPCfkqEg5o+Q56S677ya5q2k5Zu+6KGo5YyF5ZCrICR7bWVybWFpZENvZGUuc3BsaXQoJ1xcXFxuJykubGVuZ3RofSDooYzlrprkuYlcbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgYFxuICAgICAgfSlcbiAgICB9XG5cbiAgICAvLyDlu7bov5/lpITnkIbvvIznoa7kv51ET03lt7Lmm7TmlrBcbiAgICBjb25zdCB0aW1lciA9IHNldFRpbWVvdXQocmVuZGVyTWVybWFpZERpYWdyYW1zLCAxMDApXG4gICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dCh0aW1lcilcbiAgfSwgW2NvbnRlbnRdKVxuXG4gIC8vIOWkhOeQhuWGheiBlOagvOW8j++8iOeyl+S9k+OAgeaWnOS9k+OAgeS7o+eggeOAgemTvuaOpeetie+8iVxuICBjb25zdCBmb3JtYXRJbmxpbmVUZXh0ID0gKHRleHQ6IHN0cmluZyk6IFJlYWN0LlJlYWN0Tm9kZVtdID0+IHtcbiAgICAvLyDlpoLmnpzmsqHmnInnibnmrormoLzlvI/vvIznm7TmjqXov5Tlm55cbiAgICBpZiAoIXRleHQuaW5jbHVkZXMoJyoqJykgJiYgIXRleHQuaW5jbHVkZXMoJyonKSAmJiAhdGV4dC5pbmNsdWRlcygnYCcpICYmICF0ZXh0LmluY2x1ZGVzKCd+ficpICYmICF0ZXh0LmluY2x1ZGVzKCdbJykpIHtcbiAgICAgIHJldHVybiBbdGV4dF1cbiAgICB9XG5cbiAgICBjb25zdCBwYXJ0czogUmVhY3QuUmVhY3ROb2RlW10gPSBbXVxuICAgIGxldCBrZXkgPSAwXG5cbiAgICAvLyDmlLnov5vnmoTmraPliJnooajovr7lvI/vvIzmm7Tnsr7noa7lnLDlpITnkIZtYXJrZG93buagvOW8j1xuICAgIC8vIOS8mOWFiOWMuemFjei+g+mVv+eahOagvOW8j++8iOWmgiAqKiog5ZKMICoq77yJ77yM6YG/5YWN6K+v5Yy56YWN77yM5bm25pSv5oyB6ZO+5o6lXG4gICAgY29uc3QgZm9ybWF0UmVnZXggPSAvKFxcWyhbXlxcXV0rPylcXF1cXCgoW14pXSs/KSg/OlxccytcIihbXlwiXSopXCIpP1xcKXxcXCpcXCpcXCooW14qXFxuXSs/KVxcKlxcKlxcKnxcXCpcXCooW14qXFxuXSs/KVxcKlxcKnx+fihbXn5cXG5dKz8pfn58KD88IVxcKilcXCooW14qXFxuXSs/KVxcKig/IVxcKil8YChbXmBcXG5dKz8pYCkvZ1xuICAgIGxldCBsYXN0SW5kZXggPSAwXG4gICAgbGV0IG1hdGNoXG5cbiAgICAvLyDph43nva7mraPliJnooajovr7lvI/nmoRsYXN0SW5kZXhcbiAgICBmb3JtYXRSZWdleC5sYXN0SW5kZXggPSAwXG5cbiAgICB3aGlsZSAoKG1hdGNoID0gZm9ybWF0UmVnZXguZXhlYyh0ZXh0KSkgIT09IG51bGwpIHtcbiAgICAgIC8vIOa3u+WKoOagvOW8j+WJjeeahOaZrumAmuaWh+acrFxuICAgICAgaWYgKG1hdGNoLmluZGV4ID4gbGFzdEluZGV4KSB7XG4gICAgICAgIGNvbnN0IHBsYWluVGV4dCA9IHRleHQuc2xpY2UobGFzdEluZGV4LCBtYXRjaC5pbmRleClcbiAgICAgICAgaWYgKHBsYWluVGV4dCkge1xuICAgICAgICAgIHBhcnRzLnB1c2gocGxhaW5UZXh0KVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIOagueaNruWMuemFjeeahOagvOW8j+a3u+WKoOebuOW6lOeahOWFg+e0oFxuICAgICAgaWYgKG1hdGNoWzJdICYmIG1hdGNoWzNdKSB7XG4gICAgICAgIC8vIOmTvuaOpSBbdGV4dF0odXJsKSDmiJYgW3RleHRdKHVybCBcInRpdGxlXCIpXG4gICAgICAgIGNvbnN0IGxpbmtUZXh0ID0gbWF0Y2hbMl1cbiAgICAgICAgY29uc3QgbGlua1VybCA9IG1hdGNoWzNdXG4gICAgICAgIGNvbnN0IGxpbmtUaXRsZSA9IG1hdGNoWzRdIHx8ICcnXG4gICAgICAgIFxuICAgICAgICBwYXJ0cy5wdXNoKFxuICAgICAgICAgIDxhIFxuICAgICAgICAgICAga2V5PXtrZXkrK30gXG4gICAgICAgICAgICBocmVmPXtsaW5rVXJsfVxuICAgICAgICAgICAgdGl0bGU9e2xpbmtUaXRsZX1cbiAgICAgICAgICAgIHRhcmdldD1cIl9ibGFua1wiXG4gICAgICAgICAgICByZWw9XCJub29wZW5lciBub3JlZmVycmVyXCJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDAgaG92ZXI6dGV4dC1ibHVlLTgwMCB1bmRlcmxpbmUgaG92ZXI6bm8tdW5kZXJsaW5lIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMCBmb250LW1lZGl1bSBpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIHtsaW5rVGV4dH1cbiAgICAgICAgICAgIDxFeHRlcm5hbExpbmsgY2xhc3NOYW1lPVwiaC0zIHctMyBvcGFjaXR5LTYwXCIgLz5cbiAgICAgICAgICA8L2E+XG4gICAgICAgIClcbiAgICAgIH0gZWxzZSBpZiAobWF0Y2hbNV0pIHtcbiAgICAgICAgLy8g57KX5L2T5pac5L2TICoqKnRleHQqKipcbiAgICAgICAgcGFydHMucHVzaChcbiAgICAgICAgICA8c3Ryb25nIGtleT17a2V5Kyt9IGNsYXNzTmFtZT1cImZvbnQtYm9sZCBpdGFsaWMgdGV4dC1zbGF0ZS05MDBcIj5cbiAgICAgICAgICAgIHttYXRjaFs1XX1cbiAgICAgICAgICA8L3N0cm9uZz5cbiAgICAgICAgKVxuICAgICAgfSBlbHNlIGlmIChtYXRjaFs2XSkge1xuICAgICAgICAvLyDnspfkvZMgKip0ZXh0KipcbiAgICAgICAgcGFydHMucHVzaChcbiAgICAgICAgICA8c3Ryb25nIGtleT17a2V5Kyt9IGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1zbGF0ZS05MDBcIj5cbiAgICAgICAgICAgIHttYXRjaFs2XX1cbiAgICAgICAgICA8L3N0cm9uZz5cbiAgICAgICAgKVxuICAgICAgfSBlbHNlIGlmIChtYXRjaFs3XSkge1xuICAgICAgICAvLyDliKDpmaTnur8gfn50ZXh0fn5cbiAgICAgICAgcGFydHMucHVzaChcbiAgICAgICAgICA8c3BhbiBrZXk9e2tleSsrfSBjbGFzc05hbWU9XCJsaW5lLXRocm91Z2ggdGV4dC1zbGF0ZS02MDBcIj5cbiAgICAgICAgICAgIHttYXRjaFs3XX1cbiAgICAgICAgICA8L3NwYW4+XG4gICAgICAgIClcbiAgICAgIH0gZWxzZSBpZiAobWF0Y2hbOF0pIHtcbiAgICAgICAgLy8g5pac5L2TICp0ZXh0KiAo5L2/55So6LSf5ZCR5YmN55675ZKM5ZCO55676YG/5YWN5LiOKirlhrLnqoEpXG4gICAgICAgIHBhcnRzLnB1c2goXG4gICAgICAgICAgPGVtIGtleT17a2V5Kyt9IGNsYXNzTmFtZT1cIml0YWxpYyB0ZXh0LXNsYXRlLTgwMFwiPlxuICAgICAgICAgICAge21hdGNoWzhdfVxuICAgICAgICAgIDwvZW0+XG4gICAgICAgIClcbiAgICAgIH0gZWxzZSBpZiAobWF0Y2hbOV0pIHtcbiAgICAgICAgLy8g5Luj56CBIGB0ZXh0YFxuICAgICAgICBwYXJ0cy5wdXNoKFxuICAgICAgICAgIDxjb2RlIGtleT17a2V5Kyt9IGNsYXNzTmFtZT1cImJnLXNsYXRlLTEwMCB0ZXh0LXNsYXRlLTgwMCBweC0xIHB5LTAuNSByb3VuZGVkIHRleHQtc20gZm9udC1tb25vXCI+XG4gICAgICAgICAgICB7bWF0Y2hbOV19XG4gICAgICAgICAgPC9jb2RlPlxuICAgICAgICApXG4gICAgICB9XG5cbiAgICAgIGxhc3RJbmRleCA9IG1hdGNoLmluZGV4ICsgbWF0Y2hbMF0ubGVuZ3RoXG4gICAgfVxuXG4gICAgLy8g5re75Yqg5Ymp5L2Z55qE5pmu6YCa5paH5pysXG4gICAgaWYgKGxhc3RJbmRleCA8IHRleHQubGVuZ3RoKSB7XG4gICAgICBjb25zdCByZW1haW5pbmdQbGFpblRleHQgPSB0ZXh0LnNsaWNlKGxhc3RJbmRleClcbiAgICAgIGlmIChyZW1haW5pbmdQbGFpblRleHQpIHtcbiAgICAgICAgcGFydHMucHVzaChyZW1haW5pbmdQbGFpblRleHQpXG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIHBhcnRzLmxlbmd0aCA+IDAgPyBwYXJ0cyA6IFt0ZXh0XVxuICB9XG5cbiAgLy8g5aSN5Yi25paH5pys5Yiw5Ymq6LS05p2/XG4gIGNvbnN0IGNvcHlUb0NsaXBib2FyZCA9ICh0ZXh0OiBzdHJpbmcpID0+IHtcbiAgICBuYXZpZ2F0b3IuY2xpcGJvYXJkLndyaXRlVGV4dCh0ZXh0KS50aGVuKCgpID0+IHtcbiAgICAgIHRvYXN0LnN1Y2Nlc3MoJ+W3suWkjeWItuWIsOWJqui0tOadvycpXG4gICAgfSkuY2F0Y2goKCkgPT4ge1xuICAgICAgdG9hc3QuZXJyb3IoJ+WkjeWItuWksei0pScpXG4gICAgfSlcbiAgfVxuXG4gIGNvbnN0IGZvcm1hdENvbnRlbnQgPSAodGV4dDogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgbGluZXMgPSB0ZXh0LnNwbGl0KCdcXG4nKVxuICAgIGNvbnN0IGVsZW1lbnRzOiBSZWFjdC5SZWFjdE5vZGVbXSA9IFtdXG4gICAgbGV0IGtleSA9IDBcbiAgICBsZXQgaW5Db2RlQmxvY2sgPSBmYWxzZVxuICAgIGxldCBjb2RlQmxvY2tDb250ZW50OiBzdHJpbmdbXSA9IFtdXG4gICAgbGV0IGNvZGVCbG9ja0xhbmd1YWdlID0gJydcblxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbGluZXMubGVuZ3RoOyBpKyspIHtcbiAgICAgIGNvbnN0IGxpbmUgPSBsaW5lc1tpXVxuICAgICAgY29uc3QgdHJpbW1lZExpbmUgPSBsaW5lLnRyaW0oKVxuXG4gICAgICAvLyDlpITnkIbku6PnoIHlnZdcbiAgICAgIGlmICh0cmltbWVkTGluZS5zdGFydHNXaXRoKCdgYGAnKSkge1xuICAgICAgICBpZiAoIWluQ29kZUJsb2NrKSB7XG4gICAgICAgICAgLy8g5byA5aeL5Luj56CB5Z2XXG4gICAgICAgICAgaW5Db2RlQmxvY2sgPSB0cnVlXG4gICAgICAgICAgY29kZUJsb2NrTGFuZ3VhZ2UgPSB0cmltbWVkTGluZS5zbGljZSgzKS50cmltKClcbiAgICAgICAgICBjb2RlQmxvY2tDb250ZW50ID0gW11cbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAvLyDnu5PmnZ/ku6PnoIHlnZdcbiAgICAgICAgICBpbkNvZGVCbG9jayA9IGZhbHNlXG4gICAgICAgICAgY29uc3QgY29kZUNvbnRlbnQgPSBjb2RlQmxvY2tDb250ZW50LmpvaW4oJ1xcbicpXG5cbiAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKbmmK/nu7zlkIjop6PlhrPmlrnmoYjmiJbnu7zlkIjmlrnmoYjmoYbmnrbnmoRKU09O5pWw5o2uXG4gICAgICAgICAgaWYgKGNvZGVCbG9ja0xhbmd1YWdlID09PSAnanNvbicpIHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgIGNvbnN0IGpzb25EYXRhID0gSlNPTi5wYXJzZShjb2RlQ29udGVudClcbiAgICAgICAgICAgICAgaWYgKGpzb25EYXRhLnR5cGUgPT09ICdjb21wcmVoZW5zaXZlX2ZyYW1ld29yaycgJiYganNvbkRhdGEuZGF0YSkge1xuICAgICAgICAgICAgICAgIC8vIOa4suafk+e7vOWQiOaWueahiOahhuaetue7hOS7tu+8iOaMieiuvuiuoeaWh+aho+imgeaxgu+8iVxuICAgICAgICAgICAgICAgIGVsZW1lbnRzLnB1c2goXG4gICAgICAgICAgICAgICAgICA8ZGl2IGtleT17a2V5Kyt9IGNsYXNzTmFtZT1cIm15LTZcIj5cbiAgICAgICAgICAgICAgICAgICAgPENvbXByZWhlbnNpdmVTb2x1dGlvbkZyYW1ld29ya1xuICAgICAgICAgICAgICAgICAgICAgIGZyYW1ld29ya0RhdGE9e2pzb25EYXRhLmRhdGF9XG4gICAgICAgICAgICAgICAgICAgICAgb25TZWFyY2hDbGljaz17KHBsYXRmb3JtOiBzdHJpbmcsIHVybDogc3RyaW5nKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICB3aW5kb3cub3Blbih1cmwsICdfYmxhbmsnKVxuICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgb25PcHRpbWl6ZT17KGZlZWRiYWNrOiBhbnkpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfkvJjljJblj43ppog6JywgZmVlZGJhY2spXG4gICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgfSBlbHNlIGlmIChqc29uRGF0YS50eXBlID09PSAnY29tcHJlaGVuc2l2ZV9zb2x1dGlvbicgJiYganNvbkRhdGEuZGF0YSkge1xuICAgICAgICAgICAgICAgIC8vIOa4suafk+e7vOWQiOino+WGs+aWueahiOe7hOS7tu+8iOmZjee6p+aWueahiO+8iVxuICAgICAgICAgICAgICAgIGVsZW1lbnRzLnB1c2goXG4gICAgICAgICAgICAgICAgICA8ZGl2IGtleT17a2V5Kyt9IGNsYXNzTmFtZT1cIm15LTZcIj5cbiAgICAgICAgICAgICAgICAgICAgPENvbXByZWhlbnNpdmVTb2x1dGlvbkRpc3BsYXkgc29sdXRpb25EYXRhPXtqc29uRGF0YS5kYXRhfSAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIC8vIOaZrumAmkpTT07ku6PnoIHlnZdcbiAgICAgICAgICAgICAgICBlbGVtZW50cy5wdXNoKFxuICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2tleSsrfSBjbGFzc05hbWU9XCJteS00IHJlbGF0aXZlIGdyb3VwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctc2xhdGUtOTAwIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcHgtNCBweS0yIGJnLXNsYXRlLTgwMCBib3JkZXItYiBib3JkZXItc2xhdGUtNzAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtc2xhdGUtMzAwIGZvbnQtbWVkaXVtXCI+SlNPTjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gY29weVRvQ2xpcGJvYXJkKGNvZGVDb250ZW50KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC02IHB4LTIgdGV4dC1zbGF0ZS00MDAgaG92ZXI6dGV4dC13aGl0ZSBob3ZlcjpiZy1zbGF0ZS03MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Q29weSBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxwcmUgY2xhc3NOYW1lPVwicC00IHRleHQtc20gdGV4dC1zbGF0ZS0xMDAgb3ZlcmZsb3cteC1hdXRvXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8Y29kZT57Y29kZUNvbnRlbnR9PC9jb2RlPlxuICAgICAgICAgICAgICAgICAgICAgIDwvcHJlPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSBjYXRjaCAoanNvbkVycm9yKSB7XG4gICAgICAgICAgICAgIC8vIEpTT07op6PmnpDlpLHotKXvvIzkvZzkuLrmma7pgJrku6PnoIHlnZflpITnkIZcbiAgICAgICAgICAgICAgZWxlbWVudHMucHVzaChcbiAgICAgICAgICAgICAgICA8ZGl2IGtleT17a2V5Kyt9IGNsYXNzTmFtZT1cIm15LTQgcmVsYXRpdmUgZ3JvdXBcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctc2xhdGUtOTAwIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHB4LTQgcHktMiBiZy1zbGF0ZS04MDAgYm9yZGVyLWIgYm9yZGVyLXNsYXRlLTcwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1zbGF0ZS0zMDAgZm9udC1tZWRpdW1cIj5KU09OPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gY29weVRvQ2xpcGJvYXJkKGNvZGVDb250ZW50KX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtNiBweC0yIHRleHQtc2xhdGUtNDAwIGhvdmVyOnRleHQtd2hpdGUgaG92ZXI6Ymctc2xhdGUtNzAwXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8Q29weSBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxwcmUgY2xhc3NOYW1lPVwicC00IHRleHQtc20gdGV4dC1zbGF0ZS0xMDAgb3ZlcmZsb3cteC1hdXRvXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGNvZGU+e2NvZGVDb250ZW50fTwvY29kZT5cbiAgICAgICAgICAgICAgICAgICAgPC9wcmU+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKbmmK9NZXJtYWlk5Zu+6KGoXG4gICAgICAgICAgZWxzZSBpZiAoY29kZUJsb2NrTGFuZ3VhZ2UgPT09ICdtZXJtYWlkJyB8fCBjb2RlQ29udGVudC50cmltKCkubWF0Y2goL14oZ3JhcGh8cGllfGdhbnR0fHNlcXVlbmNlRGlhZ3JhbXxjbGFzc0RpYWdyYW18c3RhdGVEaWFncmFtfGpvdXJuZXl8Z2l0Z3JhcGh8Zmxvd2NoYXJ0KS8pKSB7XG4gICAgICAgICAgICBlbGVtZW50cy5wdXNoKFxuICAgICAgICAgICAgICA8ZGl2IGtleT17a2V5Kyt9IGNsYXNzTmFtZT1cIm15LTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIGJvcmRlciBib3JkZXItc2xhdGUtMjAwIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBweC00IHB5LTIgYmctc2xhdGUtNTAgYm9yZGVyLWIgYm9yZGVyLXNsYXRlLTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtc2xhdGUtNjAwIGZvbnQtbWVkaXVtIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJoLTQgdy00XCIgdmlld0JveD1cIjAgMCAyNCAyNFwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNMTIgMkwyIDdsMTAgNSAxMC01LTEwLTV6TTIgMTdsMTAgNSAxMC01TTIgMTJsMTAgNSAxMC01XCIvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgIE1lcm1haWQg5Zu+6KGoXG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBjb3B5VG9DbGlwYm9hcmQoY29kZUNvbnRlbnQpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtNiBweC0yIHRleHQtc2xhdGUtNDAwIGhvdmVyOnRleHQtc2xhdGUtNjAwXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxDb3B5IGNsYXNzTmFtZT1cImgtMyB3LTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1lcm1haWQtZGlhZ3JhbSBiZy13aGl0ZSByb3VuZGVkIGJvcmRlciBib3JkZXItc2xhdGUtMTAwIHAtNCB0ZXh0LWNlbnRlciBtaW4taC1bMjAwcHhdIGZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCJcbiAgICAgICAgICAgICAgICAgICAgICBkYXRhLW1lcm1haWQ9e2NvZGVDb250ZW50fVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNsYXRlLTUwMCB0ZXh0LXNtIG1iLTJcIj7wn46oIOato+WcqOa4suafk+WbvuihqC4uLjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNsYXRlLTQwMCBiZy1zbGF0ZS01MCBwLTIgcm91bmRlZCB0ZXh0LWxlZnQgb3ZlcmZsb3cteC1hdXRvIG1heC13LWZ1bGxcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwcmUgY2xhc3NOYW1lPVwid2hpdGVzcGFjZS1wcmUtd3JhcFwiPntjb2RlQ29udGVudH08L3ByZT5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApXG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIC8vIOaZrumAmuS7o+eggeWdl1xuICAgICAgICAgICAgZWxlbWVudHMucHVzaChcbiAgICAgICAgICAgICAgPGRpdiBrZXk9e2tleSsrfSBjbGFzc05hbWU9XCJteS00IHJlbGF0aXZlIGdyb3VwXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1zbGF0ZS05MDAgcm91bmRlZC1sZyBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHB4LTQgcHktMiBiZy1zbGF0ZS04MDAgYm9yZGVyLWIgYm9yZGVyLXNsYXRlLTcwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtc2xhdGUtMzAwIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2NvZGVCbG9ja0xhbmd1YWdlIHx8ICdDb2RlJ31cbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGNvcHlUb0NsaXBib2FyZChjb2RlQ29udGVudCl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC02IHB4LTIgdGV4dC1zbGF0ZS00MDAgaG92ZXI6dGV4dC13aGl0ZSBob3ZlcjpiZy1zbGF0ZS03MDBcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPENvcHkgY2xhc3NOYW1lPVwiaC0zIHctM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8cHJlIGNsYXNzTmFtZT1cInAtNCB0ZXh0LXNtIHRleHQtc2xhdGUtMTAwIG92ZXJmbG93LXgtYXV0b1wiPlxuICAgICAgICAgICAgICAgICAgICA8Y29kZT57Y29kZUNvbnRlbnR9PC9jb2RlPlxuICAgICAgICAgICAgICAgICAgPC9wcmU+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKVxuICAgICAgICAgIH1cblxuICAgICAgICAgIGNvZGVCbG9ja0NvbnRlbnQgPSBbXVxuICAgICAgICAgIGNvZGVCbG9ja0xhbmd1YWdlID0gJydcbiAgICAgICAgfVxuICAgICAgICBjb250aW51ZVxuICAgICAgfVxuXG4gICAgICAvLyDlpoLmnpzlnKjku6PnoIHlnZflhoXvvIzmlLbpm4blhoXlrrlcbiAgICAgIGlmIChpbkNvZGVCbG9jaykge1xuICAgICAgICBjb2RlQmxvY2tDb250ZW50LnB1c2gobGluZSlcbiAgICAgICAgY29udGludWVcbiAgICAgIH1cblxuICAgICAgLy8gU2tpcCBlbXB0eSBsaW5lcyBidXQgYWRkIHNwYWNpbmdcbiAgICAgIGlmICghdHJpbW1lZExpbmUpIHtcbiAgICAgICAgZWxlbWVudHMucHVzaCg8ZGl2IGtleT17a2V5Kyt9IGNsYXNzTmFtZT1cImgtMlwiIC8+KVxuICAgICAgICBjb250aW51ZVxuICAgICAgfVxuXG4gICAgICAvLyBNYXJrZG93buagh+mimOaUr+aMgSAoIyMjICMjICMpXG4gICAgICBpZiAodHJpbW1lZExpbmUubWF0Y2goL14jezEsNn1cXHMrKC4rKS8pKSB7XG4gICAgICAgIGNvbnN0IG1hdGNoID0gdHJpbW1lZExpbmUubWF0Y2goL14oI3sxLDZ9KVxccysoLispLylcbiAgICAgICAgaWYgKG1hdGNoKSB7XG4gICAgICAgICAgY29uc3QgWywgaGFzaGVzLCB0aXRsZV0gPSBtYXRjaFxuICAgICAgICAgIGNvbnN0IGxldmVsID0gaGFzaGVzLmxlbmd0aFxuXG4gICAgICAgICAgbGV0IGNsYXNzTmFtZSA9IFwiXCJcbiAgICAgICAgICBsZXQgQ29tcG9uZW50ID0gXCJoMVwiIGFzIGtleW9mIFJlYWN0LkpTWC5JbnRyaW5zaWNFbGVtZW50c1xuXG4gICAgICAgICAgc3dpdGNoIChsZXZlbCkge1xuICAgICAgICAgICAgY2FzZSAxOlxuICAgICAgICAgICAgICBDb21wb25lbnQgPSBcImgxXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lID0gXCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1zbGF0ZS05MDAgbXQtNiBtYi00IHBiLTIgYm9yZGVyLWIgYm9yZGVyLXNsYXRlLTIwMFwiXG4gICAgICAgICAgICAgIGJyZWFrXG4gICAgICAgICAgICBjYXNlIDI6XG4gICAgICAgICAgICAgIENvbXBvbmVudCA9IFwiaDJcIlxuICAgICAgICAgICAgICBjbGFzc05hbWUgPSBcInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LXNsYXRlLTgwMCBtdC01IG1iLTMgcGItMSBib3JkZXItYiBib3JkZXItc2xhdGUtMTAwXCJcbiAgICAgICAgICAgICAgYnJlYWtcbiAgICAgICAgICAgIGNhc2UgMzpcbiAgICAgICAgICAgICAgQ29tcG9uZW50ID0gXCJoM1wiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZSA9IFwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtc2xhdGUtODAwIG10LTQgbWItMlwiXG4gICAgICAgICAgICAgIGJyZWFrXG4gICAgICAgICAgICBjYXNlIDQ6XG4gICAgICAgICAgICAgIENvbXBvbmVudCA9IFwiaDRcIlxuICAgICAgICAgICAgICBjbGFzc05hbWUgPSBcInRleHQtYmFzZSBmb250LW1lZGl1bSB0ZXh0LXNsYXRlLTcwMCBtdC0zIG1iLTJcIlxuICAgICAgICAgICAgICBicmVha1xuICAgICAgICAgICAgY2FzZSA1OlxuICAgICAgICAgICAgICBDb21wb25lbnQgPSBcImg1XCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lID0gXCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtc2xhdGUtNzAwIG10LTIgbWItMVwiXG4gICAgICAgICAgICAgIGJyZWFrXG4gICAgICAgICAgICBjYXNlIDY6XG4gICAgICAgICAgICAgIENvbXBvbmVudCA9IFwiaDZcIlxuICAgICAgICAgICAgICBjbGFzc05hbWUgPSBcInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1zbGF0ZS02MDAgbXQtMiBtYi0xXCJcbiAgICAgICAgICAgICAgYnJlYWtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICBlbGVtZW50cy5wdXNoKFxuICAgICAgICAgICAgPGRpdiBrZXk9e2tleSsrfT5cbiAgICAgICAgICAgICAgPENvbXBvbmVudCBjbGFzc05hbWU9e2NsYXNzTmFtZX0+XG4gICAgICAgICAgICAgICAge2Zvcm1hdElubGluZVRleHQodGl0bGUpfVxuICAgICAgICAgICAgICA8L0NvbXBvbmVudD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIClcbiAgICAgICAgfVxuICAgICAgICBjb250aW51ZVxuICAgICAgfVxuXG4gICAgICAvLyBNYWluIHRpdGxlICjwn46vIGF0IHN0YXJ0KVxuICAgICAgaWYgKHRyaW1tZWRMaW5lLm1hdGNoKC9e8J+Or1xccysoLispLykpIHtcbiAgICAgICAgY29uc3QgdGl0bGUgPSB0cmltbWVkTGluZS5yZXBsYWNlKC9e8J+Or1xccysvLCAnJylcbiAgICAgICAgZWxlbWVudHMucHVzaChcbiAgICAgICAgICA8ZGl2IGtleT17a2V5Kyt9IGNsYXNzTmFtZT1cIm1iLTRcIj5cbiAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LWJsdWUtOTAwIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgIDxzcGFuPvCfjq88L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuPntmb3JtYXRJbmxpbmVUZXh0KHRpdGxlKX08L3NwYW4+XG4gICAgICAgICAgICA8L2gxPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApXG4gICAgICAgIGNvbnRpbnVlXG4gICAgICB9XG5cbiAgICAgIC8vIFNlY3Rpb24gaGVhZGVycyB3aXRoIGVtb2ppcyAo8J+Tiiwg8J+UrCwg8J+SsCwg4o+wLCDimqDvuI8sIPCfp6wsIPCfj4YsIPCfpJ0sIGV0Yy4pXG4gICAgICBpZiAodHJpbW1lZExpbmUubWF0Y2goL15b8J+TivCflKzwn5Kw4o+w4pqg77iP8J+Or/Cfk4vwn5OF4pqh8J+boe+4j/Cfk57wn5SN8J+SofCfmoDwn6es8J+PhvCfpJ3wn5Oa8J+TiF1cXHMrKC4rKS8pKSB7XG4gICAgICAgIGNvbnN0IG1hdGNoID0gdHJpbW1lZExpbmUubWF0Y2goL14oW/Cfk4rwn5Ss8J+SsOKPsOKaoO+4j/Cfjq/wn5OL8J+TheKaofCfm6HvuI/wn5Oe8J+UjfCfkqHwn5qA8J+nrPCfj4bwn6Sd8J+TmvCfk4hdKVxccysoLispLylcbiAgICAgICAgaWYgKG1hdGNoKSB7XG4gICAgICAgICAgY29uc3QgWywgZW1vamksIHRpdGxlXSA9IG1hdGNoXG4gICAgICAgICAgY29uc3QgaXNJbnRlbGxpZ2VudEZlYXR1cmUgPSBbJ/Cfp6wnLCAn8J+PhicsICfwn6SdJywgJ/Cfk5onLCAn8J+TiCddLmluY2x1ZGVzKGVtb2ppKVxuICAgICAgICAgIGVsZW1lbnRzLnB1c2goXG4gICAgICAgICAgICA8ZGl2IGtleT17a2V5Kyt9IGNsYXNzTmFtZT1cIm10LTYgbWItM1wiPlxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPXtgdGV4dC1sZyBmb250LXNlbWlib2xkIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yIGJvcmRlci1iIHBiLTIgJHtcbiAgICAgICAgICAgICAgICBpc0ludGVsbGlnZW50RmVhdHVyZSBcbiAgICAgICAgICAgICAgICAgID8gJ3RleHQtYmx1ZS04MDAgYm9yZGVyLWJsdWUtMjAwIGJnLWJsdWUtNTAgcHgtMyBweS0yIHJvdW5kZWQtdC1sZycgXG4gICAgICAgICAgICAgICAgICA6ICd0ZXh0LXNsYXRlLTgwMCBib3JkZXItc2xhdGUtMjAwJ1xuICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14bFwiPntlbW9qaX08L3NwYW4+XG4gICAgICAgICAgICAgICAgPHNwYW4+e2Zvcm1hdElubGluZVRleHQodGl0bGUpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICB7aXNJbnRlbGxpZ2VudEZlYXR1cmUgJiYgKFxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyBiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHB4LTIgcHktMSByb3VuZGVkLWZ1bGwgbWwtYXV0b1wiPlxuICAgICAgICAgICAgICAgICAgICBBSeaZuuiDveaOqOiNkFxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApXG4gICAgICAgIH1cbiAgICAgICAgY29udGludWVcbiAgICAgIH1cblxuICAgICAgLy8gU3Vic2VjdGlvbiBoZWFkZXJzICjwn4yfLCDwn5KhLCDwn46vLCBldGMuKVxuICAgICAgaWYgKHRyaW1tZWRMaW5lLm1hdGNoKC9eW/CfjJ/wn5Kh8J+Or/Cfk4XimqFdXFxzKyguKyk6LykpIHtcbiAgICAgICAgY29uc3QgbWF0Y2ggPSB0cmltbWVkTGluZS5tYXRjaCgvXihb8J+Mn/CfkqHwn46v8J+TheKaoV0pXFxzKyguKyk6Py8pXG4gICAgICAgIGlmIChtYXRjaCkge1xuICAgICAgICAgIGNvbnN0IFssIGVtb2ppLCB0aXRsZV0gPSBtYXRjaFxuICAgICAgICAgIGVsZW1lbnRzLnB1c2goXG4gICAgICAgICAgICA8ZGl2IGtleT17a2V5Kyt9IGNsYXNzTmFtZT1cIm10LTQgbWItMlwiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1iYXNlIGZvbnQtbWVkaXVtIHRleHQtc2xhdGUtNzAwIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4+e2Vtb2ppfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8c3Bhbj57Zm9ybWF0SW5saW5lVGV4dCh0aXRsZSl9PC9zcGFuPlxuICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKVxuICAgICAgICB9XG4gICAgICAgIGNvbnRpbnVlXG4gICAgICB9XG5cbiAgICAgIC8vIE1hcmtkb3du5YiG6ZqU57q/5pSv5oyBICgtLS0gKioqIF9fXylcbiAgICAgIGlmICh0cmltbWVkTGluZS5tYXRjaCgvXigtezMsfXxcXCp7Myx9fF97Myx9KSQvKSkge1xuICAgICAgICBlbGVtZW50cy5wdXNoKFxuICAgICAgICAgIDxkaXYga2V5PXtrZXkrK30gY2xhc3NOYW1lPVwibXktNFwiPlxuICAgICAgICAgICAgPGhyIGNsYXNzTmFtZT1cImJvcmRlci1zbGF0ZS0zMDAgYm9yZGVyLXQtMlwiIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIClcbiAgICAgICAgY29udGludWVcbiAgICAgIH1cblxuICAgICAgLy8gU2VwYXJhdG9yIGxpbmVzICjilIHilIHilIEpXG4gICAgICBpZiAodHJpbW1lZExpbmUubWF0Y2goL17ilIErJC8pKSB7XG4gICAgICAgIGVsZW1lbnRzLnB1c2goXG4gICAgICAgICAgPGRpdiBrZXk9e2tleSsrfSBjbGFzc05hbWU9XCJteS00XCI+XG4gICAgICAgICAgICA8aHIgY2xhc3NOYW1lPVwiYm9yZGVyLXNsYXRlLTMwMCBib3JkZXItdC0yXCIgLz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKVxuICAgICAgICBjb250aW51ZVxuICAgICAgfVxuXG4gICAgICAvLyBNYXJrZG93buaXoOW6j+WIl+ihqOaUr+aMgSAoLSAqICspXG4gICAgICBpZiAodHJpbW1lZExpbmUubWF0Y2goL15bXFxzXSpbLVxcKlxcK11cXHMrKC4rKS8pKSB7XG4gICAgICAgIGNvbnN0IG1hdGNoID0gdHJpbW1lZExpbmUubWF0Y2goL14oXFxzKikoWy1cXCpcXCtdKVxccysoLispLylcbiAgICAgICAgaWYgKG1hdGNoKSB7XG4gICAgICAgICAgY29uc3QgWywgaW5kZW50LCBidWxsZXQsIHRleHRdID0gbWF0Y2hcbiAgICAgICAgICBjb25zdCBpbmRlbnRMZXZlbCA9IE1hdGguZmxvb3IoaW5kZW50Lmxlbmd0aCAvIDIpIC8vIOavjzLkuKrnqbrmoLzkuLrkuIDnuqfnvKnov5tcbiAgICAgICAgICBjb25zdCBtYXJnaW5MZWZ0ID0gaW5kZW50TGV2ZWwgKiAyMCArIDE2IC8vIOWfuuehgDE2cHggKyDmr4/nuqcyMHB4XG5cbiAgICAgICAgICBlbGVtZW50cy5wdXNoKFxuICAgICAgICAgICAgPGRpdiBrZXk9e2tleSsrfSBjbGFzc05hbWU9XCJtYi0xIGZsZXggaXRlbXMtc3RhcnQgZ2FwLTJcIiBzdHlsZT17eyBtYXJnaW5MZWZ0OiBgJHttYXJnaW5MZWZ0fXB4YCB9fT5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibXQtMC41IHRleHQtc2xhdGUtNjAwIGZvbnQtYm9sZFwiPlxuICAgICAgICAgICAgICAgIOKAolxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc2xhdGUtNzAwIGxlYWRpbmctcmVsYXhlZFwiPntmb3JtYXRJbmxpbmVUZXh0KHRleHQpfTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIClcbiAgICAgICAgfVxuICAgICAgICBjb250aW51ZVxuICAgICAgfVxuXG4gICAgICAvLyBCdWxsZXQgcG9pbnRzICjigKIgb3Ig4pyFKVxuICAgICAgaWYgKHRyaW1tZWRMaW5lLm1hdGNoKC9eW+KAouKchV1cXHMrKC4rKS8pKSB7XG4gICAgICAgIGNvbnN0IG1hdGNoID0gdHJpbW1lZExpbmUubWF0Y2goL14oW+KAouKchV0pXFxzKyguKykvKVxuICAgICAgICBpZiAobWF0Y2gpIHtcbiAgICAgICAgICBjb25zdCBbLCBidWxsZXQsIHRleHRdID0gbWF0Y2hcbiAgICAgICAgICBjb25zdCBpc0NoZWNrbWFyayA9IGJ1bGxldCA9PT0gJ+KchSdcbiAgICAgICAgICBlbGVtZW50cy5wdXNoKFxuICAgICAgICAgICAgPGRpdiBrZXk9e2tleSsrfSBjbGFzc05hbWU9XCJtbC00IG1iLTEgZmxleCBpdGVtcy1zdGFydCBnYXAtMlwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BtdC0wLjUgJHtpc0NoZWNrbWFyayA/ICd0ZXh0LWdyZWVuLTYwMCcgOiAndGV4dC1zbGF0ZS02MDAnfWB9PlxuICAgICAgICAgICAgICAgIHtidWxsZXR9XG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbGF0ZS03MDAgbGVhZGluZy1yZWxheGVkXCI+e2Zvcm1hdElubGluZVRleHQodGV4dCl9PC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKVxuICAgICAgICB9XG4gICAgICAgIGNvbnRpbnVlXG4gICAgICB9XG5cbiAgICAgIC8vIE51bWJlcmVkIGxpc3RzICgxLiwgMi4sIGV0Yy4pXG4gICAgICBpZiAodHJpbW1lZExpbmUubWF0Y2goL15cXGQrXFwuXFxzKyguKykvKSkge1xuICAgICAgICBjb25zdCBtYXRjaCA9IHRyaW1tZWRMaW5lLm1hdGNoKC9eKFxcZCspXFwuXFxzKyguKykvKVxuICAgICAgICBpZiAobWF0Y2gpIHtcbiAgICAgICAgICBjb25zdCBbLCBudW1iZXIsIHRleHRdID0gbWF0Y2hcbiAgICAgICAgICBlbGVtZW50cy5wdXNoKFxuICAgICAgICAgICAgPGRpdiBrZXk9e2tleSsrfSBjbGFzc05hbWU9XCJtbC00IG1iLTIgZmxleCBpdGVtcy1zdGFydCBnYXAtM1wiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwIHRleHQtc20gZm9udC1tZWRpdW0gcHgtMiBweS0wLjUgcm91bmRlZC1mdWxsIG1pbi13LVsyNHB4XSB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIHtudW1iZXJ9XG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbGF0ZS03MDAgbGVhZGluZy1yZWxheGVkXCI+e2Zvcm1hdElubGluZVRleHQodGV4dCl9PC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKVxuICAgICAgICB9XG4gICAgICAgIGNvbnRpbnVlXG4gICAgICB9XG5cbiAgICAgIC8vIEtleS12YWx1ZSBwYWlycyB3aXRoIGNvbG9uICjnoJTnqbbnm67moIfvvJp4eHgpXG4gICAgICBpZiAodHJpbW1lZExpbmUuaW5jbHVkZXMoJ++8micpICYmICF0cmltbWVkTGluZS5tYXRjaCgvXlvwn5OK8J+UrPCfkrDij7DimqDvuI/wn46v8J+Ti/Cfk4XimqHwn5uh77iP8J+TnvCflI3wn5Kh8J+agF0vKSkge1xuICAgICAgICBjb25zdCBwYXJ0cyA9IHRyaW1tZWRMaW5lLnNwbGl0KCfvvJonKVxuICAgICAgICBpZiAocGFydHMubGVuZ3RoID09PSAyKSB7XG4gICAgICAgICAgY29uc3Qga2V5VGV4dCA9IHBhcnRzWzBdLnRyaW0oKVxuICAgICAgICAgIGNvbnN0IHZhbHVlID0gcGFydHNbMV0udHJpbSgpXG5cbiAgICAgICAgICAvLyBTcGVjaWFsIHN0eWxpbmcgZm9yIGRpZmZlcmVudCB0eXBlcyBvZiBpbmZvcm1hdGlvblxuICAgICAgICAgIGNvbnN0IGlzSW1wb3J0YW50ID0ga2V5VGV4dC5pbmNsdWRlcygn6aKE566XJykgfHwga2V5VGV4dC5pbmNsdWRlcygn6LS555SoJykgfHwga2V5VGV4dC5pbmNsdWRlcygn5oiQ5pysJylcbiAgICAgICAgICBjb25zdCBpc1RpbWUgPSBrZXlUZXh0LmluY2x1ZGVzKCfml7bpl7QnKSB8fCBrZXlUZXh0LmluY2x1ZGVzKCflkajmnJ8nKSB8fCBrZXlUZXh0LmluY2x1ZGVzKCfpmLbmrrUnKVxuXG4gICAgICAgICAgZWxlbWVudHMucHVzaChcbiAgICAgICAgICAgIDxkaXYga2V5PXtrZXkrK30gY2xhc3NOYW1lPVwibWItMiBmbGV4IGl0ZW1zLXN0YXJ0XCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YGZvbnQtbWVkaXVtIG1pbi13LVsxMjBweF0gJHtcbiAgICAgICAgICAgICAgICBpc0ltcG9ydGFudCA/ICd0ZXh0LWdyZWVuLTcwMCcgOlxuICAgICAgICAgICAgICAgIGlzVGltZSA/ICd0ZXh0LWJsdWUtNzAwJyA6XG4gICAgICAgICAgICAgICAgJ3RleHQtc2xhdGUtODAwJ1xuICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAge2tleVRleHR977yaXG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgdGV4dC1zbGF0ZS03MDAgJHtcbiAgICAgICAgICAgICAgICBpc0ltcG9ydGFudCA/ICdmb250LW1lZGl1bSB0ZXh0LWdyZWVuLTgwMCcgOiAnJ1xuICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAge2Zvcm1hdElubGluZVRleHQodmFsdWUpfVxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApXG4gICAgICAgICAgY29udGludWVcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBDb3N0L2ZlZSBpbmZvcm1hdGlvbiAowqUgc3ltYm9sKVxuICAgICAgaWYgKHRyaW1tZWRMaW5lLmluY2x1ZGVzKCfCpScpKSB7XG4gICAgICAgIC8vIEV4dHJhY3QgY29zdCByYW5nZXMgYW5kIGhpZ2hsaWdodCB0aGVtXG4gICAgICAgIGNvbnN0IGNvc3RQYXR0ZXJuID0gL8KlKFtcXGQsLi1dKykvZ1xuICAgICAgICBjb25zdCBwYXJ0cyA9IHRyaW1tZWRMaW5lLnNwbGl0KGNvc3RQYXR0ZXJuKVxuICAgICAgICBjb25zdCBmb3JtYXR0ZWRQYXJ0cyA9IHBhcnRzLm1hcCgocGFydCwgaW5kZXgpID0+IHtcbiAgICAgICAgICBpZiAocGFydC5tYXRjaCgvXltcXGQsLi1dKyQvKSkge1xuICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgPHNwYW4ga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWdyZWVuLTcwMCBiZy1ncmVlbi01MCBweC0xIHJvdW5kZWRcIj5cbiAgICAgICAgICAgICAgICDCpXtwYXJ0fVxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICApXG4gICAgICAgICAgfVxuICAgICAgICAgIHJldHVybiBwYXJ0XG4gICAgICAgIH0pXG5cbiAgICAgICAgZWxlbWVudHMucHVzaChcbiAgICAgICAgICA8ZGl2IGtleT17a2V5Kyt9IGNsYXNzTmFtZT1cIm1iLTEgdGV4dC1zbGF0ZS03MDBcIj5cbiAgICAgICAgICAgIHtmb3JtYXR0ZWRQYXJ0c31cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKVxuICAgICAgICBjb250aW51ZVxuICAgICAgfVxuXG4gICAgICAvLyDooajmoLzmlK/mjIEgKHwgY29sdW1uMSB8IGNvbHVtbjIgfClcbiAgICAgIGlmICh0cmltbWVkTGluZS5pbmNsdWRlcygnfCcpICYmIHRyaW1tZWRMaW5lLnNwbGl0KCd8JykubGVuZ3RoID49IDMpIHtcbiAgICAgICAgLy8g5pS26ZuG6KGo5qC86KGMXG4gICAgICAgIGNvbnN0IHRhYmxlUm93czogc3RyaW5nW10gPSBbdHJpbW1lZExpbmVdXG4gICAgICAgIGxldCBqID0gaSArIDFcblxuICAgICAgICAvLyDmlLbpm4bov57nu63nmoTooajmoLzooYxcbiAgICAgICAgd2hpbGUgKGogPCBsaW5lcy5sZW5ndGgpIHtcbiAgICAgICAgICBjb25zdCBuZXh0TGluZSA9IGxpbmVzW2pdLnRyaW0oKVxuICAgICAgICAgIGlmIChuZXh0TGluZS5pbmNsdWRlcygnfCcpICYmIG5leHRMaW5lLnNwbGl0KCd8JykubGVuZ3RoID49IDMpIHtcbiAgICAgICAgICAgIHRhYmxlUm93cy5wdXNoKG5leHRMaW5lKVxuICAgICAgICAgICAgaisrXG4gICAgICAgICAgfSBlbHNlIGlmIChuZXh0TGluZSA9PT0gJycpIHtcbiAgICAgICAgICAgIGorK1xuICAgICAgICAgICAgYnJlYWtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgYnJlYWtcbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICBpZiAodGFibGVSb3dzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICBjb25zdCBpc0hlYWRlclNlcGFyYXRvciA9IChyb3c6IHN0cmluZykgPT4gL15bXFx8XFxzXFwtOl0rJC8udGVzdChyb3cpXG4gICAgICAgICAgbGV0IGhlYWRlclJvdzogc3RyaW5nIHwgbnVsbCA9IG51bGxcbiAgICAgICAgICBsZXQgc2VwYXJhdG9ySW5kZXggPSAtMVxuICAgICAgICAgIGxldCBkYXRhUm93czogc3RyaW5nW10gPSBbXVxuXG4gICAgICAgICAgLy8g5p+l5om+6KGo5aS05ZKM5YiG6ZqU56ymXG4gICAgICAgICAgZm9yIChsZXQgayA9IDA7IGsgPCB0YWJsZVJvd3MubGVuZ3RoOyBrKyspIHtcbiAgICAgICAgICAgIGlmIChpc0hlYWRlclNlcGFyYXRvcih0YWJsZVJvd3Nba10pKSB7XG4gICAgICAgICAgICAgIGlmIChrID4gMCkge1xuICAgICAgICAgICAgICAgIGhlYWRlclJvdyA9IHRhYmxlUm93c1trIC0gMV1cbiAgICAgICAgICAgICAgICBzZXBhcmF0b3JJbmRleCA9IGtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICBicmVha1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cblxuICAgICAgICAgIC8vIOWmguaenOaJvuWIsOWIhumalOespu+8jOWIhuemu+aVsOaNruihjFxuICAgICAgICAgIGlmIChzZXBhcmF0b3JJbmRleCA+PSAwKSB7XG4gICAgICAgICAgICBkYXRhUm93cyA9IHRhYmxlUm93cy5zbGljZShzZXBhcmF0b3JJbmRleCArIDEpXG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIC8vIOayoeacieWIhumalOespu+8jOesrOS4gOihjOS9nOS4uuihqOWktO+8jOWFtuS9meS9nOS4uuaVsOaNrlxuICAgICAgICAgICAgaGVhZGVyUm93ID0gdGFibGVSb3dzWzBdXG4gICAgICAgICAgICBkYXRhUm93cyA9IHRhYmxlUm93cy5zbGljZSgxKVxuICAgICAgICAgIH1cblxuICAgICAgICAgIGNvbnN0IHBhcnNlVGFibGVSb3cgPSAocm93OiBzdHJpbmcpID0+IHtcbiAgICAgICAgICAgIHJldHVybiByb3cuc3BsaXQoJ3wnKVxuICAgICAgICAgICAgICAubWFwKGNlbGwgPT4gY2VsbC50cmltKCkpXG4gICAgICAgICAgICAgIC5maWx0ZXIoY2VsbCA9PiBjZWxsICE9PSAnJylcbiAgICAgICAgICB9XG5cbiAgICAgICAgICBlbGVtZW50cy5wdXNoKFxuICAgICAgICAgICAgPGRpdiBrZXk9e2tleSsrfSBjbGFzc05hbWU9XCJteS00IG92ZXJmbG93LXgtYXV0b1wiPlxuICAgICAgICAgICAgICA8dGFibGUgY2xhc3NOYW1lPVwibWluLXctZnVsbCBib3JkZXIgYm9yZGVyLXNsYXRlLTIwMCByb3VuZGVkLWxnIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgICAgICAgIHtoZWFkZXJSb3cgJiYgKFxuICAgICAgICAgICAgICAgICAgPHRoZWFkIGNsYXNzTmFtZT1cImJnLXNsYXRlLTUwXCI+XG4gICAgICAgICAgICAgICAgICAgIDx0cj5cbiAgICAgICAgICAgICAgICAgICAgICB7cGFyc2VUYWJsZVJvdyhoZWFkZXJSb3cpLm1hcCgoaGVhZGVyLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPHRoIGtleT17aW5kZXh9IGNsYXNzTmFtZT1cInB4LTQgcHktMiB0ZXh0LWxlZnQgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXNsYXRlLTcwMCBib3JkZXItYiBib3JkZXItc2xhdGUtMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRJbmxpbmVUZXh0KGhlYWRlcil9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICAgICAgPC90aGVhZD5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDx0Ym9keT5cbiAgICAgICAgICAgICAgICAgIHtkYXRhUm93cy5tYXAoKHJvdywgcm93SW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgPHRyIGtleT17cm93SW5kZXh9IGNsYXNzTmFtZT17cm93SW5kZXggJSAyID09PSAwID8gJ2JnLXdoaXRlJyA6ICdiZy1zbGF0ZS0yNSd9PlxuICAgICAgICAgICAgICAgICAgICAgIHtwYXJzZVRhYmxlUm93KHJvdykubWFwKChjZWxsLCBjZWxsSW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBrZXk9e2NlbGxJbmRleH0gY2xhc3NOYW1lPVwicHgtNCBweS0yIHRleHQtc20gdGV4dC1zbGF0ZS03MDAgYm9yZGVyLWIgYm9yZGVyLXNsYXRlLTEwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0SW5saW5lVGV4dChjZWxsKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L3Rib2R5PlxuICAgICAgICAgICAgICA8L3RhYmxlPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKVxuXG4gICAgICAgICAgaSA9IGogLSAxIC8vIOi3s+i/h+W3suWkhOeQhueahOihjFxuICAgICAgICAgIGNvbnRpbnVlXG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8g5byV55So5Z2X5pSv5oyBICg+IHRleHQpXG4gICAgICBpZiAodHJpbW1lZExpbmUuc3RhcnRzV2l0aCgnPiAnKSkge1xuICAgICAgICBjb25zdCBxdW90ZVRleHQgPSB0cmltbWVkTGluZS5zbGljZSgyKVxuICAgICAgICBlbGVtZW50cy5wdXNoKFxuICAgICAgICAgIDxkaXYga2V5PXtrZXkrK30gY2xhc3NOYW1lPVwibXktMyBwbC00IGJvcmRlci1sLTQgYm9yZGVyLWJsdWUtMzAwIGJnLWJsdWUtNTAgcHktMlwiPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbGF0ZS03MDAgaXRhbGljXCI+e2Zvcm1hdElubGluZVRleHQocXVvdGVUZXh0KX08L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIClcbiAgICAgICAgY29udGludWVcbiAgICAgIH1cblxuICAgICAgLy8g6K2m5ZGK5ZKM5o+Q56S65qGG5aKe5by6XG4gICAgICBpZiAodHJpbW1lZExpbmUubWF0Y2goL15b4pqg77iP8J+SoeKEue+4j+KchV1cXHMrKC4rKS8pKSB7XG4gICAgICAgIGNvbnN0IG1hdGNoID0gdHJpbW1lZExpbmUubWF0Y2goL14oW+KaoO+4j/CfkqHihLnvuI/inIVdKVxccysoLispLylcbiAgICAgICAgaWYgKG1hdGNoKSB7XG4gICAgICAgICAgY29uc3QgWywgZW1vamksIHRleHRdID0gbWF0Y2hcbiAgICAgICAgICBsZXQgYmdDb2xvciwgYm9yZGVyQ29sb3IsIHRleHRDb2xvciwgaWNvblxuXG4gICAgICAgICAgc3dpdGNoIChlbW9qaSkge1xuICAgICAgICAgICAgY2FzZSAn4pqg77iPJzpcbiAgICAgICAgICAgICAgYmdDb2xvciA9ICdiZy1hbWJlci01MCdcbiAgICAgICAgICAgICAgYm9yZGVyQ29sb3IgPSAnYm9yZGVyLWFtYmVyLTIwMCdcbiAgICAgICAgICAgICAgdGV4dENvbG9yID0gJ3RleHQtYW1iZXItODAwJ1xuICAgICAgICAgICAgICBpY29uID0gPEFsZXJ0VHJpYW5nbGUgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgIGJyZWFrXG4gICAgICAgICAgICBjYXNlICfwn5KhJzpcbiAgICAgICAgICAgICAgYmdDb2xvciA9ICdiZy1ibHVlLTUwJ1xuICAgICAgICAgICAgICBib3JkZXJDb2xvciA9ICdib3JkZXItYmx1ZS0yMDAnXG4gICAgICAgICAgICAgIHRleHRDb2xvciA9ICd0ZXh0LWJsdWUtODAwJ1xuICAgICAgICAgICAgICBpY29uID0gPExpZ2h0YnVsYiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgYnJlYWtcbiAgICAgICAgICAgIGNhc2UgJ+KEue+4jyc6XG4gICAgICAgICAgICAgIGJnQ29sb3IgPSAnYmctc2xhdGUtNTAnXG4gICAgICAgICAgICAgIGJvcmRlckNvbG9yID0gJ2JvcmRlci1zbGF0ZS0yMDAnXG4gICAgICAgICAgICAgIHRleHRDb2xvciA9ICd0ZXh0LXNsYXRlLTgwMCdcbiAgICAgICAgICAgICAgaWNvbiA9IDxJbmZvIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICBicmVha1xuICAgICAgICAgICAgY2FzZSAn4pyFJzpcbiAgICAgICAgICAgICAgYmdDb2xvciA9ICdiZy1ncmVlbi01MCdcbiAgICAgICAgICAgICAgYm9yZGVyQ29sb3IgPSAnYm9yZGVyLWdyZWVuLTIwMCdcbiAgICAgICAgICAgICAgdGV4dENvbG9yID0gJ3RleHQtZ3JlZW4tODAwJ1xuICAgICAgICAgICAgICBpY29uID0gPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICBicmVha1xuICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgYmdDb2xvciA9ICdiZy1zbGF0ZS01MCdcbiAgICAgICAgICAgICAgYm9yZGVyQ29sb3IgPSAnYm9yZGVyLXNsYXRlLTIwMCdcbiAgICAgICAgICAgICAgdGV4dENvbG9yID0gJ3RleHQtc2xhdGUtODAwJ1xuICAgICAgICAgICAgICBpY29uID0gbnVsbFxuICAgICAgICAgIH1cblxuICAgICAgICAgIGVsZW1lbnRzLnB1c2goXG4gICAgICAgICAgICA8ZGl2IGtleT17a2V5Kyt9IGNsYXNzTmFtZT17YHAtMyByb3VuZGVkLWxnIG1iLTMgJHtiZ0NvbG9yfSAke2JvcmRlckNvbG9yfSBib3JkZXJgfT5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2Bmb250LW1lZGl1bSBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiAke3RleHRDb2xvcn1gfT5cbiAgICAgICAgICAgICAgICB7aWNvbn1cbiAgICAgICAgICAgICAgICA8c3Bhbj57Zm9ybWF0SW5saW5lVGV4dCh0ZXh0KX08L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKVxuICAgICAgICB9XG4gICAgICAgIGNvbnRpbnVlXG4gICAgICB9XG5cbiAgICAgIC8vIFNpZ25hdHVyZSBsaW5lIChDZWxsRm9yZ2UgQUkgLSAuLi4pXG4gICAgICBpZiAodHJpbW1lZExpbmUuaW5jbHVkZXMoJ0NlbGxGb3JnZSBBSScpICYmIHRyaW1tZWRMaW5lLmluY2x1ZGVzKCfwn6esJykpIHtcbiAgICAgICAgZWxlbWVudHMucHVzaChcbiAgICAgICAgICA8ZGl2IGtleT17a2V5Kyt9IGNsYXNzTmFtZT1cIm10LTYgcHQtNCBib3JkZXItdCBib3JkZXItc2xhdGUtMjAwXCI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciB0ZXh0LXNtIHRleHQtc2xhdGUtNTAwIGl0YWxpY1wiPlxuICAgICAgICAgICAgICB7dHJpbW1lZExpbmV9XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIClcbiAgICAgICAgY29udGludWVcbiAgICAgIH1cblxuICAgICAgLy8g5qOA5rWL5paH54yu5byV55So5qC85byPICoqW+aVsOWtl10g5qCH6aKYKipcbiAgICAgIGNvbnN0IGxpdGVyYXR1cmVNYXRjaCA9IHRyaW1tZWRMaW5lLm1hdGNoKC9eXFwqXFwqXFxbKFxcZCspXFxdXFxzKyguKz8pXFwqXFwqJC8pXG4gICAgICBpZiAobGl0ZXJhdHVyZU1hdGNoKSB7XG4gICAgICAgIGNvbnN0IFssIG51bWJlciwgdGl0bGVdID0gbGl0ZXJhdHVyZU1hdGNoXG5cbiAgICAgICAgLy8g5pS26ZuG5ZCO57ut55qE5paH54yu5L+h5oGv6KGMXG4gICAgICAgIGNvbnN0IGxpdGVyYXR1cmVJbmZvOiBzdHJpbmdbXSA9IFt0cmltbWVkTGluZV1cbiAgICAgICAgbGV0IGogPSBpICsgMVxuXG4gICAgICAgIC8vIOaUtumbhuaWh+eMrueahOivpue7huS/oeaBr++8iOS9nOiAheOAgeacn+WIiuOAgURPSeetie+8iVxuICAgICAgICB3aGlsZSAoaiA8IGxpbmVzLmxlbmd0aCkge1xuICAgICAgICAgIGNvbnN0IG5leHRMaW5lID0gbGluZXNbal0udHJpbSgpXG4gICAgICAgICAgaWYgKG5leHRMaW5lID09PSAnJyB8fCBuZXh0TGluZS5zdGFydHNXaXRoKCcqKlsnKSkge1xuICAgICAgICAgICAgYnJlYWtcbiAgICAgICAgICB9XG4gICAgICAgICAgaWYgKG5leHRMaW5lLnN0YXJ0c1dpdGgoJyonKSB8fFxuICAgICAgICAgICAgICBuZXh0TGluZS5zdGFydHNXaXRoKCfwn5OWJykgfHxcbiAgICAgICAgICAgICAgbmV4dExpbmUuc3RhcnRzV2l0aCgn8J+PhicpIHx8XG4gICAgICAgICAgICAgIG5leHRMaW5lLnN0YXJ0c1dpdGgoJ/CfkqEnKSB8fFxuICAgICAgICAgICAgICBuZXh0TGluZS5zdGFydHNXaXRoKCfwn5SRJykgfHxcbiAgICAgICAgICAgICAgbmV4dExpbmUuc3RhcnRzV2l0aCgn8J+UlycpKSB7XG4gICAgICAgICAgICBsaXRlcmF0dXJlSW5mby5wdXNoKG5leHRMaW5lKVxuICAgICAgICAgICAgaisrXG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGJyZWFrXG4gICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgLy8g6Kej5p6Q5paH54yu5L+h5oGvXG4gICAgICAgIGNvbnN0IGxpdGVyYXR1cmUgPSBwYXJzZUxpdGVyYXR1cmVJbmZvKGxpdGVyYXR1cmVJbmZvKVxuICAgICAgICBpZiAobGl0ZXJhdHVyZSkge1xuICAgICAgICAgIGVsZW1lbnRzLnB1c2goXG4gICAgICAgICAgICA8ZGl2IGtleT17a2V5Kyt9IGNsYXNzTmFtZT1cIm15LTRcIj5cbiAgICAgICAgICAgICAgPEludGVyYWN0aXZlTGl0ZXJhdHVyZUNpdGF0aW9uXG4gICAgICAgICAgICAgICAgbGl0ZXJhdHVyZT17bGl0ZXJhdHVyZX1cbiAgICAgICAgICAgICAgICByZWxldmFuY2VFeHBsYW5hdGlvbj17bGl0ZXJhdHVyZS5yZWxldmFuY2VFeHBsYW5hdGlvbn1cbiAgICAgICAgICAgICAgICBzdXBwb3J0UG9pbnRzPXtsaXRlcmF0dXJlLnN1cHBvcnRQb2ludHN9XG4gICAgICAgICAgICAgICAgc2hvd1F1YWxpdHlJbmRpY2F0b3JzPXt0cnVlfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIC8vIOWmguaenOino+aekOWksei0pe+8jOWbnumAgOWIsOaZrumAmuaWh+acrOaYvuekulxuICAgICAgICAgIGVsZW1lbnRzLnB1c2goXG4gICAgICAgICAgICA8cCBrZXk9e2tleSsrfSBjbGFzc05hbWU9XCJtYi0yIHRleHQtc2xhdGUtNzAwIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICB7Zm9ybWF0SW5saW5lVGV4dCh0cmltbWVkTGluZSl9XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgKVxuICAgICAgICB9XG5cbiAgICAgICAgaSA9IGogLSAxIC8vIOi3s+i/h+W3suWkhOeQhueahOihjFxuICAgICAgICBjb250aW51ZVxuICAgICAgfVxuXG4gICAgICAvLyBSZWd1bGFyIHBhcmFncmFwaCB0ZXh0XG4gICAgICBpZiAodHJpbW1lZExpbmUubGVuZ3RoID4gMCkge1xuICAgICAgICAvLyBDaGVjayBpZiBpdCdzIGEgY29udGludWF0aW9uIG9mIGEgbGlzdCBvciBzZWN0aW9uXG4gICAgICAgIGNvbnN0IGlzSW5kZW50ZWQgPSBsaW5lLnN0YXJ0c1dpdGgoJyAgJykgfHwgbGluZS5zdGFydHNXaXRoKCdcXHQnKVxuXG4gICAgICAgIGVsZW1lbnRzLnB1c2goXG4gICAgICAgICAgPHAga2V5PXtrZXkrK30gY2xhc3NOYW1lPXtgbWItMiB0ZXh0LXNsYXRlLTcwMCBsZWFkaW5nLXJlbGF4ZWQgJHtcbiAgICAgICAgICAgIGlzSW5kZW50ZWQgPyAnbWwtNCB0ZXh0LXNsYXRlLTYwMCcgOiAnJ1xuICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgIHtmb3JtYXRJbmxpbmVUZXh0KHRyaW1tZWRMaW5lKX1cbiAgICAgICAgICA8L3A+XG4gICAgICAgIClcbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4gZWxlbWVudHNcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2Bmb3JtYXR0ZWQtbWVzc2FnZSAke2NsYXNzTmFtZX1gfT5cbiAgICAgIHtmb3JtYXRDb250ZW50KGNvbnRlbnQpfVxuICAgIDwvZGl2PlxuICApXG59XG5cbi8vIOaWh+eMruS/oeaBr+ino+aekOWHveaVsFxuZnVuY3Rpb24gcGFyc2VMaXRlcmF0dXJlSW5mbyhsaXRlcmF0dXJlSW5mbzogc3RyaW5nW10pOiBhbnkgfCBudWxsIHtcbiAgdHJ5IHtcbiAgICBjb25zdCB0aXRsZUxpbmUgPSBsaXRlcmF0dXJlSW5mb1swXVxuICAgIGNvbnN0IHRpdGxlTWF0Y2ggPSB0aXRsZUxpbmUubWF0Y2goL15cXCpcXCpcXFsoXFxkKylcXF1cXHMrKC4rPylcXCpcXCokLylcbiAgICBpZiAoIXRpdGxlTWF0Y2gpIHJldHVybiBudWxsXG5cbiAgICBjb25zdCBbLCBudW1iZXIsIHRpdGxlXSA9IHRpdGxlTWF0Y2hcblxuICAgIC8vIOWIneWni+WMluaWh+eMruWvueixoVxuICAgIGNvbnN0IGxpdGVyYXR1cmU6IGFueSA9IHtcbiAgICAgIGlkOiBwYXJzZUludChudW1iZXIpLFxuICAgICAgdGl0bGU6IHRpdGxlLFxuICAgICAgYXV0aG9yczogW10sXG4gICAgICBqb3VybmFsOiAnJyxcbiAgICAgIHB1YmxpY2F0aW9uX3llYXI6IG5ldyBEYXRlKCkuZ2V0RnVsbFllYXIoKSxcbiAgICAgIGNhdGVnb3J5OiAndW5rbm93bicsXG4gICAgICB0ZWNobm9sb2d5X3RhZ3M6IFtdLFxuICAgICAgYXBwbGljYXRpb25fdGFnczogW10sXG4gICAgICBjaXRhdGlvbl9jb3VudDogMCxcbiAgICAgIHJlbGV2YW5jZV9zY29yZTogMCxcbiAgICAgIGtleV9maW5kaW5nczogJycsXG4gICAgICBtZXRob2RvbG9neV9zdW1tYXJ5OiAnJyxcbiAgICAgIGJ1c2luZXNzX3ZhbHVlOiAnJyxcbiAgICAgIGFic3RyYWN0OiAnJyxcbiAgICAgIHJlbGV2YW5jZUV4cGxhbmF0aW9uOiAnJyxcbiAgICAgIHN1cHBvcnRQb2ludHM6IFtdXG4gICAgfVxuXG4gICAgLy8g6Kej5p6Q5YW25LuW5L+h5oGv6KGMXG4gICAgZm9yIChsZXQgaSA9IDE7IGkgPCBsaXRlcmF0dXJlSW5mby5sZW5ndGg7IGkrKykge1xuICAgICAgY29uc3QgbGluZSA9IGxpdGVyYXR1cmVJbmZvW2ldLnRyaW0oKVxuXG4gICAgICAvLyDkvZzogIXlkozmnJ/liIrkv6Hmga8gKuS9nOiAheetiSpcbiAgICAgIGlmIChsaW5lLnN0YXJ0c1dpdGgoJyonKSAmJiBsaW5lLmVuZHNXaXRoKCcqJykgJiYgIWxpbmUuaW5jbHVkZXMoJyoqJykpIHtcbiAgICAgICAgY29uc3QgYXV0aG9ySm91cm5hbE1hdGNoID0gbGluZS5tYXRjaCgvXlxcKiguKz8pXFwqJC8pXG4gICAgICAgIGlmIChhdXRob3JKb3VybmFsTWF0Y2gpIHtcbiAgICAgICAgICBjb25zdCBhdXRob3JKb3VybmFsVGV4dCA9IGF1dGhvckpvdXJuYWxNYXRjaFsxXVxuICAgICAgICAgIGNvbnN0IHBhcnRzID0gYXV0aG9ySm91cm5hbFRleHQuc3BsaXQoJ+etiScpXG4gICAgICAgICAgaWYgKHBhcnRzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgIGxpdGVyYXR1cmUuYXV0aG9ycyA9IHBhcnRzWzBdLnNwbGl0KCcsJykubWFwKChhdXRob3I6IHN0cmluZykgPT4gYXV0aG9yLnRyaW0oKSlcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8g5pyf5YiK5L+h5oGvIPCfk5Yg5pyf5YiK5ZCNICjlubTku70pXG4gICAgICBlbHNlIGlmIChsaW5lLnN0YXJ0c1dpdGgoJ/Cfk5YnKSkge1xuICAgICAgICBjb25zdCBqb3VybmFsTWF0Y2ggPSBsaW5lLm1hdGNoKC/wn5OWXFxzKyguKz8pXFxzK1xcKChcXGR7NH0pXFwpLylcbiAgICAgICAgaWYgKGpvdXJuYWxNYXRjaCkge1xuICAgICAgICAgIGxpdGVyYXR1cmUuam91cm5hbCA9IGpvdXJuYWxNYXRjaFsxXVxuICAgICAgICAgIGxpdGVyYXR1cmUucHVibGljYXRpb25feWVhciA9IHBhcnNlSW50KGpvdXJuYWxNYXRjaFsyXSlcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyDlvbHlk43lm6DlrZAg8J+PhiDlvbHlk43lm6DlrZA6IOaVsOWtl1xuICAgICAgZWxzZSBpZiAobGluZS5zdGFydHNXaXRoKCfwn4+GJykpIHtcbiAgICAgICAgY29uc3QgaWZNYXRjaCA9IGxpbmUubWF0Y2goL/Cfj4ZcXHMr5b2x5ZON5Zug5a2QOlxccyooW1xcZC5dKykvKVxuICAgICAgICBpZiAoaWZNYXRjaCkge1xuICAgICAgICAgIGxpdGVyYXR1cmUuaW1wYWN0X2ZhY3RvciA9IHBhcnNlRmxvYXQoaWZNYXRjaFsxXSlcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyDnm7jlhbPmgKfor7TmmI4g8J+SoSAqKuebuOWFs+aApyoqOiDlhoXlrrlcbiAgICAgIGVsc2UgaWYgKGxpbmUuc3RhcnRzV2l0aCgn8J+SoScpKSB7XG4gICAgICAgIGNvbnN0IHJlbGV2YW5jZU1hdGNoID0gbGluZS5tYXRjaCgv8J+SoVxccytcXCpcXCrnm7jlhbPmgKdcXCpcXCo6XFxzKiguKykvKVxuICAgICAgICBpZiAocmVsZXZhbmNlTWF0Y2gpIHtcbiAgICAgICAgICBsaXRlcmF0dXJlLnJlbGV2YW5jZUV4cGxhbmF0aW9uID0gcmVsZXZhbmNlTWF0Y2hbMV1cbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyDmlK/mjIHopoHngrkg8J+UkSAqKuaUr+aMgeimgeeCuSoqOiDlhoXlrrlcbiAgICAgIGVsc2UgaWYgKGxpbmUuc3RhcnRzV2l0aCgn8J+UkScpKSB7XG4gICAgICAgIGNvbnN0IHN1cHBvcnRNYXRjaCA9IGxpbmUubWF0Y2goL/CflJFcXHMrXFwqXFwq5pSv5oyB6KaB54K5XFwqXFwqOlxccyooLispLylcbiAgICAgICAgaWYgKHN1cHBvcnRNYXRjaCkge1xuICAgICAgICAgIGxpdGVyYXR1cmUuc3VwcG9ydFBvaW50cy5wdXNoKHN1cHBvcnRNYXRjaFsxXSlcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBET0npk77mjqUg8J+UlyBET0k6IOWGheWuuVxuICAgICAgZWxzZSBpZiAobGluZS5zdGFydHNXaXRoKCfwn5SXJykpIHtcbiAgICAgICAgY29uc3QgZG9pTWF0Y2ggPSBsaW5lLm1hdGNoKC/wn5SXXFxzK0RPSTpcXHMqKC4rKS8pXG4gICAgICAgIGlmIChkb2lNYXRjaCkge1xuICAgICAgICAgIGxpdGVyYXR1cmUuZG9pID0gZG9pTWF0Y2hbMV1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIOiuvue9rum7mOiupOWAvFxuICAgIGlmICghbGl0ZXJhdHVyZS5rZXlfZmluZGluZ3MpIHtcbiAgICAgIGxpdGVyYXR1cmUua2V5X2ZpbmRpbmdzID0gYCR7dGl0bGV955qE6YeN6KaB56CU56m25Y+R546wYFxuICAgIH1cblxuICAgIGlmICghbGl0ZXJhdHVyZS5tZXRob2RvbG9neV9zdW1tYXJ5KSB7XG4gICAgICBsaXRlcmF0dXJlLm1ldGhvZG9sb2d5X3N1bW1hcnkgPSAn6K+m57uG55qE5pa55rOV5a2m5L+h5oGv6K+35p+l55yL5Y6f5paHJ1xuICAgIH1cblxuICAgIGlmICghbGl0ZXJhdHVyZS5idXNpbmVzc192YWx1ZSkge1xuICAgICAgbGl0ZXJhdHVyZS5idXNpbmVzc192YWx1ZSA9ICfkuLrljZXnu4bog57mtYvluo/noJTnqbbmj5Dkvpvph43opoHnmoTnkIborrrlkozmioDmnK/mlK/mjIEnXG4gICAgfVxuXG4gICAgLy8g6K6+572u5oqA5pyv5qCH562+77yI5Z+65LqO5qCH6aKY5o6o5pat77yJXG4gICAgY29uc3QgdGl0bGVMb3dlciA9IHRpdGxlLnRvTG93ZXJDYXNlKClcbiAgICBpZiAodGl0bGVMb3dlci5pbmNsdWRlcygnc2luZ2xlLWNlbGwnKSB8fCB0aXRsZUxvd2VyLmluY2x1ZGVzKCdzY3JuYScpKSB7XG4gICAgICBsaXRlcmF0dXJlLnRlY2hub2xvZ3lfdGFncy5wdXNoKCdzY1JOQS1zZXEnKVxuICAgIH1cbiAgICBpZiAodGl0bGVMb3dlci5pbmNsdWRlcygnYXRhYycpKSB7XG4gICAgICBsaXRlcmF0dXJlLnRlY2hub2xvZ3lfdGFncy5wdXNoKCdzY0FUQUMtc2VxJylcbiAgICB9XG4gICAgaWYgKHRpdGxlTG93ZXIuaW5jbHVkZXMoJ3NldXJhdCcpKSB7XG4gICAgICBsaXRlcmF0dXJlLnRlY2hub2xvZ3lfdGFncy5wdXNoKCdzZXVyYXQnKVxuICAgIH1cbiAgICBpZiAodGl0bGVMb3dlci5pbmNsdWRlcygnMTB4JykpIHtcbiAgICAgIGxpdGVyYXR1cmUudGVjaG5vbG9neV90YWdzLnB1c2goJzEweF9nZW5vbWljcycpXG4gICAgfVxuXG4gICAgLy8g6K6+572u5bqU55So5qCH562+XG4gICAgaWYgKHRpdGxlTG93ZXIuaW5jbHVkZXMoJ2ltbXVuZScpIHx8IHRpdGxlTG93ZXIuaW5jbHVkZXMoJ2RlbmRyaXRpYycpKSB7XG4gICAgICBsaXRlcmF0dXJlLmFwcGxpY2F0aW9uX3RhZ3MucHVzaCgnaW1tdW5vbG9neScpXG4gICAgfVxuICAgIGlmICh0aXRsZUxvd2VyLmluY2x1ZGVzKCdkZXZlbG9wbWVudCcpKSB7XG4gICAgICBsaXRlcmF0dXJlLmFwcGxpY2F0aW9uX3RhZ3MucHVzaCgnZGV2ZWxvcG1lbnQnKVxuICAgIH1cblxuICAgIC8vIOiuvue9ruWIhuexu1xuICAgIGlmICh0aXRsZUxvd2VyLmluY2x1ZGVzKCdpbnRlZ3JhdGlvbicpIHx8IHRpdGxlTG93ZXIuaW5jbHVkZXMoJ21ldGhvZCcpKSB7XG4gICAgICBsaXRlcmF0dXJlLmNhdGVnb3J5ID0gJ21ldGhvZG9sb2d5J1xuICAgIH0gZWxzZSBpZiAodGl0bGVMb3dlci5pbmNsdWRlcygncmV2ZWFscycpIHx8IHRpdGxlTG93ZXIuaW5jbHVkZXMoJ2FuYWx5c2lzJykpIHtcbiAgICAgIGxpdGVyYXR1cmUuY2F0ZWdvcnkgPSAnYXBwbGljYXRpb24nXG4gICAgfSBlbHNlIHtcbiAgICAgIGxpdGVyYXR1cmUuY2F0ZWdvcnkgPSAndGVjaG5vbG9neSdcbiAgICB9XG5cbiAgICAvLyDkvLDnrpflvJXnlKjmlbDlkoznm7jlhbPmgKfor4TliIZcbiAgICBpZiAobGl0ZXJhdHVyZS5pbXBhY3RfZmFjdG9yKSB7XG4gICAgICBsaXRlcmF0dXJlLmNpdGF0aW9uX2NvdW50ID0gTWF0aC5yb3VuZChsaXRlcmF0dXJlLmltcGFjdF9mYWN0b3IgKiAxMDAgKyBNYXRoLnJhbmRvbSgpICogMTAwMClcbiAgICAgIGxpdGVyYXR1cmUucmVsZXZhbmNlX3Njb3JlID0gTWF0aC5taW4oMC45NSwgbGl0ZXJhdHVyZS5pbXBhY3RfZmFjdG9yIC8gNTAgKyAwLjUpXG4gICAgfSBlbHNlIHtcbiAgICAgIGxpdGVyYXR1cmUuY2l0YXRpb25fY291bnQgPSBNYXRoLnJvdW5kKE1hdGgucmFuZG9tKCkgKiA1MDAgKyAxMDApXG4gICAgICBsaXRlcmF0dXJlLnJlbGV2YW5jZV9zY29yZSA9IDAuNyArIE1hdGgucmFuZG9tKCkgKiAwLjJcbiAgICB9XG5cbiAgICByZXR1cm4gbGl0ZXJhdHVyZVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ+ino+aekOaWh+eMruS/oeaBr+Wksei0pTonLCBlcnJvcilcbiAgICByZXR1cm4gbnVsbFxuICB9XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VFZmZlY3QiLCJDb3B5IiwiRXh0ZXJuYWxMaW5rIiwiQ2hlY2tDaXJjbGUiLCJBbGVydFRyaWFuZ2xlIiwiSW5mbyIsIkxpZ2h0YnVsYiIsIkJ1dHRvbiIsInRvYXN0IiwiSW50ZXJhY3RpdmVMaXRlcmF0dXJlQ2l0YXRpb24iLCJDb21wcmVoZW5zaXZlU29sdXRpb25EaXNwbGF5IiwiQ29tcHJlaGVuc2l2ZVNvbHV0aW9uRnJhbWV3b3JrIiwiRm9ybWF0dGVkTWVzc2FnZSIsImNvbnRlbnQiLCJjbGFzc05hbWUiLCJyZW5kZXJNZXJtYWlkRGlhZ3JhbXMiLCJkaWFncmFtcyIsImRvY3VtZW50IiwicXVlcnlTZWxlY3RvckFsbCIsImZvckVhY2giLCJkaWFncmFtIiwiZWxlbWVudCIsIm1lcm1haWRDb2RlIiwiZ2V0QXR0cmlidXRlIiwiY2xhc3NMaXN0IiwiYWRkIiwiaW5uZXJIVE1MIiwic3BsaXQiLCJsZW5ndGgiLCJ0aW1lciIsInNldFRpbWVvdXQiLCJjbGVhclRpbWVvdXQiLCJmb3JtYXRJbmxpbmVUZXh0IiwidGV4dCIsImluY2x1ZGVzIiwicGFydHMiLCJrZXkiLCJmb3JtYXRSZWdleCIsImxhc3RJbmRleCIsIm1hdGNoIiwiZXhlYyIsImluZGV4IiwicGxhaW5UZXh0Iiwic2xpY2UiLCJwdXNoIiwibGlua1RleHQiLCJsaW5rVXJsIiwibGlua1RpdGxlIiwiYSIsImhyZWYiLCJ0aXRsZSIsInRhcmdldCIsInJlbCIsInN0cm9uZyIsInNwYW4iLCJlbSIsImNvZGUiLCJyZW1haW5pbmdQbGFpblRleHQiLCJjb3B5VG9DbGlwYm9hcmQiLCJuYXZpZ2F0b3IiLCJjbGlwYm9hcmQiLCJ3cml0ZVRleHQiLCJ0aGVuIiwic3VjY2VzcyIsImNhdGNoIiwiZXJyb3IiLCJmb3JtYXRDb250ZW50IiwibGluZXMiLCJlbGVtZW50cyIsImluQ29kZUJsb2NrIiwiY29kZUJsb2NrQ29udGVudCIsImNvZGVCbG9ja0xhbmd1YWdlIiwiaSIsImxpbmUiLCJ0cmltbWVkTGluZSIsInRyaW0iLCJzdGFydHNXaXRoIiwiY29kZUNvbnRlbnQiLCJqb2luIiwianNvbkRhdGEiLCJKU09OIiwicGFyc2UiLCJ0eXBlIiwiZGF0YSIsImRpdiIsImZyYW1ld29ya0RhdGEiLCJvblNlYXJjaENsaWNrIiwicGxhdGZvcm0iLCJ1cmwiLCJ3aW5kb3ciLCJvcGVuIiwib25PcHRpbWl6ZSIsImZlZWRiYWNrIiwiY29uc29sZSIsImxvZyIsInNvbHV0aW9uRGF0YSIsInZhcmlhbnQiLCJzaXplIiwib25DbGljayIsInByZSIsImpzb25FcnJvciIsInN2ZyIsInZpZXdCb3giLCJmaWxsIiwicGF0aCIsImQiLCJkYXRhLW1lcm1haWQiLCJoYXNoZXMiLCJsZXZlbCIsIkNvbXBvbmVudCIsInJlcGxhY2UiLCJoMSIsImVtb2ppIiwiaXNJbnRlbGxpZ2VudEZlYXR1cmUiLCJoMiIsImgzIiwiaHIiLCJpbmRlbnQiLCJidWxsZXQiLCJpbmRlbnRMZXZlbCIsIk1hdGgiLCJmbG9vciIsIm1hcmdpbkxlZnQiLCJzdHlsZSIsImlzQ2hlY2ttYXJrIiwibnVtYmVyIiwia2V5VGV4dCIsInZhbHVlIiwiaXNJbXBvcnRhbnQiLCJpc1RpbWUiLCJjb3N0UGF0dGVybiIsImZvcm1hdHRlZFBhcnRzIiwibWFwIiwicGFydCIsInRhYmxlUm93cyIsImoiLCJuZXh0TGluZSIsImlzSGVhZGVyU2VwYXJhdG9yIiwicm93IiwidGVzdCIsImhlYWRlclJvdyIsInNlcGFyYXRvckluZGV4IiwiZGF0YVJvd3MiLCJrIiwicGFyc2VUYWJsZVJvdyIsImNlbGwiLCJmaWx0ZXIiLCJ0YWJsZSIsInRoZWFkIiwidHIiLCJoZWFkZXIiLCJ0aCIsInRib2R5Iiwicm93SW5kZXgiLCJjZWxsSW5kZXgiLCJ0ZCIsInF1b3RlVGV4dCIsInAiLCJiZ0NvbG9yIiwiYm9yZGVyQ29sb3IiLCJ0ZXh0Q29sb3IiLCJpY29uIiwibGl0ZXJhdHVyZU1hdGNoIiwibGl0ZXJhdHVyZUluZm8iLCJsaXRlcmF0dXJlIiwicGFyc2VMaXRlcmF0dXJlSW5mbyIsInJlbGV2YW5jZUV4cGxhbmF0aW9uIiwic3VwcG9ydFBvaW50cyIsInNob3dRdWFsaXR5SW5kaWNhdG9ycyIsImlzSW5kZW50ZWQiLCJ0aXRsZUxpbmUiLCJ0aXRsZU1hdGNoIiwiaWQiLCJwYXJzZUludCIsImF1dGhvcnMiLCJqb3VybmFsIiwicHVibGljYXRpb25feWVhciIsIkRhdGUiLCJnZXRGdWxsWWVhciIsImNhdGVnb3J5IiwidGVjaG5vbG9neV90YWdzIiwiYXBwbGljYXRpb25fdGFncyIsImNpdGF0aW9uX2NvdW50IiwicmVsZXZhbmNlX3Njb3JlIiwia2V5X2ZpbmRpbmdzIiwibWV0aG9kb2xvZ3lfc3VtbWFyeSIsImJ1c2luZXNzX3ZhbHVlIiwiYWJzdHJhY3QiLCJlbmRzV2l0aCIsImF1dGhvckpvdXJuYWxNYXRjaCIsImF1dGhvckpvdXJuYWxUZXh0IiwiYXV0aG9yIiwiam91cm5hbE1hdGNoIiwiaWZNYXRjaCIsImltcGFjdF9mYWN0b3IiLCJwYXJzZUZsb2F0IiwicmVsZXZhbmNlTWF0Y2giLCJzdXBwb3J0TWF0Y2giLCJkb2lNYXRjaCIsImRvaSIsInRpdGxlTG93ZXIiLCJ0b0xvd2VyQ2FzZSIsInJvdW5kIiwicmFuZG9tIiwibWluIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/formatted-message.tsx\n"));

/***/ })

});