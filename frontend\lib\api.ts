/**
 * API调用工具函数
 */

// 动态获取API基础URL
const getApiBaseUrl = (): string => {
  // 优先使用环境变量
  if (process.env.NEXT_PUBLIC_API_URL) {
    return process.env.NEXT_PUBLIC_API_URL
  }

  // 在浏览器环境中，使用当前主机的IP和端口8000
  if (typeof window !== 'undefined') {
    const protocol = window.location.protocol
    const hostname = window.location.hostname
    return `${protocol}//${hostname}:8000/api/v1`
  }

  // 服务端渲染时的默认值
  return 'http://localhost:8000/api/v1'
}

const API_BASE_URL = getApiBaseUrl()

// API响应类型
export interface ApiResponse<T = any> {
  status: 'success' | 'error' | 'warning'
  message: string
  data?: T
  timestamp: string
}

export interface ApiError {
  status: 'error'
  message: string
  error_code?: string
  details?: Record<string, any>
  timestamp: string
}

// 用户相关类型
export interface User {
  id: number
  email: string
  username: string
  first_name?: string
  last_name?: string
  phone?: string
  organization?: string
  department?: string
  title?: string
  role: 'super_admin' | 'sales' | 'customer' | 'operations' | 'guest'
  status: 'active' | 'pending' | 'suspended' | 'deleted'
  is_verified: boolean
  created_at: string
  updated_at?: string
  last_login?: string
  preferred_language: string
  email_notifications: boolean
  research_interests?: string
  expertise_areas?: string
}

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  email: string
  username: string
  password: string
  confirm_password: string
  first_name?: string
  last_name?: string
  phone?: string
  organization?: string
  department?: string
  title?: string
  role?: string
  preferred_language?: string
  email_notifications?: boolean
  research_interests?: string
  expertise_areas?: string
}

export interface TokenResponse {
  access_token: string
  token_type: string
  expires_in: number
}

// Token管理
class TokenManager {
  private static readonly TOKEN_KEY = 'cellforge_access_token'
  private static readonly REFRESH_KEY = 'cellforge_refresh_token'

  static getToken(): string | null {
    if (typeof window === 'undefined') return null
    return localStorage.getItem(this.TOKEN_KEY)
  }

  static setToken(token: string): void {
    if (typeof window === 'undefined') return
    localStorage.setItem(this.TOKEN_KEY, token)
  }

  static removeToken(): void {
    if (typeof window === 'undefined') return
    localStorage.removeItem(this.TOKEN_KEY)
    localStorage.removeItem(this.REFRESH_KEY)
  }

  static isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      return payload.exp * 1000 < Date.now()
    } catch {
      return true
    }
  }
}

// HTTP客户端类
class ApiClient {
  private baseURL: string

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    const token = TokenManager.getToken()

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    }

    // 添加认证头
    if (token && !TokenManager.isTokenExpired(token)) {
      headers.Authorization = `Bearer ${token}`
    }

    const config: RequestInit = {
      ...options,
      headers,
    }

    try {
      const response = await fetch(url, config)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))

        // 根据HTTP状态码提供更详细的错误信息
        let errorMessage = errorData.detail || errorData.message || `HTTP ${response.status}: ${response.statusText}`

        if (response.status === 401) {
          errorMessage = errorData.detail || "邮箱或密码错误"
        } else if (response.status === 403) {
          errorMessage = errorData.detail || "账户已被暂停或无权限访问"
        } else if (response.status === 404) {
          errorMessage = "请求的资源不存在"
        } else if (response.status === 422) {
          errorMessage = "请求数据格式错误"
        } else if (response.status >= 500) {
          errorMessage = "服务器内部错误，请稍后重试"
        }

        throw new Error(errorMessage)
      }

      return await response.json()
    } catch (error) {
      // 网络错误处理
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new Error('网络连接失败，请检查网络连接')
      }

      console.error('API请求失败:', error)
      throw error
    }
  }

  // GET请求
  async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' })
  }

  // POST请求
  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  // PUT请求
  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  // DELETE请求
  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' })
  }
}

// 创建API客户端实例
const apiClient = new ApiClient()

// 认证API
export const authApi = {
  // 用户登录
  async login(credentials: LoginRequest): Promise<TokenResponse> {
    const response = await apiClient.post<TokenResponse>('/auth/login', credentials)
    TokenManager.setToken(response.access_token)
    return response
  },

  // 用户注册
  async register(userData: RegisterRequest): Promise<ApiResponse> {
    return apiClient.post<ApiResponse>('/auth/register', userData)
  },

  // 获取当前用户信息
  async getCurrentUser(): Promise<User> {
    return apiClient.get<User>('/auth/me')
  },

  // 刷新令牌
  async refreshToken(): Promise<TokenResponse> {
    const response = await apiClient.post<TokenResponse>('/auth/refresh')
    TokenManager.setToken(response.access_token)
    return response
  },

  // 修改密码
  async changePassword(data: {
    current_password: string
    new_password: string
    confirm_password: string
  }): Promise<ApiResponse> {
    return apiClient.post<ApiResponse>('/auth/change-password', data)
  },

  // 登出
  logout(): void {
    TokenManager.removeToken()
  },
}

// 对话API
export const conversationApi = {
  // 发送消息
  async sendMessage(data: {
    message: string
    conversation_id?: number
    conversation_type?: string
    history?: Array<{ role: string; content: string }>
    context?: Record<string, any>
  }): Promise<{
    message: string
    confidence: number
    sources?: string[]
    suggestions?: string[]
    timestamp: string
  }> {
    return apiClient.post('/conversation/chat', data)
  },

  // 获取对话历史
  async getHistory(page: number = 1, size: number = 20): Promise<{
    conversations: any[]
    total: number
    page: number
    size: number
  }> {
    return apiClient.get(`/conversation/history?page=${page}&size=${size}`)
  },
}

// 文献API
export const literatureApi = {
  // 搜索文献
  async searchLiterature(data: {
    query: string
    category?: string
    technology_tags?: string[]
    top_k?: number
  }): Promise<{
    id?: number
    title: string
    authors: string[]
    journal: string
    publication_year: number
    doi?: string
    pubmed_id?: string
    abstract: string
    category: string
    technology_tags: string[]
    application_tags: string[]
    impact_factor?: number
    citation_count: number
    relevance_score: number
    key_findings: string
    methodology_summary: string
    business_value: string
  }[]> {
    return apiClient.post('/literature/search', data)
  },

  // 获取文献推荐
  async getLiteratureRecommendations(data: {
    context: Record<string, any>
    recommendation_type?: string
    top_k?: number
  }): Promise<{
    literature: any
    recommendation_type: string
    relevance_explanation: string
    business_value: string
    key_support_points: string[]
  }[]> {
    return apiClient.post('/literature/recommendations', data)
  },

  // 获取文献分类
  async getCategories(): Promise<{
    categories: Array<{ value: string; label: string }>
    technology_tags: Array<{ value: string; label: string }>
    application_tags: Array<{ value: string; label: string }>
  }> {
    return apiClient.get('/literature/categories')
  },

  // 获取热门文献
  async getTrendingLiterature(limit: number = 10): Promise<{
    trending_literature: any[]
    total_count: number
  }> {
    return apiClient.get(`/literature/trending?limit=${limit}`)
  },

  // 获取文献统计
  async getStats(): Promise<{
    total_literature: number
    categories: Record<string, number>
    technology_coverage: Record<string, number>
    average_impact_factor: number
    total_citations: number
    last_updated: string
  }> {
    return apiClient.get('/literature/stats')
  },

  // 获取文献搜索状态
  async getSearchStatus(): Promise<{
    literature_search_enabled: boolean
    is_ready: boolean
    available_apis: string[]
    available_count: number
    total_apis: number
    recommendations: string[]
    service_status: {
      optional_service_available: boolean
      service_status: any
    }
  }> {
    return apiClient.get('/literature/search-status')
  },

  // 基于需求搜集文献
  async collectForRequirements(data: {
    requirements: Record<string, any>
    user_preferences?: Record<string, any>
    enable_literature_search: boolean
  }): Promise<{
    status: string
    enabled: boolean
    collection_timestamp?: string
    search_strategy?: any
    total_found?: number
    evaluated_count?: number
    supporting_papers?: any[]
    potential_papers?: any[]
    search_summary?: any
    message?: string
    recommendations?: string[]
  }> {
    return apiClient.post('/literature/collect-for-requirements', data)
  }
}

// 客户画像相关类型
export interface CustomerProfile {
  id: number
  user_id: number
  profile_version: string
  last_updated: string
  confidence_score: number
  completeness_score: number

  // 研究画像
  research_maturity?: string
  research_focus?: Record<string, any>
  publication_level?: string
  collaboration_preference?: string

  // 技术画像
  technical_expertise?: Record<string, any>
  platform_preference?: Record<string, any>
  analysis_complexity?: string
  data_handling_capability?: string

  // 商业画像
  budget_range?: string
  decision_authority?: string
  cost_sensitivity?: string
  project_scale?: string

  // 行为画像
  engagement_level?: string
  learning_style?: string
  communication_preference?: string
  response_speed?: string

  // 偏好画像
  content_preference?: Record<string, any>
  interaction_style?: string
  information_depth?: string

  // 预测性指标
  conversion_probability: number
  lifetime_value_prediction: number
  churn_risk_score: number
  next_best_action?: Record<string, any>
}

export interface ProfileInsight {
  id: number
  analysis_id: number
  insight_type: string
  title: string
  description: string
  confidence: number
  actionable_recommendations: string[]
  business_impact: string
  priority: string
  created_at: string
}

export interface PersonalizedRecommendations {
  user_id: number
  recommendations: Array<{
    type: string
    title: string
    description: string
    priority: string
    estimated_value: number
    implementation_effort: string
    success_probability: number
  }>
  generated_at: string
}

export interface WebProfileAnalysisRequest {
  url: string
  analysis_type?: string
  focus_areas?: string[]
}

export interface TextProfileAnalysisRequest {
  text_content: string
  content_type?: string
  analysis_depth?: string
}

// 客户画像API
export const customerProfileApi = {
  // 获取客户画像
  async getProfile(userId: number): Promise<CustomerProfile> {
    return apiClient.get<CustomerProfile>(`/customer/profile/${userId}`)
  },

  // 分析客户画像
  async analyzeProfile(data: {
    user_id: number
    analysis_type?: string
    include_predictions?: boolean
  }): Promise<{
    profile: CustomerProfile
    insights: ProfileInsight[]
    recommendations: PersonalizedRecommendations
  }> {
    return apiClient.post('/customer/analyze', data)
  },

  // 获取客户洞察
  async getInsights(userId: number): Promise<ProfileInsight[]> {
    return apiClient.get<ProfileInsight[]>(`/customer/insights/${userId}`)
  },

  // 获取个性化推荐
  async getRecommendations(userId: number): Promise<PersonalizedRecommendations> {
    return apiClient.get<PersonalizedRecommendations>(`/customer/recommendations/${userId}`)
  },

  // 更新客户画像
  async updateProfile(userId: number, profileData: Partial<CustomerProfile>): Promise<CustomerProfile> {
    return apiClient.put<CustomerProfile>(`/customer/profile/${userId}`, profileData)
  },

  // 基于需求更新画像
  async updateFromRequirements(userId: number, requirementData: Record<string, any>): Promise<{ message: string }> {
    return apiClient.post<{ message: string }>(`/customer/requirements/${userId}`, requirementData)
  },

  // 网页分析
  async analyzeWebProfile(data: WebProfileAnalysisRequest): Promise<{
    analysis_id: string
    profile_data: Record<string, any>
    confidence_score: number
    key_insights: string[]
    recommendations: string[]
  }> {
    return apiClient.post('/profile-analysis/analyze-url', data)
  },

  // 文本分析
  async analyzeTextProfile(data: TextProfileAnalysisRequest): Promise<{
    analysis_id: string
    profile_data: Record<string, any>
    confidence_score: number
    key_insights: string[]
    recommendations: string[]
  }> {
    return apiClient.post('/profile-analysis/analyze-text', data)
  },

  // 获取分析历史
  async getAnalysisHistory(page: number = 1, size: number = 20): Promise<{
    analyses: Array<{
      id: string
      source_type: string
      source_content: string
      analysis_result: Record<string, any>
      confidence_score: number
      created_at: string
    }>
    total: number
    page: number
    size: number
  }> {
    return apiClient.get(`/profile-analysis/analysis-history?page=${page}&size=${size}`)
  }
}

// 智能文献搜索API
export const smartLiteratureApi = {
  // 生成智能搜索查询
  async generateSmartQueries(data: {
    requirements: Record<string, any>
    user_query?: string
    max_queries?: number
  }): Promise<{
    primary_query: string
    suggested_queries: string[]
    related_topics: string[]
    search_strategy: Record<string, any>
    optimization_notes: string[]
    generated_at: string
  }> {
    return apiClient.post('/smart-literature/generate-smart-queries', data)
  },

  // Perplexity风格搜索
  async perplexitySearch(data: {
    query: string
    requirements?: Record<string, any>
    include_analysis?: boolean
    max_papers?: number
  }): Promise<{
    query: string
    optimized_query: string
    papers: any[]
    ai_summary: string
    related_topics: string[]
    sources: string[]
    search_time_ms: number
  }> {
    return apiClient.post('/smart-literature/perplexity-search', data)
  },

  // 增强文献搜索
  async enhancedLiteratureSearch(data: {
    query: string
    requirements: Record<string, any>
    user_preferences?: Record<string, any>
    enable_literature_search?: boolean
  }): Promise<{
    smart_queries: any
    literature_results: any[]
    optional_literature: any
    total_papers: number
    sources: string[]
    recommendations: string[]
  }> {
    return apiClient.post('/smart-literature/enhanced-literature-search', data)
  }
}

// 方案生成API
export const solutionApi = {
  // 生成完整方案
  async generateSolution(data: {
    requirements: Record<string, any>
    user_message?: string
    include_cost_analysis?: boolean
    include_risk_assessment?: boolean
  }): Promise<{
    solution_id: string
    generated_at: string
    client_requirements: Record<string, any>
    recommended_solution: Record<string, any>
    cost_analysis: Record<string, any>
    risk_assessment: Record<string, any>
    timeline: Record<string, any>
    deliverables: string[]
    next_steps: string[]
    contact_info: Record<string, any>
  }> {
    return apiClient.post('/solution/generate-solution', data)
  },

  // 快速成本估算
  async quickEstimate(requirements: Record<string, any>): Promise<{
    quick_estimate: {
      recommended_platform: string
      estimated_cost: string
      estimated_timeline: string
      sample_count: number
      cost_per_sample: string
    }
    next_steps: string[]
    disclaimer: string
  }> {
    return apiClient.post('/solution/quick-estimate', requirements)
  },

  // 获取方案模板
  async getSolutionTemplates(): Promise<{
    available_templates: Record<string, any>
    customization_note: string
    consultation_available: string
  }> {
    return apiClient.get('/solution/solution-templates')
  },

  // 验证需求完整性
  async validateRequirements(requirements: Record<string, any>): Promise<{
    validation_result: {
      is_valid: boolean
      completeness_percentage: number
      missing_required_fields: string[]
      completed_fields: string[]
      recommendations: string[]
    }
    next_action: string
  }> {
    return apiClient.post('/solution/validate-requirements', requirements)
  },

  // 获取平台对比
  async getPlatformComparison(): Promise<{
    platform_comparison: Record<string, any>
    selection_guide: Record<string, any>
    consultation_note: string
  }> {
    return apiClient.get('/solution/platform-comparison')
  },

  // 生成综合方案框架（按设计文档要求）
  async generateComprehensiveFramework(data: {
    requirements: Record<string, any>
    user_message?: string
    framework_template?: 'standard' | 'detailed' | 'simplified'
    enable_literature_search?: boolean
  }): Promise<{
    success: boolean
    framework_id: string
    data: {
      solution_overview: Record<string, any>
      research_intent_analysis: Record<string, any>
      key_factors: Record<string, any>
      literature_recommendations: Record<string, any>
      platform_comparison: Record<string, any>
      implementation_plan: Record<string, any>
      risk_assessment: Record<string, any>
      generated_at: string
      template_used: string
    }
    generation_time: string
    template_used: string
  }> {
    return apiClient.post('/comprehensive-solution/comprehensive-framework', {
      requirements: data.requirements,
      user_message: data.user_message || '',
      framework_template: data.framework_template || 'standard',
      enable_literature_search: data.enable_literature_search !== false
    })
  }
}

// 智能推荐API
export const intelligentRecommendationApi = {
  // 获取综合智能推荐
  async getComprehensiveRecommendation(data: {
    speciesType: string
    experimentType: string
    researchGoal: string
    sampleType: string
    sampleCount: string
    sampleStatus: string
    processingMethod: string
    cellCount: string
    cellViability: string
    budgetRange: string
    projectDuration: string
    urgency: string
    sequencingDepth: string
    analysisType: string
    dataAnalysisNeeds: string
    cellSorting: string
    additionalRequirements?: string
  }): Promise<{
    success: boolean
    data: {
      search_queries: {
        primary_query: string
        suggested_queries: string[]
        related_topics: string[]
        search_strategy: Record<string, any>
        optimization_notes: string[]
      }
      literature_results: {
        local_results: any[]
        external_results: any[]
        combined_results: any[]
      }
      expanded_keywords: {
        semantic_expansion: string[]
        cross_disciplinary: string[]
        trending_terms: string[]
        molecular_targets: string[]
        clinical_terms: string[]
      }
      hot_papers: Array<{
        title: string
        authors: string
        journal: string
        impact_factor: number
        publication_date: string
        citation_count: number
        trend_score: number
        reason: string
        doi: string
        relevance: number
      }>
      tech_recommendations: Array<{
        platform: string
        platform_id: string
        specifications: Record<string, any>
        cost_breakdown: Record<string, any>
        total_estimated_cost: number
        budget_match_score: number
        timeline_estimate: Record<string, string>
        advantages: string[]
        considerations: string[]
        suitability_score: number
      }>
      project_solution: {
        overview: string
        experimental_design: Record<string, any>
        data_analysis_plan: Record<string, any>
        expected_outcomes: Record<string, any>
        publication_strategy: Record<string, any>
        collaboration_opportunities: Array<{
          institution: string
          contact: string
          expertise: string
          collaboration_type: string
        }>
        upgrade_pathways: Record<string, any>
      }
      risk_assessment: {
        technical_risks: Array<{
          risk: string
          probability: string
          impact: string
          mitigation: string
        }>
        timeline_risks: any[]
        budget_risks: any[]
        data_risks: any[]
        overall_risk_level: string
        success_probability: string
      }
      generated_at: string
    }
    processing_time?: number
  }> {
    return apiClient.post('/intelligent-recommendation/comprehensive-recommendation', data)
  },

  // 获取智能关键词扩展
  async getKeywordExpansion(data: any): Promise<{
    success: boolean
    data: {
      expanded_keywords: {
        semantic_expansion: string[]
        cross_disciplinary: string[]
        trending_terms: string[]
        molecular_targets: string[]
        clinical_terms: string[]
      }
      generated_at: string
    }
  }> {
    return apiClient.post('/intelligent-recommendation/keyword-expansion', data)
  },

  // 获取热点文献推荐
  async getHotPapers(data: any): Promise<{
    success: boolean
    data: {
      hot_papers: Array<{
        title: string
        authors: string
        journal: string
        impact_factor: number
        publication_date: string
        citation_count: number
        trend_score: number
        reason: string
        doi: string
        relevance: number
      }>
      total_count: number
    }
  }> {
    return apiClient.post('/intelligent-recommendation/hot-papers', data)
  },

  // 获取技术平台推荐
  async getTechRecommendations(data: any): Promise<{
    success: boolean
    data: {
      tech_recommendations: Array<{
        platform: string
        platform_id: string
        specifications: Record<string, any>
        cost_breakdown: Record<string, any>
        total_estimated_cost: number
        budget_match_score: number
        timeline_estimate: Record<string, string>
        advantages: string[]
        considerations: string[]
        suitability_score: number
      }>
      total_count: number
    }
  }> {
    return apiClient.post('/intelligent-recommendation/tech-recommendations', data)
  },

  // 获取项目整体解决方案
  async getProjectSolution(data: any): Promise<{
    success: boolean
    data: {
      project_solution: {
        overview: string
        experimental_design: Record<string, any>
        data_analysis_plan: Record<string, any>
        expected_outcomes: Record<string, any>
        publication_strategy: Record<string, any>
        collaboration_opportunities: Array<{
          institution: string
          contact: string
          expertise: string
          collaboration_type: string
        }>
        upgrade_pathways: Record<string, any>
      }
    }
  }> {
    return apiClient.post('/intelligent-recommendation/project-solution', data)
  },

  // 健康检查
  async healthCheck(): Promise<{
    status: string
    service: string
    timestamp: string
  }> {
    return apiClient.get('/intelligent-recommendation/health')
  }
}

// 导出Token管理器和API客户端
export { TokenManager, apiClient }
