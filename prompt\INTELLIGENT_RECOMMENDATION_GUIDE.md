# CellForge AI 智能推荐系统使用指南

## 🌟 系统概览

CellForge AI 智能推荐系统是一个集成了文献搜索、技术方案推荐、项目规划等多功能的智能化平台。基于用户的项目需求，系统能够自动生成个性化的综合解决方案。

## 🚀 核心功能

### 1. 智能文献推荐
- **语义关键词扩展**: 自动发现相关概念和同义词
- **热点文献追踪**: 实时推荐高引用、高关注度的最新文献  
- **跨学科关键词推荐**: 从相关领域发现研究机会
- **权威文献筛选**: 基于影响因子和相关性的智能排序

### 2. 技术方案推荐
- **平台对比分析**: 详细的技术平台规格和成本对比
- **预算匹配评估**: 基于用户预算的最优方案推荐
- **时间线规划**: 精确的项目时间估算和里程碑设置
- **风险评估**: 全面的技术和商业风险分析

### 3. 项目整体解决方案
- **实验设计建议**: 基于最佳实践的实验方案设计
- **数据分析计划**: 完整的生信分析流程规划
- **发表策略**: 期刊选择和发表时间线建议
- **合作机会推荐**: 相关领域专家和机构推荐

## 🎯 Demo 场景演示

### 项目需求示例
```
🧬 物种类型: 大鼠 (Rattus norvegicus)
🔬 实验类型: 单细胞RNA测序 (scRNA-seq)  
🎯 研究目标: 发育轨迹分析
🧪 样本信息: 心脏组织, 4-5个样本, 5,000-10,000细胞
💰 预算范围: 10-20万
📅 项目周期: 3-6个月
⚡ 紧急程度: 非常紧急（加急处理）
```

### 系统推荐结果

#### 📚 智能文献推荐
- **主要查询**: "rat scRNA-seq heart developmental trajectory analysis"
- **扩展查询**: 6个优化的学术搜索词
- **热点文献**: 4篇高影响因子的最新相关论文
- **关键词扩展**: 语义、跨学科、热点趋势词汇

#### 🏆 技术平台推荐
1. **10x Genomics Chromium** (推荐度: 92%)
   - 预估成本: 2.5万元
   - 项目周期: 30-43天  
   - 主要优势: 标准化流程、高通量处理

2. **BD Rhapsody** (推荐度: 78%)
   - 预估成本: 2.0万元
   - 项目周期: 21-32天
   - 主要优势: 成本较低、操作简便

#### 🎯 预期研究成果
- **科学发现**: 构建大鼠心脏发育单细胞图谱
- **发表潜力**: 预计IF 8-15期刊，2-3篇后续研究
- **转化价值**: 先天性心脏病研究模型

#### ⚠️ 风险评估
- **整体风险**: 低-中等
- **成功概率**: 85-90%
- **主要风险**: 组织解离难度、批次效应
- **缓解策略**: 预实验优化、标准化流程

## 🛠 技术架构

### 后端服务
```
backend/app/services/intelligent_recommendation_service.py
- 智能推荐核心逻辑
- 知识图谱管理  
- 技术平台数据库
- 风险评估算法

backend/app/api/endpoints/intelligent_recommendation.py  
- RESTful API端点
- 请求参数验证
- 响应数据格式化
```

### 前端界面
```
frontend/app/intelligent-recommendation/page.tsx
- 处理流程可视化
- 进度条和状态管理

frontend/components/comprehensive-recommendation-platform.tsx
- 多标签页布局设计
- 交互式数据展示
- 响应式界面适配
```

### API集成
```
frontend/lib/api.ts
- intelligentRecommendationApi
- 统一的错误处理  
- Token认证管理
```

## 📋 使用步骤

### 1. 访问智能推荐页面
```
URL: /intelligent-recommendation
```

### 2. 查看需求概览
系统会显示基于问卷收集的项目需求摘要

### 3. 启动智能分析
点击"开始智能分析"按钮，系统将执行以下步骤：
- 需求分析
- 智能查询生成
- 文献搜索  
- 关键词扩展
- 技术方案推荐
- 项目规划
- 报告生成

### 4. 浏览推荐结果
通过四个主要标签页查看结果：
- **项目概览**: 整体方案和预期成果
- **文献推荐**: 智能搜索和热点文献
- **技术方案**: 平台对比和成本分析  
- **风险评估**: 风险分析和合作建议

## 🔧 API使用示例

### 获取综合推荐
```javascript
import { intelligentRecommendationApi } from '@/lib/api'

const requirementData = {
  speciesType: "大鼠 (Rattus norvegicus)",
  experimentType: "单细胞RNA测序 (scRNA-seq)",
  researchGoal: "发育轨迹分析",
  // ... 其他参数
}

const response = await intelligentRecommendationApi
  .getComprehensiveRecommendation(requirementData)

if (response.success) {
  console.log('推荐结果:', response.data)
}
```

### 单独获取热点文献
```javascript  
const hotPapers = await intelligentRecommendationApi
  .getHotPapers(requirementData)

console.log('热点文献:', hotPapers.data.hot_papers)
```

## 🎨 界面特性

### 响应式设计
- 桌面端: 多列网格布局
- 移动端: 单列堆叠布局
- 自适应卡片尺寸

### 交互体验
- 渐进式内容加载
- 可折叠详情区域
- 实时搜索和过滤
- 一键导出功能

### 视觉设计
- 清晰的信息层级
- 直观的图标系统
- 一致的颜色语言
- 优雅的加载动画

## 🔄 数据流转

```
用户需求 → 智能分析引擎 → 知识图谱查询 → 文献数据库检索 
    ↓
技术规格匹配 → 成本效益分析 → 风险评估计算 → 综合推荐生成
    ↓  
前端界面渲染 → 用户交互反馈 → 方案优化迭代
```

## 🚀 扩展功能

### 即将推出
- **AI对话助手**: 基于推荐结果的智能问答
- **实时协作**: 团队成员共享和讨论方案
- **版本管理**: 方案历史记录和比较功能
- **自动化报告**: 生成专业的PDF研究方案

### 长期规划
- **机器学习优化**: 基于用户反馈的推荐算法改进
- **行业扩展**: 支持更多生命科学研究领域
- **国际化**: 多语言支持和本地化适配

## 📞 技术支持

如有任何技术问题或改进建议，请联系：
- **技术团队**: <EMAIL>
- **产品反馈**: <EMAIL>
- **商务合作**: <EMAIL>

---

## 🎉 结语

CellForge AI 智能推荐系统代表了科研服务智能化的新里程碑。通过将AI技术与专业知识深度融合，我们为研究者提供了更精准、更全面、更个性化的科研解决方案。

**让AI为您的科研之路点亮明灯！** ✨