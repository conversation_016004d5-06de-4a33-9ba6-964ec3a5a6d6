# 智能文献搜索功能使用指南

## 🎯 功能概述

本功能实现了类似Perplexity的智能文献搜索体验，通过结合问卷调研的背景信息和AI技术，为单细胞测序研究提供精准的文献发现和分析服务。

## 🏗️ 架构设计

### 前端组件
- **SmartLiteratureResearchPlatform**: 主平台组件，整合需求收集和文献搜索
- **ProgressiveRequirementCollector**: 渐进式问卷调研组件
- **PerplexityLiteratureSearch**: Perplexity风格的搜索界面
- **LiteratureSearchToggle**: 文献搜索功能开关

### 后端服务
- **SmartPromptGenerator**: 智能提示词生成服务
- **SmartLiteratureAPI**: 智能文献搜索API端点
- **OptionalLiteratureService**: 可选的外部文献源集成

## 🚀 使用流程

### 第一步：需求收集
1. 访问 `/smart-literature-research` 页面
2. 填写问卷调研信息：
   - 物种类型（人类、小鼠、大鼠等）
   - 实验类型（scRNA-seq、scATAC-seq等）
   - 研究目标（细胞分型、轨迹分析等）
   - 样本信息、预算、时间等
3. 系统自动计算完成度，达到80%可进入下一步

### 第二步：智能搜索
1. 切换到"文献搜索"标签页
2. AI基于问卷信息生成智能搜索建议
3. 用户可以：
   - 直接使用推荐的搜索查询
   - 输入自定义搜索内容
   - 点击相关主题深入探索

### 第三步：结果分析
1. 系统展示搜索进度和实时状态
2. AI生成智能摘要和关键洞察
3. 展示相关文献列表，包含：
   - 文献基本信息（标题、作者、期刊等）
   - AI相关性分析
   - 影响因子和引用数据
   - 直接链接到原文

## 💡 核心特性

### 1. 智能提示词生成
- 基于问卷信息自动生成优化的搜索查询
- 使用专业术语和学术规范
- 支持多语言和跨领域关键词映射

### 2. Perplexity风格体验
- 流式搜索进度展示
- 实时AI分析和摘要
- 交互式结果探索
- 相关主题推荐

### 3. 多源文献集成
- 内置文献库（示例数据）
- 外部API集成支持（PubMed、Semantic Scholar、bioRxiv）
- 智能降级机制

### 4. AI驱动分析
- 基于需求的相关性评估
- 智能摘要生成
- 个性化推荐算法

## 🔧 技术实现

### API端点
- `POST /api/v1/smart-literature/generate-smart-queries` - 生成智能查询
- `POST /api/v1/smart-literature/perplexity-search` - Perplexity风格搜索
- `POST /api/v1/smart-literature/enhanced-literature-search` - 增强搜索

### 数据流
1. 问卷数据 → 智能提示词生成器 → 优化的搜索查询
2. 搜索查询 → 多源文献检索 → 原始文献结果
3. 文献结果 → AI分析服务 → 智能摘要和洞察
4. 综合结果 → 前端展示 → 用户交互

## 🎨 界面设计

### 主要特色
- **渐进式设计**: 引导用户逐步完成需求收集
- **状态可视化**: 清晰的进度指示和完成状态
- **智能建议**: AI驱动的搜索建议和相关主题
- **流式体验**: 模拟Perplexity的搜索和响应流程

### 响应式布局
- 桌面端：侧边栏 + 主内容区域
- 移动端：堆叠式布局，优化触控体验

## 🔍 使用示例

### 典型用例1：肿瘤研究
1. 问卷填写：人类 + scRNA-seq + 肿瘤异质性研究
2. AI生成查询：`human single cell RNA sequencing tumor heterogeneity`
3. 搜索结果：相关的肿瘤单细胞研究文献
4. AI洞察：技术趋势、方法学建议、成本分析

### 典型用例2：发育生物学
1. 问卷填写：小鼠 + scRNA-seq + 发育轨迹分析
2. AI生成查询：`mouse single cell trajectory analysis development`
3. 搜索结果：发育轨迹分析相关文献
4. AI洞察：最新算法、数据分析工具推荐

## 🚦 部署说明

### 前端部署
```bash
cd frontend
npm install
npm run build
npm start
```

### 后端部署
```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --host 0.0.0.0 --port 8000
```

### 访问地址
- 智能文献研究平台：`http://localhost:3000/smart-literature-research`
- API文档：`http://localhost:8000/docs`

## 🔮 未来扩展

### 短期计划
- 集成真实的外部文献API
- 增强AI分析算法
- 添加文献收藏和导出功能

### 长期愿景
- 构建专业的单细胞文献知识图谱
- 实现多模态搜索（图像、表格、代码）
- 开发协作研究工具

## 📞 技术支持

如有问题或建议，请联系开发团队或在GitHub上提交Issue。

---

**CellForge AI Team**  
*让AI赋能科学研究*