/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"bac346506d2c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcY2NhaVxcRGVza3RvcFxcRGV2XFxDZWxsRm9yZ2UgQUlcXGZyb250ZW5kXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYmFjMzQ2NTA2ZDJjXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _app_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/auth-context */ \"(rsc)/./contexts/auth-context.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(rsc)/./node_modules/sonner/dist/index.mjs\");\n\n\n\n\n\n\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"light\",\n                enableSystem: false,\n                disableTransitionOnChange: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                    children: [\n                        children,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_4__.Toaster, {\n                            position: \"top-right\",\n                            richColors: true,\n                            closeButton: true,\n                            duration: 4000\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\layout.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\layout.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 14,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\layout.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\nconst metadata = {\n    generator: 'v0.dev'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/loading.tsx":
/*!*************************!*\
  !*** ./app/loading.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\nfunction Loading() {\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbG9hZGluZy50c3giLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBO0lBQ3RCLE9BQU87QUFDVCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxjY2FpXFxEZXNrdG9wXFxEZXZcXENlbGxGb3JnZSBBSVxcZnJvbnRlbmRcXGFwcFxcbG9hZGluZy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9hZGluZygpIHtcbiAgcmV0dXJuIG51bGxcbn1cbiJdLCJuYW1lcyI6WyJMb2FkaW5nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Dev\\CellForge AI\\frontend\\components\\theme-provider.tsx",
"ThemeProvider",
);

/***/ }),

/***/ "(rsc)/./contexts/auth-context.tsx":
/*!***********************************!*\
  !*** ./contexts/auth-context.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Dev\\CellForge AI\\frontend\\contexts\\auth-context.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Dev\\CellForge AI\\frontend\\contexts\\auth-context.tsx",
"useAuth",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cccai%5CDesktop%5CDev%5CCellForge%20AI%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cccai%5CDesktop%5CDev%5CCellForge%20AI%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cccai%5CDesktop%5CDev%5CCellForge%20AI%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cccai%5CDesktop%5CDev%5CCellForge%20AI%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/loading.tsx */ \"(rsc)/./app/loading.tsx\"));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\layout.tsx\"],\n'loading': [module2, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\loading.tsx\"],\n'not-found': [module3, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module4, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module5, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cccai%5CDesktop%5CDev%5CCellForge%20AI%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cccai%5CDesktop%5CDev%5CCellForge%20AI%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(rsc)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/auth-context.tsx */ \"(rsc)/./contexts/auth-context.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(rsc)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 10,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBSVY7QUFFYixTQUFTQyxjQUFjLEVBQUVFLFFBQVEsRUFBRSxHQUFHQyxPQUEyQjtJQUN0RSxxQkFBTyw4REFBQ0Ysc0RBQWtCQTtRQUFFLEdBQUdFLEtBQUs7a0JBQUdEOzs7Ozs7QUFDekMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcY2NhaVxcRGVza3RvcFxcRGV2XFxDZWxsRm9yZ2UgQUlcXGZyb250ZW5kXFxjb21wb25lbnRzXFx0aGVtZS1wcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHtcbiAgVGhlbWVQcm92aWRlciBhcyBOZXh0VGhlbWVzUHJvdmlkZXIsXG4gIHR5cGUgVGhlbWVQcm92aWRlclByb3BzLFxufSBmcm9tICduZXh0LXRoZW1lcydcblxuZXhwb3J0IGZ1bmN0aW9uIFRoZW1lUHJvdmlkZXIoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfTogVGhlbWVQcm92aWRlclByb3BzKSB7XG4gIHJldHVybiA8TmV4dFRoZW1lc1Byb3ZpZGVyIHsuLi5wcm9wc30+e2NoaWxkcmVufTwvTmV4dFRoZW1lc1Byb3ZpZGVyPlxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiVGhlbWVQcm92aWRlciIsIk5leHRUaGVtZXNQcm92aWRlciIsImNoaWxkcmVuIiwicHJvcHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/auth-context.tsx":
/*!***********************************!*\
  !*** ./contexts/auth-context.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./lib/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\nconst defaultPermissions = [\n    {\n        id: \"view_dashboard\",\n        name: \"查看仪表盘\",\n        description: \"允许用户查看数据分析仪表盘\",\n        roles: [\n            \"super_admin\",\n            \"sales\"\n        ]\n    },\n    {\n        id: \"manage_customers\",\n        name: \"管理客户\",\n        description: \"允许用户添加、编辑和删除客户信息\",\n        roles: [\n            \"super_admin\",\n            \"sales\"\n        ]\n    },\n    {\n        id: \"view_customers\",\n        name: \"查看客户\",\n        description: \"允许用户查看客户信息\",\n        roles: [\n            \"super_admin\",\n            \"sales\",\n            \"operations\"\n        ]\n    },\n    {\n        id: \"manage_knowledge\",\n        name: \"管理知识库\",\n        description: \"允许用户添加、编辑和删除知识库内容\",\n        roles: [\n            \"super_admin\",\n            \"operations\"\n        ]\n    },\n    {\n        id: \"view_knowledge\",\n        name: \"查看知识库\",\n        description: \"允许用户查看知识库内容\",\n        roles: [\n            \"super_admin\",\n            \"sales\",\n            \"operations\",\n            \"customer\"\n        ]\n    },\n    {\n        id: \"manage_solutions\",\n        name: \"管理解决方案\",\n        description: \"允许用户创建和编辑解决方案\",\n        roles: [\n            \"super_admin\",\n            \"sales\",\n            \"operations\"\n        ]\n    },\n    {\n        id: \"view_solutions\",\n        name: \"查看解决方案\",\n        description: \"允许用户查看解决方案\",\n        roles: [\n            \"super_admin\",\n            \"sales\",\n            \"operations\",\n            \"customer\"\n        ]\n    },\n    {\n        id: \"manage_users\",\n        name: \"管理用户\",\n        description: \"允许用户添加、编辑和删除用户账户\",\n        roles: [\n            \"super_admin\"\n        ]\n    },\n    {\n        id: \"manage_permissions\",\n        name: \"管理权限\",\n        description: \"允许用户配置系统权限\",\n        roles: [\n            \"super_admin\"\n        ]\n    }\n];\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// 辅助函数：将API用户转换为前端用户格式\nfunction transformApiUser(apiUser) {\n    // 构建用户显示名称\n    let displayName = apiUser.username; // 默认使用用户名\n    if (apiUser.first_name && apiUser.last_name) {\n        displayName = `${apiUser.first_name} ${apiUser.last_name}`;\n    } else if (apiUser.first_name) {\n        displayName = apiUser.first_name;\n    } else if (apiUser.last_name) {\n        displayName = apiUser.last_name;\n    }\n    return {\n        ...apiUser,\n        id: apiUser.id.toString(),\n        name: displayName,\n        avatar: \"/placeholder.svg?height=40&width=40&query=avatar\"\n    };\n}\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultPermissions);\n    // 从API加载用户信息\n    const loadUser = async ()=>{\n        try {\n            // 检查是否有有效的token\n            const token = localStorage.getItem('cellforge_access_token');\n            if (!token) {\n                setUser(null);\n                return;\n            }\n            const apiUser = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.getCurrentUser();\n            const transformedUser = transformApiUser(apiUser);\n            setUser(transformedUser);\n        } catch (error) {\n            console.error(\"Failed to load user:\", error);\n            // 如果获取用户失败，清除可能过期的token\n            _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.logout();\n            setUser(null);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initAuth = {\n                \"AuthProvider.useEffect.initAuth\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        // 只在客户端执行\n                        if (false) {} else {\n                            // 服务端渲染时设置为null\n                            setUser(null);\n                        }\n                    } catch (error) {\n                        console.error(\"Auth initialization failed:\", error);\n                        setUser(null);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.initAuth\"];\n            initAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const hasPermission = (permission)=>{\n        return (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.hasPermission)(permission, user?.role);\n    };\n    const login = async (email, password)=>{\n        setIsLoading(true);\n        try {\n            // 调用登录API\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.login({\n                email,\n                password\n            });\n            // 登录成功后获取用户信息\n            await loadUser();\n        } catch (error) {\n            console.error(\"Login failed:\", error);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const register = async (userData)=>{\n        setIsLoading(true);\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.register(userData);\n        // 注册成功，但不自动登录，需要等待管理员审核\n        } catch (error) {\n            console.error(\"Registration failed:\", error);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const refreshUser = async ()=>{\n        await loadUser();\n    };\n    const logout = ()=>{\n        _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.logout();\n        setUser(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            isLoading,\n            permissions,\n            hasPermission,\n            login,\n            register,\n            logout,\n            refreshUser\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\contexts\\\\auth-context.tsx\",\n        lineNumber: 225,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/auth-context.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TokenManager: () => (/* binding */ TokenManager),\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   conversationApi: () => (/* binding */ conversationApi),\n/* harmony export */   customerProfileApi: () => (/* binding */ customerProfileApi),\n/* harmony export */   intelligentRecommendationApi: () => (/* binding */ intelligentRecommendationApi),\n/* harmony export */   literatureApi: () => (/* binding */ literatureApi),\n/* harmony export */   smartLiteratureApi: () => (/* binding */ smartLiteratureApi),\n/* harmony export */   solutionApi: () => (/* binding */ solutionApi)\n/* harmony export */ });\n/**\n * API调用工具函数\n */ // 动态获取API基础URL\nconst getApiBaseUrl = ()=>{\n    // 优先使用环境变量\n    if (process.env.NEXT_PUBLIC_API_URL) {\n        return process.env.NEXT_PUBLIC_API_URL;\n    }\n    // 在浏览器环境中，使用当前主机的IP和端口8000\n    if (false) {}\n    // 服务端渲染时的默认值\n    return 'http://localhost:8000/api/v1';\n};\nconst API_BASE_URL = getApiBaseUrl();\n// Token管理\nclass TokenManager {\n    static{\n        this.TOKEN_KEY = 'cellforge_access_token';\n    }\n    static{\n        this.REFRESH_KEY = 'cellforge_refresh_token';\n    }\n    static getToken() {\n        if (true) return null;\n        return localStorage.getItem(this.TOKEN_KEY);\n    }\n    static setToken(token) {\n        if (true) return;\n        localStorage.setItem(this.TOKEN_KEY, token);\n    }\n    static removeToken() {\n        if (true) return;\n        localStorage.removeItem(this.TOKEN_KEY);\n        localStorage.removeItem(this.REFRESH_KEY);\n    }\n    static isTokenExpired(token) {\n        try {\n            const payload = JSON.parse(atob(token.split('.')[1]));\n            return payload.exp * 1000 < Date.now();\n        } catch  {\n            return true;\n        }\n    }\n}\n// HTTP客户端类\nclass ApiClient {\n    constructor(baseURL = API_BASE_URL){\n        this.baseURL = baseURL;\n    }\n    async request(endpoint, options = {}) {\n        const url = `${this.baseURL}${endpoint}`;\n        const token = TokenManager.getToken();\n        const headers = {\n            'Content-Type': 'application/json',\n            ...options.headers\n        };\n        // 添加认证头\n        if (token && !TokenManager.isTokenExpired(token)) {\n            headers.Authorization = `Bearer ${token}`;\n        }\n        const config = {\n            ...options,\n            headers\n        };\n        try {\n            const response = await fetch(url, config);\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                // 根据HTTP状态码提供更详细的错误信息\n                let errorMessage = errorData.detail || errorData.message || `HTTP ${response.status}: ${response.statusText}`;\n                if (response.status === 401) {\n                    errorMessage = errorData.detail || \"邮箱或密码错误\";\n                } else if (response.status === 403) {\n                    errorMessage = errorData.detail || \"账户已被暂停或无权限访问\";\n                } else if (response.status === 404) {\n                    errorMessage = \"请求的资源不存在\";\n                } else if (response.status === 422) {\n                    errorMessage = \"请求数据格式错误\";\n                } else if (response.status >= 500) {\n                    errorMessage = \"服务器内部错误，请稍后重试\";\n                }\n                throw new Error(errorMessage);\n            }\n            return await response.json();\n        } catch (error) {\n            // 网络错误处理\n            if (error instanceof TypeError && error.message.includes('fetch')) {\n                throw new Error('网络连接失败，请检查网络连接');\n            }\n            console.error('API请求失败:', error);\n            throw error;\n        }\n    }\n    // GET请求\n    async get(endpoint) {\n        return this.request(endpoint, {\n            method: 'GET'\n        });\n    }\n    // POST请求\n    async post(endpoint, data) {\n        return this.request(endpoint, {\n            method: 'POST',\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    // PUT请求\n    async put(endpoint, data) {\n        return this.request(endpoint, {\n            method: 'PUT',\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    // DELETE请求\n    async delete(endpoint) {\n        return this.request(endpoint, {\n            method: 'DELETE'\n        });\n    }\n}\n// 创建API客户端实例\nconst apiClient = new ApiClient();\n// 认证API\nconst authApi = {\n    // 用户登录\n    async login (credentials) {\n        const response = await apiClient.post('/auth/login', credentials);\n        TokenManager.setToken(response.access_token);\n        return response;\n    },\n    // 用户注册\n    async register (userData) {\n        return apiClient.post('/auth/register', userData);\n    },\n    // 获取当前用户信息\n    async getCurrentUser () {\n        return apiClient.get('/auth/me');\n    },\n    // 刷新令牌\n    async refreshToken () {\n        const response = await apiClient.post('/auth/refresh');\n        TokenManager.setToken(response.access_token);\n        return response;\n    },\n    // 修改密码\n    async changePassword (data) {\n        return apiClient.post('/auth/change-password', data);\n    },\n    // 登出\n    logout () {\n        TokenManager.removeToken();\n    }\n};\n// 对话API\nconst conversationApi = {\n    // 发送消息\n    async sendMessage (data) {\n        return apiClient.post('/conversation/chat', data);\n    },\n    // 获取对话历史\n    async getHistory (page = 1, size = 20) {\n        return apiClient.get(`/conversation/history?page=${page}&size=${size}`);\n    }\n};\n// 文献API\nconst literatureApi = {\n    // 搜索文献\n    async searchLiterature (data) {\n        return apiClient.post('/literature/search', data);\n    },\n    // 获取文献推荐\n    async getLiteratureRecommendations (data) {\n        return apiClient.post('/literature/recommendations', data);\n    },\n    // 获取文献分类\n    async getCategories () {\n        return apiClient.get('/literature/categories');\n    },\n    // 获取热门文献\n    async getTrendingLiterature (limit = 10) {\n        return apiClient.get(`/literature/trending?limit=${limit}`);\n    },\n    // 获取文献统计\n    async getStats () {\n        return apiClient.get('/literature/stats');\n    },\n    // 获取文献搜索状态\n    async getSearchStatus () {\n        return apiClient.get('/literature/search-status');\n    },\n    // 基于需求搜集文献\n    async collectForRequirements (data) {\n        return apiClient.post('/literature/collect-for-requirements', data);\n    }\n};\n// 客户画像API\nconst customerProfileApi = {\n    // 获取客户画像\n    async getProfile (userId) {\n        return apiClient.get(`/customer/profile/${userId}`);\n    },\n    // 分析客户画像\n    async analyzeProfile (data) {\n        return apiClient.post('/customer/analyze', data);\n    },\n    // 获取客户洞察\n    async getInsights (userId) {\n        return apiClient.get(`/customer/insights/${userId}`);\n    },\n    // 获取个性化推荐\n    async getRecommendations (userId) {\n        return apiClient.get(`/customer/recommendations/${userId}`);\n    },\n    // 更新客户画像\n    async updateProfile (userId, profileData) {\n        return apiClient.put(`/customer/profile/${userId}`, profileData);\n    },\n    // 基于需求更新画像\n    async updateFromRequirements (userId, requirementData) {\n        return apiClient.post(`/customer/requirements/${userId}`, requirementData);\n    },\n    // 网页分析\n    async analyzeWebProfile (data) {\n        return apiClient.post('/profile-analysis/analyze-url', data);\n    },\n    // 文本分析\n    async analyzeTextProfile (data) {\n        return apiClient.post('/profile-analysis/analyze-text', data);\n    },\n    // 获取分析历史\n    async getAnalysisHistory (page = 1, size = 20) {\n        return apiClient.get(`/profile-analysis/analysis-history?page=${page}&size=${size}`);\n    }\n};\n// 智能文献搜索API\nconst smartLiteratureApi = {\n    // 生成智能搜索查询\n    async generateSmartQueries (data) {\n        return apiClient.post('/smart-literature/generate-smart-queries', data);\n    },\n    // Perplexity风格搜索\n    async perplexitySearch (data) {\n        return apiClient.post('/smart-literature/perplexity-search', data);\n    },\n    // 增强文献搜索\n    async enhancedLiteratureSearch (data) {\n        return apiClient.post('/smart-literature/enhanced-literature-search', data);\n    }\n};\n// 方案生成API\nconst solutionApi = {\n    // 生成完整方案\n    async generateSolution (data) {\n        return apiClient.post('/solution/generate-solution', data);\n    },\n    // 快速成本估算\n    async quickEstimate (requirements) {\n        return apiClient.post('/solution/quick-estimate', requirements);\n    },\n    // 获取方案模板\n    async getSolutionTemplates () {\n        return apiClient.get('/solution/solution-templates');\n    },\n    // 验证需求完整性\n    async validateRequirements (requirements) {\n        return apiClient.post('/solution/validate-requirements', requirements);\n    },\n    // 获取平台对比\n    async getPlatformComparison () {\n        return apiClient.get('/solution/platform-comparison');\n    },\n    // 生成综合方案框架（按设计文档要求）\n    async generateComprehensiveFramework (data) {\n        return apiClient.post('/comprehensive-solution/comprehensive-framework', {\n            requirements: data.requirements,\n            user_message: data.user_message || '',\n            framework_template: data.framework_template || 'standard',\n            enable_literature_search: data.enable_literature_search !== false\n        });\n    }\n};\n// 智能推荐API\nconst intelligentRecommendationApi = {\n    // 获取综合智能推荐\n    async getComprehensiveRecommendation (data) {\n        return apiClient.post('/intelligent-recommendation/comprehensive-recommendation', data);\n    },\n    // 获取智能关键词扩展\n    async getKeywordExpansion (data) {\n        return apiClient.post('/intelligent-recommendation/keyword-expansion', data);\n    },\n    // 获取热点文献推荐\n    async getHotPapers (data) {\n        return apiClient.post('/intelligent-recommendation/hot-papers', data);\n    },\n    // 获取技术平台推荐\n    async getTechRecommendations (data) {\n        return apiClient.post('/intelligent-recommendation/tech-recommendations', data);\n    },\n    // 获取项目整体解决方案\n    async getProjectSolution (data) {\n        return apiClient.post('/intelligent-recommendation/project-solution', data);\n    },\n    // 健康检查\n    async healthCheck () {\n        return apiClient.get('/intelligent-recommendation/health');\n    }\n};\n// 导出Token管理器和API客户端\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PERMISSIONS: () => (/* binding */ PERMISSIONS),\n/* harmony export */   getUserPermissions: () => (/* binding */ getUserPermissions),\n/* harmony export */   hasAllPermissions: () => (/* binding */ hasAllPermissions),\n/* harmony export */   hasAnyPermission: () => (/* binding */ hasAnyPermission),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission)\n/* harmony export */ });\n/**\n * 权限检查工具函数\n */ // 权限定义\nconst PERMISSIONS = {\n    // 仪表盘权限\n    view_dashboard: [\n        \"super_admin\",\n        \"sales\",\n        \"operations\"\n    ],\n    export_dashboard: [\n        \"super_admin\",\n        \"sales\"\n    ],\n    customize_dashboard: [\n        \"super_admin\"\n    ],\n    // 客户管理权限\n    view_customers: [\n        \"super_admin\",\n        \"sales\",\n        \"operations\"\n    ],\n    manage_customers: [\n        \"super_admin\",\n        \"sales\"\n    ],\n    add_customers: [\n        \"super_admin\",\n        \"sales\"\n    ],\n    edit_customers: [\n        \"super_admin\",\n        \"sales\"\n    ],\n    delete_customers: [\n        \"super_admin\"\n    ],\n    export_customers: [\n        \"super_admin\",\n        \"sales\"\n    ],\n    // 方案管理权限\n    view_solutions: [\n        \"super_admin\",\n        \"sales\",\n        \"operations\",\n        \"customer\"\n    ],\n    create_solutions: [\n        \"super_admin\",\n        \"sales\",\n        \"operations\"\n    ],\n    edit_solutions: [\n        \"super_admin\",\n        \"sales\",\n        \"operations\"\n    ],\n    delete_solutions: [\n        \"super_admin\"\n    ],\n    share_solutions: [\n        \"super_admin\",\n        \"sales\"\n    ],\n    // 知识库权限\n    view_knowledge: [\n        \"super_admin\",\n        \"sales\",\n        \"operations\",\n        \"customer\"\n    ],\n    manage_knowledge: [\n        \"super_admin\",\n        \"operations\"\n    ],\n    add_knowledge: [\n        \"super_admin\",\n        \"operations\"\n    ],\n    edit_knowledge: [\n        \"super_admin\",\n        \"operations\"\n    ],\n    delete_knowledge: [\n        \"super_admin\"\n    ],\n    approve_knowledge: [\n        \"super_admin\",\n        \"operations\"\n    ],\n    // 系统管理权限\n    manage_users: [\n        \"super_admin\"\n    ],\n    manage_permissions: [\n        \"super_admin\"\n    ],\n    manage_system: [\n        \"super_admin\",\n        \"operations\"\n    ],\n    view_analytics: [\n        \"super_admin\",\n        \"sales\",\n        \"operations\"\n    ]\n};\n/**\n * 检查用户是否具有指定权限\n * @param permission 权限名称\n * @param userRole 用户角色\n * @returns 是否具有权限\n */ function hasPermission(permission, userRole) {\n    if (!userRole) return false;\n    const allowedRoles = PERMISSIONS[permission];\n    return allowedRoles.includes(userRole);\n}\n/**\n * 获取用户的所有权限\n * @param userRole 用户角色\n * @returns 权限列表\n */ function getUserPermissions(userRole) {\n    return Object.keys(PERMISSIONS).filter((permission)=>hasPermission(permission, userRole));\n}\n/**\n * 检查用户是否具有任一权限\n * @param permissions 权限列表\n * @param userRole 用户角色\n * @returns 是否具有任一权限\n */ function hasAnyPermission(permissions, userRole) {\n    return permissions.some((permission)=>hasPermission(permission, userRole));\n}\n/**\n * 检查用户是否具有所有权限\n * @param permissions 权限列表\n * @param userRole 用户角色\n * @returns 是否具有所有权限\n */ function hasAllPermissions(permissions, userRole) {\n    return permissions.every((permission)=>hasPermission(permission, userRole));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/auth-context.tsx */ \"(ssr)/./contexts/auth-context.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(ssr)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/sonner","vendor-chunks/next-themes","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cccai%5CDesktop%5CDev%5CCellForge%20AI%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cccai%5CDesktop%5CDev%5CCellForge%20AI%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();