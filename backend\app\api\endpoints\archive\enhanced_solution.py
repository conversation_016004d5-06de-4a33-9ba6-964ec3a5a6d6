"""
增强方案建议API端点
提供5个核心功能的统一API接口
"""
from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any, Optional
import logging
from datetime import datetime

from app.services.enhanced_solution_service import get_enhanced_solution_service
from app.services.ai_service import AIService

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/comprehensive-solution")
async def generate_comprehensive_solution(
    requirements: Dict[str, Any]
) -> Dict[str, Any]:
    """
    生成综合解决方案，包含5个核心功能：
    1. 个性化方案建议
    2. 文献推荐
    3. 搜索关键词
    4. 领域痛点分析
    5. 项目风险评估
    """
    try:
        # 获取增强方案服务实例
        enhanced_service = get_enhanced_solution_service()
        
        # 首先进行AI意图分析
        ai_service = AIService()
        intent_analysis = None
        
        try:
            intent_analysis = await ai_service.analyze_user_intent(
                requirements=requirements,
                user_message=requirements.get('userMessage', '')
            )
            logger.info(f"AI意图分析完成: {intent_analysis.get('research_domain', '未知')}")
        except Exception as e:
            logger.warning(f"AI意图分析失败，使用基础方法: {e}")
        
        # 生成综合解决方案
        solution = await enhanced_service.generate_comprehensive_solution(
            requirements=requirements,
            intent_analysis=intent_analysis
        )
        
        return {
            "success": True,
            "data": solution,
            "message": "综合解决方案生成成功",
            "generated_at": datetime.now().isoformat(),
            "version": "enhanced_v1.0"
        }
        
    except Exception as e:
        logger.error(f"综合方案生成失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"综合方案生成失败: {str(e)}"
        )

@router.post("/personalized-solution")
async def generate_personalized_solution(
    requirements: Dict[str, Any]
) -> Dict[str, Any]:
    """生成个性化技术方案"""
    try:
        enhanced_service = get_enhanced_solution_service()
        
        # AI意图分析
        ai_service = AIService()
        intent_analysis = None
        try:
            intent_analysis = await ai_service.analyze_user_intent(requirements)
        except Exception as e:
            logger.warning(f"AI意图分析失败: {e}")
        
        # 生成个性化方案
        solution = await enhanced_service._generate_personalized_solution(
            requirements, intent_analysis
        )
        
        return {
            "success": True,
            "data": solution,
            "message": "个性化方案生成成功"
        }
        
    except Exception as e:
        logger.error(f"个性化方案生成失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"个性化方案生成失败: {str(e)}"
        )

@router.post("/literature-recommendations")
async def generate_literature_recommendations(
    requirements: Dict[str, Any]
) -> Dict[str, Any]:
    """生成文献推荐"""
    try:
        enhanced_service = get_enhanced_solution_service()
        
        # AI意图分析
        ai_service = AIService()
        intent_analysis = None
        try:
            intent_analysis = await ai_service.analyze_user_intent(requirements)
        except Exception as e:
            logger.warning(f"AI意图分析失败: {e}")
        
        # 生成文献推荐
        literature = enhanced_service._generate_literature_recommendations(
            requirements, intent_analysis
        )
        
        return {
            "success": True,
            "data": literature,
            "message": "文献推荐生成成功"
        }
        
    except Exception as e:
        logger.error(f"文献推荐生成失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"文献推荐生成失败: {str(e)}"
        )

@router.post("/search-keywords")
async def generate_search_keywords(
    requirements: Dict[str, Any]
) -> Dict[str, Any]:
    """生成搜索关键词"""
    try:
        enhanced_service = get_enhanced_solution_service()
        
        # AI意图分析
        ai_service = AIService()
        intent_analysis = None
        try:
            intent_analysis = await ai_service.analyze_user_intent(requirements)
        except Exception as e:
            logger.warning(f"AI意图分析失败: {e}")
        
        # 生成搜索关键词
        keywords = enhanced_service._generate_search_keywords(
            requirements, intent_analysis
        )
        
        return {
            "success": True,
            "data": keywords,
            "message": "搜索关键词生成成功"
        }
        
    except Exception as e:
        logger.error(f"搜索关键词生成失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"搜索关键词生成失败: {str(e)}"
        )

@router.post("/pain-point-analysis")
async def analyze_domain_pain_points(
    requirements: Dict[str, Any]
) -> Dict[str, Any]:
    """分析领域痛点"""
    try:
        enhanced_service = get_enhanced_solution_service()
        
        # AI意图分析
        ai_service = AIService()
        intent_analysis = None
        try:
            intent_analysis = await ai_service.analyze_user_intent(requirements)
        except Exception as e:
            logger.warning(f"AI意图分析失败: {e}")
        
        # 分析领域痛点
        pain_points = enhanced_service._analyze_domain_pain_points(
            requirements, intent_analysis
        )
        
        return {
            "success": True,
            "data": pain_points,
            "message": "领域痛点分析完成"
        }
        
    except Exception as e:
        logger.error(f"领域痛点分析失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"领域痛点分析失败: {str(e)}"
        )

@router.post("/risk-assessment")
async def assess_project_risks(
    requirements: Dict[str, Any]
) -> Dict[str, Any]:
    """评估项目风险"""
    try:
        enhanced_service = get_enhanced_solution_service()
        
        # AI意图分析
        ai_service = AIService()
        intent_analysis = None
        try:
            intent_analysis = await ai_service.analyze_user_intent(requirements)
        except Exception as e:
            logger.warning(f"AI意图分析失败: {e}")
        
        # 评估项目风险
        risks = enhanced_service._assess_project_risks(
            requirements, intent_analysis
        )
        
        return {
            "success": True,
            "data": risks,
            "message": "项目风险评估完成"
        }
        
    except Exception as e:
        logger.error(f"项目风险评估失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"项目风险评估失败: {str(e)}"
        )

@router.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "enhanced_solution",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }