"use client"

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Brain, 
  Target, 
  TrendingUp,
  CheckCircle,
  Lightbulb
} from 'lucide-react'

interface ResearchIntentGuessProps {
  research_intent_guess: {
    primary_intent: string
    research_direction: string
    analysis_type: string
    confidence_level: string
    key_interests?: string[]
    inferred_goals?: string[]
  }
}

const ResearchIntentGuessPanel: React.FC<ResearchIntentGuessProps> = ({ research_intent_guess }) => {
  const getConfidenceColor = (confidence: string) => {
    const level = parseInt(confidence.replace('%', ''))
    if (level >= 90) return 'text-green-600 bg-green-50 border-green-200'
    if (level >= 70) return 'text-blue-600 bg-blue-50 border-blue-200'
    return 'text-amber-600 bg-amber-50 border-amber-200'
  }

  const getConfidenceIcon = (confidence: string) => {
    const level = parseInt(confidence.replace('%', ''))
    if (level >= 90) return <CheckCircle className="h-4 w-4 text-green-600" />
    if (level >= 70) return <Target className="h-4 w-4 text-blue-600" />
    return <TrendingUp className="h-4 w-4 text-amber-600" />
  }

  return (
    <Card className="border-purple-200">
      <CardHeader className="bg-gradient-to-r from-purple-50 to-indigo-50 border-b border-purple-200">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Brain className="h-5 w-5 text-purple-600" />
          🎯 研究意图智能分析
        </CardTitle>
        <p className="text-sm text-slate-600 mt-1">基于您的输入，AI分析您的研究目标和关注重点</p>
      </CardHeader>
      <CardContent className="p-6">
        {/* 主要意图 */}
        <div className="mb-6">
          <div className="flex items-center gap-2 mb-3">
            <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-full flex items-center justify-center">
              <Lightbulb className="h-4 w-4 text-white" />
            </div>
            <h3 className="font-semibold text-slate-800">研究意图分析</h3>
          </div>
          <div className="p-4 bg-slate-50 rounded-lg border border-slate-200">
            <p className="text-slate-700 leading-relaxed">{research_intent_guess.primary_intent}</p>
          </div>
        </div>

        {/* 研究属性 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="p-4 bg-white rounded-lg border border-slate-200">
            <div className="text-sm text-slate-500 mb-1">研究方向</div>
            <div className="font-semibold text-slate-800">{research_intent_guess.research_direction}</div>
          </div>
          <div className="p-4 bg-white rounded-lg border border-slate-200">
            <div className="text-sm text-slate-500 mb-1">分析类型</div>
            <div className="font-semibold text-slate-800">{research_intent_guess.analysis_type}</div>
          </div>
        </div>

        {/* 置信度 */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-slate-700">分析置信度</span>
            <div className={`inline-flex items-center gap-1 px-3 py-1 rounded-full border ${getConfidenceColor(research_intent_guess.confidence_level)}`}>
              {getConfidenceIcon(research_intent_guess.confidence_level)}
              <span className="text-sm font-medium">{research_intent_guess.confidence_level}</span>
            </div>
          </div>
          <div className="w-full bg-slate-200 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-purple-400 to-indigo-600 h-2 rounded-full transition-all"
              style={{ width: research_intent_guess.confidence_level }}
            />
          </div>
        </div>

        {/* 关键兴趣点 */}
        {(research_intent_guess.key_interests || research_intent_guess.inferred_goals) && (
          <div>
            <h4 className="font-medium text-slate-700 mb-3 flex items-center gap-2">
              <Target className="h-4 w-4 text-indigo-500" />
              推测的研究重点
            </h4>
            <div className="space-y-2">
              {(research_intent_guess.key_interests || research_intent_guess.inferred_goals || []).map((interest, index) => (
                <div key={index} className="flex items-start gap-3 p-3 bg-indigo-50 rounded-lg border border-indigo-200">
                  <div className="w-6 h-6 bg-indigo-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-xs font-medium text-indigo-600">{index + 1}</span>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-indigo-800">{interest}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 分析说明 */}
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
              <Brain className="h-3 w-3 text-blue-600" />
            </div>
            <div className="text-sm text-blue-800">
              <p><strong>分析说明：</strong>基于您的输入信息，AI系统分析了您的研究背景、样本类型和目标，推测了最可能的研究意图。这有助于为您提供更精准的技术方案和文献推荐。</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default ResearchIntentGuessPanel