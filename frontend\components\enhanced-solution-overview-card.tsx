"use client"

import React, { useState } from 'react'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Microscope, 
  Clock, 
  DollarSign, 
  ChevronDown, 
  ChevronUp,
  AlertCircle,
  Settings,
  CheckCircle2,
  PlayCircle,
  Target
} from 'lucide-react'

interface SolutionOverviewProps {
  solution_overview: {
    research_type: string
    estimated_cost: string
    recommended_platform: string
    project_timeline: string
    key_process_steps: string[]
    critical_considerations: string[]
    technical_details: {
      platform_specifications: string
      sample_prep_protocol: string
      analysis_pipeline: string
      expected_outcomes: string
    }
  }
}

const EnhancedSolutionOverviewCard: React.FC<SolutionOverviewProps> = ({ solution_overview }) => {
  const [showTechnicalDetails, setShowTechnicalDetails] = useState(false)
  const [showProcessSteps, setShowProcessSteps] = useState(true)
  const [showConsiderations, setShowConsiderations] = useState(true)

  return (
    <Card className="border-blue-200 shadow-lg">
      <CardHeader className="bg-gradient-to-br from-blue-50 to-indigo-100 border-b border-blue-200">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
            <Microscope className="h-6 w-6 text-white" />
          </div>
          <div className="flex-1">
            <CardTitle className="text-xl font-bold text-slate-800">
              🧬 单细胞测序解决方案
            </CardTitle>
            <p className="text-sm text-slate-600 mt-1">
              基于AI分析的个性化技术方案
            </p>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-6">
        {/* 核心信息概览 */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="p-4 bg-white rounded-lg border border-slate-200 shadow-sm">
            <div className="flex items-center gap-2 mb-2">
              <Target className="h-4 w-4 text-blue-500" />
              <span className="text-sm text-slate-500">研究类型</span>
            </div>
            <div className="font-semibold text-slate-800">{solution_overview.research_type}</div>
            <div className="text-xs text-slate-600 mt-1">
              平台：{solution_overview.recommended_platform}
            </div>
          </div>
          
          <div className="p-4 bg-white rounded-lg border border-slate-200 shadow-sm">
            <div className="flex items-center gap-2 mb-2">
              <DollarSign className="h-4 w-4 text-green-500" />
              <span className="text-sm text-slate-500">预估成本</span>
            </div>
            <div className="font-semibold text-slate-800">{solution_overview.estimated_cost}</div>
            <div className="text-xs text-slate-600 mt-1">
              周期：{solution_overview.project_timeline}
            </div>
          </div>
        </div>

        {/* 全流程关键环节 */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-slate-800 flex items-center gap-2">
              <PlayCircle className="h-4 w-4 text-indigo-500" />
              全流程关键环节
            </h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowProcessSteps(!showProcessSteps)}
              className="text-slate-600 hover:text-slate-800"
            >
              {showProcessSteps ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </div>
          
          {showProcessSteps && (
            <div className="space-y-3">
              {solution_overview.key_process_steps.map((step, index) => {
                const [timeAndTitle, description] = step.split(' - ')
                return (
                  <div key={index} className="flex items-start gap-3 p-3 bg-slate-50 rounded-lg border border-slate-200">
                    <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center flex-shrink-0">
                      <span className="text-sm font-bold text-white">{index + 1}</span>
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-slate-800">{timeAndTitle}</div>
                      {description && (
                        <div className="text-sm text-slate-600 mt-1">{description}</div>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </div>

        {/* 关键注意事项 */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-slate-800 flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-amber-500" />
              关键注意事项
            </h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowConsiderations(!showConsiderations)}
              className="text-slate-600 hover:text-slate-800"
            >
              {showConsiderations ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </div>
          
          {showConsiderations && (
            <div className="space-y-2">
              {solution_overview.critical_considerations.map((consideration, index) => (
                <div key={index} className="flex items-start gap-3 p-3 bg-amber-50 rounded-lg border border-amber-200">
                  <AlertCircle className="h-4 w-4 text-amber-600 flex-shrink-0 mt-0.5" />
                  <div className="text-sm text-amber-800">{consideration}</div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 技术方案详情 */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-slate-800 flex items-center gap-2">
              <Settings className="h-4 w-4 text-slate-500" />
              技术方案详情
            </h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowTechnicalDetails(!showTechnicalDetails)}
              className="text-slate-600 hover:text-slate-800"
            >
              {showTechnicalDetails ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </div>
          
          {showTechnicalDetails && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-white rounded-lg border border-slate-200">
                <div className="text-sm text-slate-500 mb-1">平台规格</div>
                <div className="text-sm text-slate-700">{solution_overview.technical_details.platform_specifications}</div>
              </div>
              
              <div className="p-4 bg-white rounded-lg border border-slate-200">
                <div className="text-sm text-slate-500 mb-1">样本制备</div>
                <div className="text-sm text-slate-700">{solution_overview.technical_details.sample_prep_protocol}</div>
              </div>
              
              <div className="p-4 bg-white rounded-lg border border-slate-200">
                <div className="text-sm text-slate-500 mb-1">分析流程</div>
                <div className="text-sm text-slate-700">{solution_overview.technical_details.analysis_pipeline}</div>
              </div>
              
              <div className="p-4 bg-white rounded-lg border border-slate-200">
                <div className="text-sm text-slate-500 mb-1">预期结果</div>
                <div className="text-sm text-slate-700">{solution_overview.technical_details.expected_outcomes}</div>
              </div>
            </div>
          )}
        </div>

        {/* 方案状态指示 */}
        <div className="mt-6 pt-4 border-t border-slate-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CheckCircle2 className="h-4 w-4 text-green-500" />
              <span className="text-sm text-slate-600">方案状态：</span>
              <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200">
                已优化
              </Badge>
            </div>
            <div className="text-xs text-slate-500">
              生成时间：{new Date().toLocaleString('zh-CN')}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default EnhancedSolutionOverviewCard