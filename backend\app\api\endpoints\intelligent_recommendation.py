"""
智能推荐API端点
提供综合文献推荐和技术方案建议
集成完全AI驱动的文献推荐系统
"""
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
import logging

from app.services.intelligent_recommendation_service import get_intelligent_recommendation_service, IntelligentRecommendationService
from app.services.fully_ai_driven_literature_service import get_fully_ai_driven_service, FullyAIDrivenRecommendationService
from app.services.comprehensive_solution_service import get_comprehensive_solution_service

logger = logging.getLogger(__name__)

router = APIRouter()


class RequirementRequest(BaseModel):
    """需求请求模型"""
    speciesType: str
    experimentType: str
    researchGoal: str
    sampleType: str
    sampleCount: str
    sampleStatus: str
    processingMethod: str
    cellCount: str
    cellViability: str
    budgetRange: str
    projectDuration: str
    urgency: str
    sequencingDepth: str
    analysisType: str
    dataAnalysisNeeds: str
    cellSorting: str
    additionalRequirements: Optional[str] = None


class IntelligentRecommendationResponse(BaseModel):
    """智能推荐响应模型"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    processing_time: Optional[float] = None


@router.post("/comprehensive-recommendation", response_model=IntelligentRecommendationResponse)
async def get_comprehensive_recommendation(
    request: RequirementRequest,
    comprehensive_service=Depends(get_comprehensive_solution_service),
    fallback_service: IntelligentRecommendationService = Depends(get_intelligent_recommendation_service)
):
    """
    获取基于优化框架的综合智能推荐
    使用新的综合方案框架服务，包括研究意图分析、精准搜索链接等
    """
    try:
        import time
        start_time = time.time()
        
        # 转换请求为新服务所需格式
        requirements = {
            "researchGoal": request.researchGoal,
            "sampleType": request.sampleType,
            "experimentType": request.experimentType,
            "speciesType": request.speciesType,
            "cellCount": request.cellCount,
            "budget": request.budgetRange,
            "timeline": request.projectDuration,
            "urgencyLevel": request.urgency,
            "sampleCount": request.sampleCount,
            "sampleCondition": request.sampleStatus,
            "cellViability": request.cellViability,
            "sequencingDepth": request.sequencingDepth,
            "analysisType": request.analysisType
        }
        
        # 构造用户消息
        user_message = f"""
        我需要进行{request.researchGoal}的研究。
        样本类型：{request.sampleType}
        实验物种：{request.speciesType}
        预期细胞数量：{request.cellCount}
        预算范围：{request.budgetRange}
        项目周期：{request.projectDuration}
        """
        if request.additionalRequirements:
            user_message += f"\n额外需求：{request.additionalRequirements}"
        
        logger.info(f"🚀 使用综合方案框架生成推荐，研究目标: {requirements.get('researchGoal')}")
        
        try:
            # 使用新的综合方案框架服务
            framework_result = await comprehensive_service.generate_comprehensive_framework(
                requirements=requirements,
                user_message=user_message,
                framework_template="standard",
                enable_literature_search=True
            )
            
            processing_time = time.time() - start_time
            logger.info(f"✅ 综合方案框架生成成功，处理时间: {processing_time:.2f}秒")
            
            # 转换新框架数据为前端期望的格式
            response_data = await convert_framework_to_legacy_format(framework_result, requirements)
            
            return IntelligentRecommendationResponse(
                success=True,
                data=response_data,
                processing_time=processing_time
            )
                
        except Exception as framework_error:
            logger.warning(f"综合方案框架服务失败: {framework_error}，使用传统推荐服务作为回退")
            
            # 回退到传统推荐服务，保持原始格式转换
            old_requirements = {
                "speciesType": request.speciesType,
                "experimentType": request.experimentType, 
                "researchGoal": request.researchGoal,
                "sampleType": request.sampleType,
                "sampleCount": request.sampleCount,
                "sampleStatus": request.sampleStatus,
                "processingMethod": request.processingMethod,
                "cellCount": request.cellCount,
                "cellViability": request.cellViability,
                "budgetRange": request.budgetRange,
                "projectDuration": request.projectDuration,
                "urgency": request.urgency,
                "sequencingDepth": request.sequencingDepth,
                "analysisType": request.analysisType,
                "dataAnalysisNeeds": request.dataAnalysisNeeds,
                "cellSorting": request.cellSorting,
                "additionalRequirements": request.additionalRequirements
            }
            
            recommendation = await fallback_service.generate_comprehensive_recommendation(old_requirements)
            
            processing_time = time.time() - start_time
            logger.info(f"使用回退服务生成推荐成功，处理时间: {processing_time:.2f}秒")
            
            return IntelligentRecommendationResponse(
                success=True,
                data=recommendation,
                processing_time=processing_time
            )
        
    except Exception as e:
        logger.error(f"生成智能推荐完全失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"生成智能推荐失败: {str(e)}"
        )


async def convert_framework_to_legacy_format(framework_result: Dict[str, Any], requirements: Dict[str, Any]) -> Dict[str, Any]:
    """
    将新的综合方案框架数据转换为前端期望的传统格式
    """
    research_intent = framework_result.get("research_intent_analysis", {})
    solution_overview = framework_result.get("solution_overview", {})
    risk_assessment = framework_result.get("risk_assessment", {})
    platform_comparison = framework_result.get("platform_comparison", {})
    
    # 提取搜索链接
    precision_links = research_intent.get("precision_search_links", {}).get("primary_links", {})
    search_links = []
    for platform, link_info in precision_links.items():
        search_links.append({
            "platform": link_info.get("name", platform),
            "url": link_info.get("url", ""),
            "description": link_info.get("description", ""),
            "icon": link_info.get("icon", "🔗")
        })
    
    # 提取关键词信息 - 按照文档要求整合展示
    integrated_keywords = research_intent.get("integrated_keywords", {})
    primary_query = integrated_keywords.get("primary_query", "")
    research_focus = integrated_keywords.get("research_focus", "")
    
    # 生成整合后的关键词展示（不是分散的关键词列表）
    expanded_keywords = {
        "integrated_research_query": primary_query,  # 整合后的主要研究查询
        "research_focus_display": research_focus,    # 研究焦点展示
        "alternative_search_queries": integrated_keywords.get("alternative_queries", []),  # 备选搜索查询
        "optimization_context": integrated_keywords.get("query_optimization_notes", "基于AI意图分析的整合关键词")
    }
    
    # 构建技术推荐 - 基于综合方案框架的智能推荐
    recommended_platform = solution_overview.get("recommended_platform", "10x Genomics")
    estimated_cost = solution_overview.get("estimated_cost", "¥10,000-15,000")
    timeline = solution_overview.get("expected_timeline", "4-6周")
    
    # 从预算字符串中提取数字范围
    budget_range = requirements.get("budget", "10-20万")
    cost_match_score = 0.9 if "10-20万" in budget_range else 0.8
    
    tech_recommendations = [{
        "platform": recommended_platform,
        "platform_id": "comprehensive_v1",
        "specifications": f"基于{research_focus}优化的综合解决方案",
        "cost_breakdown": {
            "platform_cost": {"cost": 12000, "description": "单细胞测序平台费用"},
            "analysis_cost": {"cost": 8000, "description": "数据分析和报告"},
            "literature_support": {"cost": 2000, "description": "AI文献推荐服务"}
        },
        "total_estimated_cost": 22000,
        "budget_match_score": cost_match_score,
        "timeline_estimate": {
            "total": timeline, 
            "sample_prep": "1-2周", 
            "sequencing": "1周",
            "analysis": "2-3周"
        },
        "advantages": solution_overview.get("key_advantages", [
            f"基于{research_focus}的专业化技术方案",
            "AI驱动的研究意图分析和关键词整合",
            "一键直达的精准文献搜索链接",
            "完整的风险评估和缓解策略",
            "个性化的项目实施规划"
        ]),
        "considerations": solution_overview.get("considerations", [
            "样本质量对结果影响较大",
            "需要专业的数据分析能力",
            "建议与技术专家充分沟通"
        ]),
        "suitability_score": solution_overview.get("confidence", 0.88),
        "reason": f"基于AI意图分析的{research_focus}方案"
    }]
    
    # 构建项目解决方案
    project_solution = {
        "overview": f"基于AI意图分析的{integrated_keywords.get('research_focus', '研究')}方案",
        "experimental_design": {
            "approach": f"使用{recommended_platform}进行{requirements.get('researchGoal', '研究')}",
            "methodology": "AI驱动的需求分析 + 精准技术匹配 + 智能文献支持"
        },
        "data_analysis_plan": {
            "research_intent_analysis": "深度分析用户研究意图和目标",
            "literature_support": "生成精准的文献搜索策略",
            "risk_assessment": "全面的项目风险评估和缓解措施"
        },
        "expected_outcomes": {
            "deliverables": [
                "个性化的技术方案推荐",
                "精准的文献搜索链接集合", 
                "完整的风险评估报告",
                "详细的项目实施计划"
            ]
        },
        "publication_strategy": {
            "literature_foundation": "基于AI分析的高质量文献支持",
            "keyword_optimization": "智能生成的研究关键词"
        },
        "collaboration_opportunities": [],
        "upgrade_pathways": {
            "advanced_framework": "可升级至详细版方案框架",
            "real_time_optimization": "可集成实时方案优化"
        }
    }
    
    # 构建风险评估
    risk_data = risk_assessment.get("risk_categories", {})
    technical_risks = []
    for category, risk_info in risk_data.items():
        technical_risks.append({
            "risk": category,
            "probability": risk_info.get("level", "中等"),
            "impact": "中等",
            "mitigation": ". ".join(risk_info.get("mitigation", ["制定应对策略"]))
        })
    
    formatted_risk_assessment = {
        "technical_risks": technical_risks,
        "timeline_risks": [],
        "budget_risks": [],
        "data_risks": [],
        "overall_risk_level": risk_assessment.get("overall_assessment", {}).get("risk_level", "中等"),
        "success_probability": risk_assessment.get("overall_assessment", {}).get("success_probability", "85%")
    }
    
    # 构建最终响应数据
    response_data = {
        "generated_at": framework_result.get("generated_at"),
        "hot_papers": [],  # 可以从文献推荐中提取
        "expanded_keywords": expanded_keywords,
        "literature_results": {"combined_results": []},
        "search_links": search_links,
        "analysis_summary": f"基于AI分析生成的{integrated_keywords.get('research_focus', '研究')}综合方案",
        "search_queries": {
            "primary_query": integrated_keywords.get("primary_query", ""),
            "suggested_queries": integrated_keywords.get("alternative_queries", []),
            "related_topics": integrated_keywords.get("suggested_terms", [])[:3],
            "search_strategy": research_intent.get("research_focus_display", {}),
            "optimization_notes": [
                "基于AI研究意图分析的优化策略",
                "智能关键词整合和精准匹配",
                "多平台搜索链接一键直达"
            ]
        },
        "tech_recommendations": tech_recommendations,
        "project_solution": project_solution,
        "risk_assessment": formatted_risk_assessment
    }
    
    return response_data


@router.post("/ai-driven-literature", response_model=IntelligentRecommendationResponse)
async def get_ai_driven_literature_recommendations(
    request: RequirementRequest,
    ai_service: FullyAIDrivenRecommendationService = Depends(get_fully_ai_driven_service)
):
    """
    获取完全AI驱动的文献推荐
    专门用于实时AI分析、搜索策略生成和文献结果分析
    """
    try:
        import time
        start_time = time.time()
        
        # 转换请求为服务所需格式
        requirements = {
            "speciesType": request.speciesType,
            "experimentType": request.experimentType, 
            "researchGoal": request.researchGoal,
            "sampleType": request.sampleType,
            "sampleCount": request.sampleCount,
            "sampleStatus": request.sampleStatus,
            "processingMethod": request.processingMethod,
            "cellCount": request.cellCount,
            "cellViability": request.cellViability,
            "budgetRange": request.budgetRange,
            "projectDuration": request.projectDuration,
            "urgency": request.urgency,
            "sequencingDepth": request.sequencingDepth,
            "analysisType": request.analysisType,
            "dataAnalysisNeeds": request.dataAnalysisNeeds,
            "cellSorting": request.cellSorting,
            "additionalRequirements": request.additionalRequirements
        }
        
        logger.info(f"开始AI驱动文献推荐，领域: {requirements.get('researchGoal')}")
        
        # 调用完全AI驱动的文献推荐服务
        recommendation = await ai_service.generate_literature_recommendations(requirements)
        
        processing_time = time.time() - start_time
        
        if recommendation.get("success", False):
            logger.info(f"AI驱动文献推荐完成，找到 {len(recommendation.get('hot_papers', []))} 篇热点文献")
            
            return IntelligentRecommendationResponse(
                success=True,
                data=recommendation,
                processing_time=processing_time
            )
        else:
            error_msg = recommendation.get("error", "AI文献推荐服务失败")
            logger.error(f"AI驱动文献推荐失败: {error_msg}")
            raise HTTPException(
                status_code=500,
                detail=error_msg
            )
        
    except Exception as e:
        logger.error(f"AI驱动文献推荐异常: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"AI驱动文献推荐失败: {str(e)}"
        )


@router.post("/keyword-expansion")
async def get_keyword_expansion(
    request: RequirementRequest,
    service: IntelligentRecommendationService = Depends(get_intelligent_recommendation_service)
):
    """获取智能关键词扩展"""
    try:
        requirements = request.dict()
        expanded_keywords = await service._generate_expanded_keywords(requirements)
        
        return {
            "success": True, 
            "data": {
                "expanded_keywords": expanded_keywords,
                "generated_at": requirements.get("generated_at")
            }
        }
        
    except Exception as e:
        logger.error(f"关键词扩展失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"关键词扩展失败: {str(e)}"
        )


@router.post("/hot-papers")
async def get_hot_papers(
    request: RequirementRequest,
    service: IntelligentRecommendationService = Depends(get_intelligent_recommendation_service)
):
    """获取热点文献推荐"""
    try:
        requirements = request.dict()
        hot_papers = service._get_relevant_hot_papers(requirements)
        
        return {
            "success": True,
            "data": {
                "hot_papers": hot_papers,
                "total_count": len(hot_papers)
            }
        }
        
    except Exception as e:
        logger.error(f"获取热点文献失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取热点文献失败: {str(e)}"
        )


@router.post("/tech-recommendations")
async def get_tech_recommendations(
    request: RequirementRequest,
    service: IntelligentRecommendationService = Depends(get_intelligent_recommendation_service)
):
    """获取技术平台推荐"""
    try:
        requirements = request.dict()
        tech_recommendations = service._generate_tech_recommendations(requirements)
        
        return {
            "success": True,
            "data": {
                "tech_recommendations": tech_recommendations,
                "total_count": len(tech_recommendations)
            }
        }
        
    except Exception as e:
        logger.error(f"获取技术推荐失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取技术推荐失败: {str(e)}"
        )


@router.post("/project-solution")
async def get_project_solution(
    request: RequirementRequest,
    service: IntelligentRecommendationService = Depends(get_intelligent_recommendation_service)
):
    """获取项目整体解决方案"""
    try:
        requirements = request.dict()
        project_solution = await service._generate_project_solution(requirements)
        
        return {
            "success": True,
            "data": {
                "project_solution": project_solution
            }
        }
        
    except Exception as e:
        logger.error(f"获取项目方案失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取项目方案失败: {str(e)}"
        )


@router.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "intelligent_recommendation",
        "timestamp": "2024-07-23T00:00:00Z"
    }